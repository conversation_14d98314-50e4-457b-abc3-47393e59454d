# HUST数据转换系统

华中科技大学电源明细表数据转换工具

## 项目概述

本系统采用三层混合架构，结合数据库和Excel直接操作的优势，实现从源Excel文件到目标Excel文件的智能数据转换。

## 项目结构

```
HUST/
├── core/                    # 核心层
│   ├── database/           # 数据库操作
│   │   ├── __init__.py
│   │   ├── connection.py   # 数据库连接管理
│   │   ├── models.py       # 数据模型定义
│   │   └── operations.py   # 数据库操作
│   ├── excel/              # Excel操作
│   │   ├── __init__.py
│   │   ├── reader.py       # Excel读取
│   │   ├── writer.py       # Excel写入
│   │   └── validator.py    # Excel验证
│   └── converter/          # 转换引擎
│       ├── __init__.py
│       ├── base.py         # 基础转换类
│       └── rules.py        # 转换规则定义
├── processors/             # 处理器层
│   ├── __init__.py
│   ├── data_loader.py      # 数据加载器
│   ├── data_transformer.py # 数据转换器
│   ├── data_validator.py   # 数据验证器
│   └── backup_manager.py   # 备份管理器
├── config/                 # 配置层
│   ├── __init__.py
│   ├── settings.py         # 系统配置
│   ├── mapping.py          # 字段映射配置
│   └── rules.py            # 业务规则配置
├── utils/                  # 工具层
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   ├── helpers.py          # 辅助函数
│   └── exceptions.py       # 自定义异常
├── gui/                    # 界面层
│   ├── __init__.py
│   ├── main_window.py      # 主界面
│   └── dialogs.py          # 对话框
├── tests/                  # 测试层
│   ├── __init__.py
│   ├── test_core.py
│   ├── test_processors.py
│   └── test_integration.py
├── backups/                # 备份目录
├── logs/                   # 日志目录
├── main.py                 # 主程序入口
└── README.md               # 项目说明
```

## 核心特性

### 🏗️ 三层混合架构
- **核心层**: 数据库操作、Excel操作、转换引擎
- **处理器层**: 数据加载、转换、验证、备份管理
- **配置层**: 系统配置、字段映射、业务规则

### 💾 数据库支持
- 使用SQLite作为临时数据库
- 支持复杂的数据验证和查询
- 事务支持，确保数据一致性

### 📊 Excel操作
- 支持读取和写入Excel文件
- 保持原有格式和样式
- 自动列宽调整

### 🛡️ 自动备份
- 每次操作前自动创建备份
- 功能化命名规范
- 备份文件管理和清理

### ⚙️ 配置驱动
- 灵活的字段映射配置
- 支持多种转换类型
- 可扩展的业务规则

## 安装和使用

### 环境要求
- Python 3.7+
- pandas
- openpyxl
- sqlite3 (Python内置)

### 安装依赖
```bash
pip install pandas openpyxl
```

### 运行程序

#### 1. 运行基础测试
```bash
cd HUST
python main.py test
```

#### 2. 启动GUI界面（开发中）
```bash
python main.py gui
```

#### 3. 运行数据转换（开发中）
```bash
python main.py convert
```

#### 4. 直接运行测试
```bash
python tests/test_basic.py
```

## 配置说明

### 文件配置 (config/settings.py)
- 源文件和目标文件路径
- 数据库配置
- 日志配置
- 备份配置

### 字段映射 (config/mapping.py)
- 直接映射: 一对一字段映射
- 公式映射: 基于公式的计算
- 查找映射: 基于查找表的转换
- 自定义映射: 自定义函数处理

### 映射类型示例

#### 直接映射
```python
DIRECT_MAPPING = {
    "机组名称": "机组名称",
    "装机容量": "装机容量"
}
```

#### 公式映射
```python
FORMULA_MAPPING = {
    "总装机容量": {
        "type": "formula",
        "formula": "装机容量 * 机组数量",
        "dependencies": ["装机容量", "机组数量"]
    }
}
```

#### 查找映射
```python
LOOKUP_MAPPING = {
    "标准机组类型": {
        "type": "lookup",
        "lookup_table": "machine_type_mapping",
        "key_field": "机组类型"
    }
}
```

## 备份策略

### 命名规范
```
电源明细表手动转HUST_backup_YYYYMMDD_HHMMSS_功能名称.xlsx
```

### 示例
```
电源明细表手动转HUST_backup_20241201_143022_机组名称转换.xlsx
电源明细表手动转HUST_backup_20241201_143055_容量计算.xlsx
```

## 日志系统

- 自动日志记录
- 文件轮转 (10MB, 5个备份文件)
- 多级别日志 (DEBUG, INFO, WARNING, ERROR)
- 进度跟踪

## 错误处理

- 自定义异常体系
- 错误收集和报告
- 优雅的错误恢复
- 详细的错误上下文

## 开发状态

### ✅ 已完成
- [x] 项目架构设计
- [x] 数据库模型和操作
- [x] Excel读写功能
- [x] 备份管理系统
- [x] 日志系统
- [x] 异常处理
- [x] 基础测试框架
- [x] 配置系统

### 🚧 开发中
- [ ] 数据转换引擎
- [ ] 数据验证器
- [ ] GUI界面
- [ ] 完整的测试套件

### 📋 待开发
- [ ] 高级转换规则
- [ ] 性能优化
- [ ] 用户文档
- [ ] 部署脚本

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
