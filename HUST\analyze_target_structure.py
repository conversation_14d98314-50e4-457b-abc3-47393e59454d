"""
分析目标Excel文件结构
"""
import pandas as pd
from pathlib import Path

def analyze_target_file():
    """分析目标文件结构"""
    target_file = '电源明细表手动转HUST.xlsx'

    if not Path(target_file).exists():
        print(f"目标文件不存在: {target_file}")
        return

    print(f"=== 分析目标文件: {target_file} ===")

    try:
        # 使用pandas读取工作表信息
        excel_file = pd.ExcelFile(target_file)
        print(f"工作表列表: {excel_file.sheet_names}")

        # 重点分析"电站表"工作表
        if '电站表' in excel_file.sheet_names:
            print(f"\n=== 电站表工作表分析 ===")

            # 读取电站表数据
            df = pd.read_excel(target_file, sheet_name='电站表', header=None)
            print(f"数据形状: {df.shape}")

            # 显示前几行数据
            print("\n前5行数据:")
            for idx, row in df.head().iterrows():
                print(f"第{idx+1}行: {row.tolist()}")

            # 检查列标题（第一行）
            print("\n列标题分析:")
            if len(df) > 0:
                header_row = df.iloc[0]
                for col_idx, value in enumerate(header_row):
                    col_letter = chr(65 + col_idx) if col_idx < 26 else f"A{chr(65 + col_idx - 26)}"
                    print(f"列{col_letter}({col_idx+1}): {value}")

            # 检查B列和E列的具体内容
            print("\nB列和E列内容分析:")
            if df.shape[1] >= 2:
                print("B列(第2列)前10行:")
                for idx in range(min(10, len(df))):
                    b_value = df.iloc[idx, 1] if df.shape[1] > 1 else None
                    print(f"  第{idx+1}行: {b_value}")

            if df.shape[1] >= 5:
                print("E列(第5列)前10行:")
                for idx in range(min(10, len(df))):
                    e_value = df.iloc[idx, 4] if df.shape[1] > 4 else None
                    print(f"  第{idx+1}行: {e_value}")

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_target_file()
