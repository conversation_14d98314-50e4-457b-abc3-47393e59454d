"""
清理重复电站数据工具
用于清理之前运行时产生的重复风电电站数据
"""
import sys
from pathlib import Path
import pandas as pd
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.station_id_manager import clean_duplicate_stations

def backup_file(file_path):
    """
    创建文件备份
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        str: 备份文件路径
    """
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = file_path.parent / f"{file_path.stem}_备份_{timestamp}{file_path.suffix}"
        
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份: {backup_path}")
        return str(backup_path)
        
    except Exception as e:
        print(f"❌ 创建备份失败: {str(e)}")
        return None

def analyze_station_data(file_path, sheet_name='电站表'):
    """
    分析电站数据，查找重复项
    
    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称
        
    Returns:
        dict: 分析结果
    """
    try:
        if not Path(file_path).exists():
            return {"error": "文件不存在"}
        
        # 读取数据
        data = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        
        if data.empty:
            return {"error": "数据为空"}
        
        if '名称' not in data.columns:
            return {"error": "缺少名称列"}
        
        # 分析数据
        total_stations = len(data)
        unique_names = data['名称'].nunique()
        duplicates = data[data.duplicated(subset=['名称'], keep=False)]
        
        # 统计各类型电站
        station_counts = data['名称'].value_counts()
        
        result = {
            "total_stations": total_stations,
            "unique_names": unique_names,
            "duplicate_count": len(duplicates),
            "station_counts": station_counts.to_dict(),
            "duplicates": duplicates if not duplicates.empty else None
        }
        
        return result
        
    except Exception as e:
        return {"error": f"分析失败: {str(e)}"}

def clean_station_duplicates(file_path, sheet_name='电站表', dry_run=False):
    """
    清理电站重复数据
    
    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称
        dry_run (bool): 是否为试运行（不实际修改文件）
        
    Returns:
        dict: 清理结果
    """
    try:
        if not Path(file_path).exists():
            return {"error": "文件不存在"}
        
        # 分析现有数据
        analysis = analyze_station_data(file_path, sheet_name)
        if "error" in analysis:
            return analysis
        
        print(f"分析结果:")
        print(f"  总电站数: {analysis['total_stations']}")
        print(f"  唯一名称数: {analysis['unique_names']}")
        print(f"  重复记录数: {analysis['duplicate_count']}")
        
        if analysis['duplicate_count'] == 0:
            print("✓ 没有发现重复记录")
            return {"message": "没有重复记录"}
        
        # 显示重复的电站
        print(f"\n重复的电站:")
        for name, count in analysis['station_counts'].items():
            if count > 1:
                print(f"  {name}: {count} 条记录")
        
        if dry_run:
            print("\n这是试运行，不会实际修改文件")
            return {"message": "试运行完成"}
        
        # 创建备份
        backup_path = backup_file(file_path)
        if not backup_path:
            return {"error": "创建备份失败"}
        
        # 读取数据并清理重复项
        data = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        
        # 保留每个名称的第一条记录
        cleaned_data = data.drop_duplicates(subset=['名称'], keep='first')
        
        # 保存清理后的数据
        with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            cleaned_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        removed_count = len(data) - len(cleaned_data)
        
        result = {
            "original_count": len(data),
            "cleaned_count": len(cleaned_data),
            "removed_count": removed_count,
            "backup_path": backup_path
        }
        
        print(f"\n✓ 清理完成:")
        print(f"  原始记录数: {result['original_count']}")
        print(f"  清理后记录数: {result['cleaned_count']}")
        print(f"  删除记录数: {result['removed_count']}")
        print(f"  备份文件: {result['backup_path']}")
        
        return result
        
    except Exception as e:
        return {"error": f"清理失败: {str(e)}"}

def clean_unit_duplicates(file_path, sheet_name='机组表', dry_run=False):
    """
    清理机组重复数据
    
    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称
        dry_run (bool): 是否为试运行
        
    Returns:
        dict: 清理结果
    """
    try:
        if not Path(file_path).exists():
            return {"error": "文件不存在"}
        
        # 读取数据
        data = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        
        if data.empty:
            return {"error": "数据为空"}
        
        if '名称' not in data.columns:
            return {"error": "缺少名称列"}
        
        # 分析重复数据
        total_units = len(data)
        duplicates = data[data.duplicated(subset=['名称'], keep=False)]
        
        print(f"机组表分析结果:")
        print(f"  总机组数: {total_units}")
        print(f"  重复记录数: {len(duplicates)}")
        
        if len(duplicates) == 0:
            print("✓ 没有发现重复的机组记录")
            return {"message": "没有重复记录"}
        
        # 显示重复的机组（按电站ID分组）
        if '电站ID' in data.columns:
            duplicate_by_station = duplicates.groupby('电站ID')['名称'].count()
            print(f"\n按电站ID统计的重复机组:")
            for station_id, count in duplicate_by_station.items():
                print(f"  电站ID {station_id}: {count} 条重复记录")
        
        if dry_run:
            print("\n这是试运行，不会实际修改文件")
            return {"message": "试运行完成"}
        
        # 创建备份
        backup_path = backup_file(file_path)
        if not backup_path:
            return {"error": "创建备份失败"}
        
        # 清理重复项（保留第一条记录）
        cleaned_data = data.drop_duplicates(subset=['名称'], keep='first')
        
        # 保存清理后的数据
        with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            cleaned_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        removed_count = len(data) - len(cleaned_data)
        
        result = {
            "original_count": len(data),
            "cleaned_count": len(cleaned_data),
            "removed_count": removed_count,
            "backup_path": backup_path
        }
        
        print(f"\n✓ 机组清理完成:")
        print(f"  原始记录数: {result['original_count']}")
        print(f"  清理后记录数: {result['cleaned_count']}")
        print(f"  删除记录数: {result['removed_count']}")
        
        return result
        
    except Exception as e:
        return {"error": f"机组清理失败: {str(e)}"}

def main():
    """主函数"""
    print("=" * 70)
    print("重复电站数据清理工具")
    print("=" * 70)
    print("此工具用于清理之前运行时产生的重复风电电站和机组数据")
    print("建议先运行试运行模式查看重复情况，再决定是否清理")
    
    # 获取用户输入
    while True:
        print(f"\n请选择操作:")
        print("1. 分析电站表重复数据")
        print("2. 清理电站表重复数据（试运行）")
        print("3. 清理电站表重复数据（实际执行）")
        print("4. 分析机组表重复数据")
        print("5. 清理机组表重复数据（试运行）")
        print("6. 清理机组表重复数据（实际执行）")
        print("7. 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice == '7':
            print("退出程序")
            break
        
        if choice not in ['1', '2', '3', '4', '5', '6']:
            print("❌ 无效选择，请重新输入")
            continue
        
        # 获取文件路径
        file_path = input("\n请输入Excel文件路径: ").strip().strip('"')
        
        if not file_path:
            print("❌ 文件路径不能为空")
            continue
        
        if not Path(file_path).exists():
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        # 执行相应操作
        if choice == '1':
            print(f"\n分析电站表重复数据...")
            result = analyze_station_data(file_path)
            if "error" in result:
                print(f"❌ {result['error']}")
            else:
                print(f"✓ 分析完成")
                
        elif choice == '2':
            print(f"\n清理电站表重复数据（试运行）...")
            result = clean_station_duplicates(file_path, dry_run=True)
            if "error" in result:
                print(f"❌ {result['error']}")
                
        elif choice == '3':
            confirm = input("\n⚠️  确认要清理电站表重复数据吗？这将修改原文件（会创建备份）(y/N): ").strip().lower()
            if confirm == 'y':
                print(f"\n清理电站表重复数据...")
                result = clean_station_duplicates(file_path, dry_run=False)
                if "error" in result:
                    print(f"❌ {result['error']}")
            else:
                print("操作已取消")
                
        elif choice == '4':
            print(f"\n分析机组表重复数据...")
            result = analyze_station_data(file_path, '机组表')
            if "error" in result:
                print(f"❌ {result['error']}")
            else:
                print(f"✓ 分析完成")
                
        elif choice == '5':
            print(f"\n清理机组表重复数据（试运行）...")
            result = clean_unit_duplicates(file_path, dry_run=True)
            if "error" in result:
                print(f"❌ {result['error']}")
                
        elif choice == '6':
            confirm = input("\n⚠️  确认要清理机组表重复数据吗？这将修改原文件（会创建备份）(y/N): ").strip().lower()
            if confirm == 'y':
                print(f"\n清理机组表重复数据...")
                result = clean_unit_duplicates(file_path, dry_run=False)
                if "error" in result:
                    print(f"❌ {result['error']}")
            else:
                print("操作已取消")

if __name__ == "__main__":
    main()
