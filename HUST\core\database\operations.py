"""
数据库操作模块
提供对各个数据表的CRUD操作
"""
import json
import logging
from typing import List, Optional, Dict, Any
from .connection import db_manager
from .models import SourceData, ConversionRule, ValidationRule, ConversionLog

logger = logging.getLogger(__name__)

class SourceDataDAO:
    """源数据数据访问对象"""
    
    @staticmethod
    def insert(source_data: SourceData) -> int:
        """插入源数据记录"""
        sql = """
        INSERT INTO source_data (raw_data, sheet_name, row_number)
        VALUES (?, ?, ?)
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (
                source_data.raw_data,
                source_data.sheet_name,
                source_data.row_number
            ))
            conn.commit()
            return cursor.lastrowid
    
    @staticmethod
    def insert_batch(source_data_list: List[SourceData]) -> int:
        """批量插入源数据记录"""
        sql = """
        INSERT INTO source_data (raw_data, sheet_name, row_number)
        VALUES (?, ?, ?)
        """
        data_tuples = [
            (sd.raw_data, sd.sheet_name, sd.row_number)
            for sd in source_data_list
        ]
        
        with db_manager.get_connection() as conn:
            cursor = conn.executemany(sql, data_tuples)
            conn.commit()
            return cursor.rowcount
    
    @staticmethod
    def get_by_sheet(sheet_name: str) -> List[SourceData]:
        """根据工作表名获取源数据"""
        sql = """
        SELECT id, raw_data, sheet_name, row_number, created_at
        FROM source_data
        WHERE sheet_name = ?
        ORDER BY row_number
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (sheet_name,))
            rows = cursor.fetchall()
            return [SourceData(
                id=row['id'],
                raw_data=row['raw_data'],
                sheet_name=row['sheet_name'],
                row_number=row['row_number'],
                created_at=row['created_at']
            ) for row in rows]
    
    @staticmethod
    def clear_by_sheet(sheet_name: str) -> int:
        """清空指定工作表的数据"""
        sql = "DELETE FROM source_data WHERE sheet_name = ?"
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (sheet_name,))
            conn.commit()
            return cursor.rowcount
    
    @staticmethod
    def clear_all() -> int:
        """清空所有源数据"""
        sql = "DELETE FROM source_data"
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql)
            conn.commit()
            return cursor.rowcount

class ConversionRuleDAO:
    """转换规则数据访问对象"""
    
    @staticmethod
    def insert(rule: ConversionRule) -> int:
        """插入转换规则"""
        sql = """
        INSERT INTO conversion_rules 
        (rule_name, source_field, target_field, conversion_type, conversion_params, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (
                rule.rule_name,
                rule.source_field,
                rule.target_field,
                rule.conversion_type,
                rule.conversion_params,
                rule.is_active
            ))
            conn.commit()
            return cursor.lastrowid
    
    @staticmethod
    def get_active_rules() -> List[ConversionRule]:
        """获取所有激活的转换规则"""
        sql = """
        SELECT id, rule_name, source_field, target_field, conversion_type, 
               conversion_params, is_active, created_at
        FROM conversion_rules
        WHERE is_active = 1
        ORDER BY id
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql)
            rows = cursor.fetchall()
            return [ConversionRule(
                id=row['id'],
                rule_name=row['rule_name'],
                source_field=row['source_field'],
                target_field=row['target_field'],
                conversion_type=row['conversion_type'],
                conversion_params=row['conversion_params'],
                is_active=bool(row['is_active']),
                created_at=row['created_at']
            ) for row in rows]
    
    @staticmethod
    def get_by_name(rule_name: str) -> Optional[ConversionRule]:
        """根据规则名称获取转换规则"""
        sql = """
        SELECT id, rule_name, source_field, target_field, conversion_type, 
               conversion_params, is_active, created_at
        FROM conversion_rules
        WHERE rule_name = ?
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (rule_name,))
            row = cursor.fetchone()
            if row:
                return ConversionRule(
                    id=row['id'],
                    rule_name=row['rule_name'],
                    source_field=row['source_field'],
                    target_field=row['target_field'],
                    conversion_type=row['conversion_type'],
                    conversion_params=row['conversion_params'],
                    is_active=bool(row['is_active']),
                    created_at=row['created_at']
                )
            return None
    
    @staticmethod
    def update(rule: ConversionRule) -> bool:
        """更新转换规则"""
        sql = """
        UPDATE conversion_rules 
        SET source_field = ?, target_field = ?, conversion_type = ?, 
            conversion_params = ?, is_active = ?
        WHERE rule_name = ?
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (
                rule.source_field,
                rule.target_field,
                rule.conversion_type,
                rule.conversion_params,
                rule.is_active,
                rule.rule_name
            ))
            conn.commit()
            return cursor.rowcount > 0

class ValidationRuleDAO:
    """验证规则数据访问对象"""
    
    @staticmethod
    def insert(rule: ValidationRule) -> int:
        """插入验证规则"""
        sql = """
        INSERT INTO validation_rules 
        (rule_name, field_name, rule_type, rule_params, error_message, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (
                rule.rule_name,
                rule.field_name,
                rule.rule_type,
                rule.rule_params,
                rule.error_message,
                rule.is_active
            ))
            conn.commit()
            return cursor.lastrowid
    
    @staticmethod
    def get_active_rules() -> List[ValidationRule]:
        """获取所有激活的验证规则"""
        sql = """
        SELECT id, rule_name, field_name, rule_type, rule_params, 
               error_message, is_active, created_at
        FROM validation_rules
        WHERE is_active = 1
        ORDER BY id
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql)
            rows = cursor.fetchall()
            return [ValidationRule(
                id=row['id'],
                rule_name=row['rule_name'],
                field_name=row['field_name'],
                rule_type=row['rule_type'],
                rule_params=row['rule_params'],
                error_message=row['error_message'],
                is_active=bool(row['is_active']),
                created_at=row['created_at']
            ) for row in rows]

class ConversionLogDAO:
    """转换日志数据访问对象"""
    
    @staticmethod
    def insert(log: ConversionLog) -> int:
        """插入转换日志"""
        sql = """
        INSERT INTO conversion_logs 
        (operation_type, source_file, target_file, backup_file, status, 
         error_message, records_processed)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (
                log.operation_type,
                log.source_file,
                log.target_file,
                log.backup_file,
                log.status,
                log.error_message,
                log.records_processed
            ))
            conn.commit()
            return cursor.lastrowid
    
    @staticmethod
    def get_recent_logs(limit: int = 100) -> List[ConversionLog]:
        """获取最近的转换日志"""
        sql = """
        SELECT id, operation_type, source_file, target_file, backup_file, 
               status, error_message, records_processed, created_at
        FROM conversion_logs
        ORDER BY created_at DESC
        LIMIT ?
        """
        with db_manager.get_connection() as conn:
            cursor = conn.execute(sql, (limit,))
            rows = cursor.fetchall()
            return [ConversionLog(
                id=row['id'],
                operation_type=row['operation_type'],
                source_file=row['source_file'],
                target_file=row['target_file'],
                backup_file=row['backup_file'],
                status=row['status'],
                error_message=row['error_message'],
                records_processed=row['records_processed'],
                created_at=row['created_at']
            ) for row in rows]
