"""
Excel写入模块
"""
import pandas as pd
import logging
from typing import Dict, List, Any, Optional
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

logger = logging.getLogger(__name__)

class ExcelWriter:
    """Excel文件写入器"""
    
    def __init__(self, file_path: str, template_path: Optional[str] = None):
        """
        初始化Excel写入器
        
        Args:
            file_path (str): 目标Excel文件路径
            template_path (Optional[str]): 模板文件路径
        """
        self.file_path = file_path
        self.template_path = template_path
        self.workbook = None
        
    def load_or_create_workbook(self) -> bool:
        """
        加载现有工作簿或创建新工作簿
        
        Returns:
            bool: 是否成功
        """
        try:
            if self.template_path:
                # 从模板创建
                self.workbook = load_workbook(self.template_path)
                logger.info(f"从模板加载工作簿: {self.template_path}")
            else:
                try:
                    # 尝试加载现有文件
                    self.workbook = load_workbook(self.file_path)
                    logger.info(f"加载现有工作簿: {self.file_path}")
                except FileNotFoundError:
                    # 创建新工作簿
                    self.workbook = Workbook()
                    logger.info("创建新工作簿")
            
            return True
        except Exception as e:
            logger.error(f"加载或创建工作簿失败: {str(e)}")
            return False
    
    def write_sheet_data(self, sheet_name: str, data: List[Dict[str, Any]], 
                        start_row: int = 1, clear_existing: bool = True) -> bool:
        """
        写入数据到指定工作表
        
        Args:
            sheet_name (str): 工作表名称
            data (List[Dict[str, Any]]): 要写入的数据
            start_row (int): 起始行号（从1开始）
            clear_existing (bool): 是否清空现有数据
            
        Returns:
            bool: 是否写入成功
        """
        if not self.workbook:
            if not self.load_or_create_workbook():
                return False
        
        try:
            # 获取或创建工作表
            if sheet_name in self.workbook.sheetnames:
                ws = self.workbook[sheet_name]
                if clear_existing:
                    # 清空现有数据（保留第一行标题）
                    for row in ws.iter_rows(min_row=start_row + 1):
                        for cell in row:
                            cell.value = None
            else:
                ws = self.workbook.create_sheet(sheet_name)
            
            if not data:
                logger.warning(f"没有数据要写入到工作表 {sheet_name}")
                return True
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 写入标题行（如果是从第1行开始）
            if start_row == 1:
                for col_idx, column_name in enumerate(df.columns, 1):
                    cell = ws.cell(row=1, column=col_idx)
                    cell.value = column_name
                    # 设置标题样式
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center')
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # 写入数据行
            data_start_row = start_row + (1 if start_row == 1 else 0)
            for row_idx, (_, row_data) in enumerate(df.iterrows(), data_start_row):
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.value = value
                    # 设置数据样式
                    cell.alignment = Alignment(horizontal='left', vertical='center')
            
            # 设置列宽自适应
            self._adjust_column_width(ws)
            
            logger.info(f"成功写入 {len(data)} 行数据到工作表 {sheet_name}")
            return True
            
        except Exception as e:
            logger.error(f"写入数据到工作表 {sheet_name} 失败: {str(e)}")
            return False
    
    def append_sheet_data(self, sheet_name: str, data: List[Dict[str, Any]]) -> bool:
        """
        追加数据到指定工作表
        
        Args:
            sheet_name (str): 工作表名称
            data (List[Dict[str, Any]]): 要追加的数据
            
        Returns:
            bool: 是否追加成功
        """
        if not self.workbook:
            if not self.load_or_create_workbook():
                return False
        
        try:
            if sheet_name not in self.workbook.sheetnames:
                logger.error(f"工作表 {sheet_name} 不存在")
                return False
            
            ws = self.workbook[sheet_name]
            
            # 找到最后一行
            last_row = ws.max_row
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 追加数据行
            for row_idx, (_, row_data) in enumerate(df.iterrows(), last_row + 1):
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.value = value
                    cell.alignment = Alignment(horizontal='left', vertical='center')
            
            logger.info(f"成功追加 {len(data)} 行数据到工作表 {sheet_name}")
            return True
            
        except Exception as e:
            logger.error(f"追加数据到工作表 {sheet_name} 失败: {str(e)}")
            return False
    
    def update_cell(self, sheet_name: str, row: int, column: int, value: Any) -> bool:
        """
        更新指定单元格的值
        
        Args:
            sheet_name (str): 工作表名称
            row (int): 行号（从1开始）
            column (int): 列号（从1开始）
            value (Any): 新值
            
        Returns:
            bool: 是否更新成功
        """
        if not self.workbook:
            if not self.load_or_create_workbook():
                return False
        
        try:
            if sheet_name not in self.workbook.sheetnames:
                logger.error(f"工作表 {sheet_name} 不存在")
                return False
            
            ws = self.workbook[sheet_name]
            ws.cell(row=row, column=column).value = value
            
            logger.debug(f"更新单元格 {sheet_name}[{row},{column}] = {value}")
            return True
            
        except Exception as e:
            logger.error(f"更新单元格失败: {str(e)}")
            return False
    
    def copy_sheet_format(self, source_sheet_name: str, target_sheet_name: str) -> bool:
        """
        复制工作表格式
        
        Args:
            source_sheet_name (str): 源工作表名称
            target_sheet_name (str): 目标工作表名称
            
        Returns:
            bool: 是否复制成功
        """
        if not self.workbook:
            return False
        
        try:
            if source_sheet_name not in self.workbook.sheetnames:
                logger.error(f"源工作表 {source_sheet_name} 不存在")
                return False
            
            if target_sheet_name not in self.workbook.sheetnames:
                logger.error(f"目标工作表 {target_sheet_name} 不存在")
                return False
            
            source_ws = self.workbook[source_sheet_name]
            target_ws = self.workbook[target_sheet_name]
            
            # 复制列宽
            for col in source_ws.columns:
                col_letter = col[0].column_letter
                if source_ws.column_dimensions[col_letter].width:
                    target_ws.column_dimensions[col_letter].width = source_ws.column_dimensions[col_letter].width
            
            # 复制行高
            for row in source_ws.rows:
                row_num = row[0].row
                if source_ws.row_dimensions[row_num].height:
                    target_ws.row_dimensions[row_num].height = source_ws.row_dimensions[row_num].height
            
            logger.info(f"成功复制工作表格式从 {source_sheet_name} 到 {target_sheet_name}")
            return True
            
        except Exception as e:
            logger.error(f"复制工作表格式失败: {str(e)}")
            return False
    
    def _adjust_column_width(self, worksheet):
        """自动调整列宽"""
        try:
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    if cell.value:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length
                
                # 设置列宽，最小8，最大50
                adjusted_width = min(max(max_length + 2, 8), 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
                
        except Exception as e:
            logger.warning(f"调整列宽失败: {str(e)}")
    
    def save(self) -> bool:
        """
        保存工作簿
        
        Returns:
            bool: 是否保存成功
        """
        if not self.workbook:
            logger.error("没有工作簿可保存")
            return False
        
        try:
            self.workbook.save(self.file_path)
            logger.info(f"成功保存Excel文件: {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"保存Excel文件失败: {str(e)}")
            return False
    
    def update_specific_columns(self, sheet_name: str, data: List[Dict[str, Any]],
                               column_mapping: Dict[str, int], start_row: int = 2) -> bool:
        """
        更新指定列的数据，保持其他列不变

        Args:
            sheet_name (str): 工作表名称
            data (List[Dict[str, Any]]): 要写入的数据
            column_mapping (Dict[str, int]): 字段到列号的映射，如 {'名称': 2, '类型': 5}
            start_row (int): 起始行号（从1开始，默认从第2行开始，保留标题行）

        Returns:
            bool: 是否更新成功
        """
        if not self.workbook:
            if not self.load_or_create_workbook():
                return False

        try:
            if sheet_name not in self.workbook.sheetnames:
                logger.error(f"工作表 {sheet_name} 不存在")
                return False

            ws = self.workbook[sheet_name]

            if not data:
                logger.warning(f"没有数据要写入到工作表 {sheet_name}")
                return True

            # 写入数据到指定列
            for row_idx, record in enumerate(data, start_row):
                for field_name, col_num in column_mapping.items():
                    if field_name in record:
                        cell = ws.cell(row=row_idx, column=col_num)
                        cell.value = record[field_name]
                        # 设置数据样式
                        cell.alignment = Alignment(horizontal='left', vertical='center')

            logger.info(f"成功更新 {len(data)} 行数据到工作表 {sheet_name} 的指定列")
            return True

        except Exception as e:
            logger.error(f"更新指定列数据失败: {str(e)}")
            return False

    def close(self):
        """关闭工作簿"""
        if self.workbook:
            self.workbook.close()
            self.workbook = None
            logger.info("Excel工作簿已关闭")
