# 以下每次添加电站表sheet时，都根据已有电站id往后面顺延

# 1.1 HUST电站表sheet调峰气电320类型需求：

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（机组类型）= QD_9F
提取字段：项目名称

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称）、E列（类型=320）、检修场地 =1	备用Rmax = 1 	储能比率	储能效率	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0
B列命名规则：QD_1_9F_项目名称1, QD_2_9F_项目名称2, QD_3_9F_项目名称3

# 1.2 HUST机组表sheet调峰气电类型需求：

数据流转：电站表(E列=320) → 提取名称+电站ID → 匹配源文件项目名称 → 提取机组信息 →
组合机组名称 → 匹配特性表 → 生成机组表记录

### 实现步骤

**第一步：机组数据转换器**

* 从电站表提取类型=320的记录
* 匹配源文件获取机组信息
* 生成机组名称和基础数据

**第二步：特性数据匹配器**

* 根据机组类型匹配特性表
* 提取技术出力和特性ID

**第三步：机组表写入器**

* 组合所有数据
* 写入目标机组表

## 输入：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
筛选条件：E列（类型）= 320
提取字段：名称，电站ID
拿提取到的名称去和文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1的项目名称列匹配，匹配上以后提取机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0.3）
I列（储能库容 = 0）、检修天数 = 30、

特性ID = 5
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 3500、变电投资 =0	运维费率 = 0.04	运行费 =0.02	燃料单耗 = 220	燃料单价 = 1654	上网电价 =0.585	汛期电价 =0.585	爬坡率 = 0.05	功频系数 = 0	惯性常数 =0	强迫停运 =0.05

# 2.1 HUST电站表sheet燃气类型需求：

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（机组类型）= QD_RD
提取字段：项目名称

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称）、E列（类型=325）、检修场地 =3	备用Rmax = 0.3 	储能比率	储能效率	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0
B列命名规则：QD_RD_项目名称1, QD_RD_项目名称2, QD_RD_项目名称3

# 2.2 HUST机组表sheet燃气类型需求：

数据流转：电站表(E列=325) → 提取名称+电站ID → 匹配源文件项目名称 → 提取机组信息 →
组合机组名称 → 匹配特性表 → 生成机组表记录

### 实现步骤

**第一步：机组数据转换器**

* 从电站表提取类型=325的记录
* 匹配源文件获取机组信息
* 生成机组名称和基础数据

**第二步：特性数据匹配器**

* 根据机组类型匹配特性表
* 提取技术出力和特性ID

**第三步：机组表写入器**

* 组合所有数据
* 写入目标机组表

## 输入：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
筛选条件：E列（类型）= 325
提取字段：名称，电站ID
拿提取到的名称去和文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1的项目名称列匹配，匹配上以后提取机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0.3）
I列（储能库容 = 0）、检修天数 = 30、

特性ID =7
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 3500、变电投资 =0	运维费率 = 0.04	运行费 =0.02	燃料单耗 = 220	燃料单价 = 1654	上网电价 =0.585	汛期电价 =0.585	爬坡率 = 0.05	功频系数 = 0	惯性常数 =0	强迫停运 =0.05

# 3.1 HUST电站表sheet核电360类型需求：

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（机组类型）= HD
提取字段：项目名称

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称）、E列（类型= 360）、检修场地 =1	备用Rmax = 0 	储能比率	储能效率	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0,期望电量 =7000
B列命名规则：HD_1_项目名称1, HD_2_项目名称2, HD_3_项目名称3

# 3.2 HUST机组表sheet核电类型需求：

数据流转：电站表(E列= 360) → 提取名称+电站ID → 匹配源文件项目名称 → 提取机组信息 →
组合机组名称 → 匹配特性表 → 生成机组表记录

### 实现步骤

**第一步：机组数据转换器**

* 从电站表提取类型= 360的记录
* 匹配源文件获取机组信息
* 生成机组名称和基础数据

**第二步：特性数据匹配器**

* 根据机组类型匹配特性表
* 提取技术出力和特性ID

**第三步：机组表写入器**

* 组合所有数据
* 写入目标机组表

## 输入：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
筛选条件：E列（类型）= 360
提取字段：名称，电站ID
拿提取到的名称去和文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1的项目名称列匹配，匹配上以后提取机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 1）
I列（储能库容 = 0）、检修天数 = 30、

特性ID = 1
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 20000、变电投资 =0	运维费率 = 0.04	运行费 =0.025	燃料单耗 = 0.081	燃料单价 = 0.4	上网电价 =0.43	汛期电价 =0.43	爬坡率 = 0.01	功频系数 = 0	惯性常数 =0	强迫停运 = 0.05

# 4.1 HUST电站表sheet中调水电371类型需求：

只有一行，且是写死的

## 输入：

无

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称 = 中调水电）、E列（类型= 371）、检修场地 = 0	备用Rmax = 0.1 	储能比率	储能效率	储能损耗	期望电量 = 1	最小电量 = 1	电站约束	流域ID	优化空间列都为0,期望电量 = 1
B列命名规则：HD_1_项目名称1, HD_2_项目名称2, HD_3_项目名称3

# 4.2 HUST机组表sheet中调水电371类型需求：

直接从电源明细表读取数据，因为电站表里只有一行，只需要从电站表里读出中调水电的电站ID即可

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（类型）= SD_KT
提取字段：项目名称，机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，
将提取到的名称与机组序列组合后输出给机组表的B列，如新丰江1，新丰江2

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID，从电站表里读出中调水电的电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0.5）
I列（储能库容 = 0）、检修天数 = 30、

特性ID = 1
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 10000、变电投资 =0	运维费率 = 0.01	运行费 =0.001	燃料单耗 = 0	燃料单价 = 0	上网电价 =0.3296	汛期电价 = 0.3296	爬坡率 = 0.75	功频系数 = 0	惯性常数 =0	强迫停运 = 0.05

# 5.1 HUST电站表sheet抽蓄380类型需求：

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（机组类型）= XN
提取字段：项目名称

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称）、E列（类型=380）、检修场地 =1	备用Rmax = 0.1 	储能比率 = 1.1	储能效率 = 0.75	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0
B列命名规则：XN_1_项目名称1, XN_2_项目名称2, XN_3_项目名称3

# 5.2 HUST机组表sheet抽蓄380类型需求：

数据流转：电站表(E列=380) → 提取名称+电站ID → 匹配源文件项目名称 → 提取机组信息 →
组合机组名称 → 匹配特性表 → 生成机组表记录

### 实现步骤

**第一步：机组数据转换器**

* 从电站表提取类型=320的记录
* 匹配源文件获取机组信息
* 生成机组名称和基础数据

**第二步：特性数据匹配器**

* 根据机组类型匹配特性表
* 提取技术出力和特性ID

**第三步：机组表写入器**

* 组合所有数据
* 写入目标机组表

## 输入：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
筛选条件：E列（类型）= 380
提取字段：名称，电站ID
拿提取到的名称去和文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1的项目名称列匹配，匹配上以后提取机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0）
I列（储能库容 = 0）、检修天数 = 30、

特性ID = 100
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 6000、变电投资 =0	运维费率 = 0.024	运行费 =0.006	燃料单耗 = 0.345	燃料单价 = 0	上网电价 =0.4	汛期电价 =0.4	爬坡率 = 0.05	功频系数 = 0	惯性常数 =0	强迫停运 =0.05

# 6.1 HUST电站表sheet陆上风电390类型需求：

只有一行，且是写死的

## 输入：

无

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称 = 陆上风电）、E列（类型= 390）、检修场地 = 0	备用Rmax = 0.1 	储能比率 =1	储能效率	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0

# 6.2 HUST机组表sheet陆上风电390类型需求：

直接从电源明细表读取数据，因为电站表里只有一行，只需要从电站表里读出陆上风电的电站ID即可

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（类型）= FD
提取字段：项目名称，机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，
将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID，从电站表里读出陆上风电的电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0）
I列（储能库容 = 0）、检修天数 = 0、

特性ID = 0
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 4000、变电投资 =0	运维费率 = 0.05	运行费 =0	燃料单耗 = 0.45	燃料单价 = 0.45	上网电价 =0.45	汛期电价 = 0.45	爬坡率 = 0	功频系数 = 0	惯性常数 =0	强迫停运 = 0.05

# 7.1 HUST电站表sheet海上风电392类型需求：

只有一行，且是写死的

## 输入：

无

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称 = 海上风电）、E列（类型= 392）、检修场地 = 0	备用Rmax = 0.1 	储能比率 =1	储能效率	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0

# 7.2 HUST机组表sheet海上风电392类型需求：

直接从电源明细表读取数据，因为电站表里只有一行，只需要从电站表里读出海上风电的电站ID即可

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（类型）= FD_JH,FD_SH
提取字段：项目名称，机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，
将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID，从电站表里读出陆上风电的电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0）
I列（储能库容 = 0）、检修天数 = 0、

特性ID = 0
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 17000、变电投资 =0	运维费率 = 0.05	运行费 =0	燃料单耗 = 0	燃料单价 = 0	上网电价 =0.55	汛期电价 = 0.55	爬坡率 = 0	功频系数 = 0	惯性常数 =0	强迫停运 = 0.05

# 8.1 HUST电站表sheet光伏391类型需求：

只有一行，且是写死的

## 输入：

无

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：电站表
目标列：B列（名称 = 光伏）、E列（类型= 391）、检修场地 = 0	备用Rmax = 0 	储能比率 =1	储能效率	储能损耗	期望电量	最小电量	电站约束	流域ID	优化空间列都为0

# 8.2 HUST机组表sheet光伏391类型需求：（暂无数据，先不管）

直接从电源明细表读取数据，因为电站表里只有一行，只需要从电站表里读出光伏的电站ID即可

## 输入：

文件：HUST\01 电源明细表-模板.xlsx
工作表：Sheet1
筛选条件：E列（类型）= FD_JH,FD_SH
提取字段：项目名称，机组序列字段、E列（机组类型）字段、投产时间字段、退役时间字段，
将提取到的名称输出给机组表的B列

## 输出：

文件：HUST\电源明细表手动转HUST.xlsx
工作表：机组表
目标列：B列（名称）、D列（电站ID，从电站表里读出陆上风电的电站ID）、E列（单机容量）、F列（台数 = 1）、G列（类型 =0）
H列（技术出力 = 0）
I列（储能库容 = 0）、检修天数 = 0、

特性ID = 0
投产年月列逻辑：

* 当投产年月为 `"0"` 时，投产进度也为 `0`
* 当投产年月不为 `"0"` 时，投产进度为 `101`

### 具体场景

1. **投产时间早于2025/6/12** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

1. **投产时间晚于2025/6/12** ：

* 投产年月 = `"YYYYMM"` (如 `"202506"`)
* 投产进度 = `101`

1. **投产时间为空或无效** ：

* 投产年月 = `"0"`
* 投产进度 = `0`

退役年月 = 0、退役进度 = 0、动态投资 = 17000、变电投资 =0	运维费率 = 0.05	运行费 =0	燃料单耗 = 0	燃料单价 = 0	上网电价 =0.55	汛期电价 = 0.55	爬坡率 = 0	功频系数 = 0	惯性常数 =0	强迫停运 = 0.05
