def station_get_cols(data, cols_name, type_val):
    """添加新列并设置默认值"""
    if type_val == 'QD_9F':
        for col_name in cols_name:
            if col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax':
                data[col_name] = 1
            elif col_name == '类型':
                data[col_name] = 320
            else:
                data[col_name] = 0
    elif type_val == 'QD_RD':
        for col_name in cols_name:
            if col_name == '有效性' or col_name == '节点ID':
                data[col_name] = 1
            elif col_name == '类型':
                data[col_name] = 325
            elif col_name == '检修场地':
                data[col_name] = 3
            elif col_name == '备用Rmax':
                data[col_name] = 0.3
            else:
                data[col_name] = 0
    elif type_val == 'HD':
        for col_name in cols_name:
            if col_name == '有效性' or col_name == '节点ID':
                data[col_name] = 1
            elif col_name == '类型':
                data[col_name] = 360
            elif col_name == '检修场地':
                data[col_name] = 1
            elif col_name == '备用Rmax':
                data[col_name] = 0
            elif col_name == '期望电量':
                data[col_name] = 7000
            else:
                data[col_name] = 0
    elif type_val == 'XN':
        for col_name in cols_name:
            if col_name == '有效性' or col_name == '节点ID':
                data[col_name] = 1
            elif col_name == '类型':
                data[col_name] = 380
            elif col_name == '检修场地':
                data[col_name] = 1
            elif col_name == '备用Rmax':
                data[col_name] = 0.1
            elif col_name == '储能比率':
                data[col_name] = 1.1
            elif col_name == '储能效率':
                data[col_name] = 0.75
            else:
                data[col_name] = 0
    return data
