import pandas as pd

def unit_get_cols(data, cols_name, type_val):
    """添加新列并设置默认值"""
    # 重命名
    data.rename(columns={'机组容量': '单机容量', '投产时间': '投产年月'}, inplace=True)

    # 赋值
    if type_val == 'QD_9F':
        for col_name in cols_name:
            if col_name == '动态投资':
                data[col_name] = 3500
            elif col_name == '燃料单价':
                data[col_name] = 1654
            elif col_name == '燃料单耗':
                data[col_name] = 220
            elif col_name == '检修天数':
                data[col_name] = 30
            elif col_name == '特性ID':
                data[col_name] = 5
            elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
                data[col_name] = 1
            elif col_name == '上网电价' or col_name == '汛期电价':
                data[col_name] = 0.585
            elif col_name == '技术出力':
                data[col_name] = 0.3
            elif col_name == '爬坡率' or col_name == '强迫停运':
                data[col_name] = 0.05
            elif col_name == '运维费率':
                data[col_name] = 0.04
            elif col_name == '运行费':
                data[col_name] = 0.02
            elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
                data[col_name] = 0
    elif type_val == 'QD_RD':
        for col_name in cols_name:
            if col_name == '动态投资':
                data[col_name] = 3500
            elif col_name == '燃料单价':
                data[col_name] = 1654
            elif col_name == '燃料单耗':
                data[col_name] = 220
            elif col_name == '检修天数':
                data[col_name] = 30
            elif col_name == '特性ID':
                data[col_name] = 7
            elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
                data[col_name] = 1
            elif col_name == '上网电价' or col_name == '汛期电价':
                data[col_name] = 0.585
            elif col_name == '技术出力':
                data[col_name] = 0.3
            elif col_name == '爬坡率' or col_name == '强迫停运':
                data[col_name] = 0.05
            elif col_name == '运维费率':
                data[col_name] = 0.04
            elif col_name == '运行费':
                data[col_name] = 0.02
            elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
                data[col_name] = 0
    elif type_val == 'HD':
        for col_name in cols_name:
            if col_name == '动态投资':
                data[col_name] = 20000
            elif col_name == '燃料单价':
                data[col_name] = 0.4
            elif col_name == '燃料单耗':
                data[col_name] = 0.081
            elif col_name == '检修天数':
                data[col_name] = 30
            elif col_name == '特性ID':
                data[col_name] = 1
            elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
                data[col_name] = 1
            elif col_name == '上网电价' or col_name == '汛期电价':
                data[col_name] = 0.43
            elif col_name == '技术出力':
                data[col_name] = 1
            elif col_name == '爬坡率':
                data[col_name] = 0.01
            elif col_name == '运维费率':
                data[col_name] = 0.04
            elif col_name == '运行费':
                data[col_name] = 0.025
            elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
                data[col_name] = 0
            elif col_name == '强迫停运':
                data[col_name] = 0.05
    elif type_val == 'XN':
        for col_name in cols_name:
            if col_name == '动态投资':
                data[col_name] = 6000
            elif col_name == '燃料单价':
                data[col_name] = 0
            elif col_name == '燃料单耗':
                data[col_name] = 0.345
            elif col_name == '检修天数':
                data[col_name] = 30
            elif col_name == '特性ID':
                data[col_name] = 100
            elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
                data[col_name] = 1
            elif col_name == '上网电价' or col_name == '汛期电价':
                data[col_name] = 0.4
            elif col_name == '技术出力':
                data[col_name] = 0
            elif col_name == '爬坡率' or col_name == '强迫停运':
                data[col_name] = 0.05
            elif col_name == '运维费率':
                data[col_name] = 0.024
            elif col_name == '运行费':
                data[col_name] = 0.006
            elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
                data[col_name] = 0



    # 处理投产年月列
    # 将所有能转换为日期的元素转换为datetime类型
    data['投产年月'] = pd.to_datetime(data['投产年月'], errors='coerce')

    # 提取年月信息并转换为整数
    year_month_series = data['投产年月'].dt.strftime('%Y%m')

    # 将年月信息更新到投产年月列
    data['投产年月'] = year_month_series

    # 设置投产进度为101的条件
    mask = (data['投产年月'].notna()) & (data['投产年月'] >= '202506')
    data.loc[mask, '投产进度'] = 101

    cols_name = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                 '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                 '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                 '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']

    return data[cols_name]
