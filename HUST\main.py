"""
HUST数据转换系统主程序
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger, get_main_logger
from config.settings import FILES, LOGGING
from core.database.connection import db_manager

def initialize_system():
    """初始化系统"""
    try:
        # 初始化日志
        log_file = project_root / FILES['log_file']
        logger = init_main_logger(
            log_file=str(log_file),
            level=LOGGING['level'],
            format_string=LOGGING['format'],
            max_bytes=LOGGING['max_bytes'],
            backup_count=LOGGING['backup_count']
        )
        
        logger.info("=" * 50)
        logger.info("HUST数据转换系统启动")
        logger.info("=" * 50)
        
        # 初始化数据库
        logger.info("初始化数据库...")
        db_manager.initialize_database()
        logger.info("数据库初始化完成")
        
        # 检查必要文件
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        if not source_file.exists():
            logger.warning(f"源文件不存在: {source_file}")
        else:
            logger.info(f"找到源文件: {source_file}")
        
        if not target_file.exists():
            logger.warning(f"目标文件不存在: {target_file}")
        else:
            logger.info(f"找到目标文件: {target_file}")
        
        logger.info("系统初始化完成")
        return True
        
    except Exception as e:
        print(f"系统初始化失败: {str(e)}")
        return False

def main():
    """主函数"""
    if not initialize_system():
        sys.exit(1)
    
    logger = get_main_logger()
    
    try:
        # 这里可以添加命令行参数处理
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()
            
            if command == "test":
                run_tests()
            elif command == "gui":
                run_gui()
            elif command == "convert":
                run_conversion()
            else:
                logger.error(f"未知命令: {command}")
                show_help()
        else:
            # 默认启动GUI
            run_gui()
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        sys.exit(1)
    finally:
        logger.info("程序结束")

def run_tests():
    """运行测试"""
    logger = get_main_logger()
    logger.info("运行系统测试...")
    
    try:
        from tests.test_basic import run_basic_tests
        run_basic_tests()
        logger.info("测试完成")
    except ImportError:
        logger.warning("测试模块未找到")
    except Exception as e:
        logger.error(f"测试运行失败: {str(e)}")

def run_gui():
    """启动GUI界面"""
    logger = get_main_logger()
    logger.info("启动GUI界面...")
    
    try:
        # TODO: 实现GUI界面
        logger.info("GUI界面启动成功")
        print("GUI界面尚未实现，请使用命令行模式")
    except Exception as e:
        logger.error(f"GUI启动失败: {str(e)}")

def run_conversion():
    """运行数据转换"""
    logger = get_main_logger()
    logger.info("开始数据转换...")
    
    try:
        # TODO: 实现数据转换逻辑
        logger.info("数据转换完成")
        print("数据转换功能尚未实现")
    except Exception as e:
        logger.error(f"数据转换失败: {str(e)}")

def show_help():
    """显示帮助信息"""
    help_text = """
HUST数据转换系统

用法:
    python main.py [命令]

命令:
    gui      启动图形界面 (默认)
    convert  运行数据转换
    test     运行系统测试
    help     显示此帮助信息

示例:
    python main.py gui      # 启动GUI界面
    python main.py convert  # 运行数据转换
    python main.py test     # 运行测试
    """
    print(help_text)

if __name__ == "__main__":
    main()
