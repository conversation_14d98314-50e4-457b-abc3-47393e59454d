"""
中调水电转换器
专门处理中调水电类型的电站和机组转换
"""
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from ..utils.exceptions import ConversionError
from ..processors.backup_manager import BackupManager

logger = logging.getLogger(__name__)

class HydropowerConverter:
    """中调水电转换器"""
    
    def __init__(self, source_file: str, target_file: str):
        """
        初始化转换器
        
        Args:
            source_file (str): 源文件路径
            target_file (str): 目标文件路径
        """
        self.source_file = source_file
        self.target_file = target_file
        self.backup_manager = BackupManager()
        
        # 中调水电转换配置
        self.source_machine_type = 'SD_KT'  # 源文件中的机组类型
        self.station_type = 371  # 电站表中的类型
        self.station_name = '中调水电'  # 固定的电站名称
        self.cutoff_date = datetime(2025, 6, 12)  # 投产时间截止日期
        
    def convert_hydropower_station(self) -> bool:
        """
        转换中调水电电站数据
        创建固定的中调水电电站记录
        
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info("开始中调水电电站转换...")
            
            # 1. 创建备份
            backup_path = self.backup_manager.create_backup(
                self.target_file, 
                "中调水电电站转换"
            )
            logger.info(f"已创建备份: {backup_path}")
            
            # 2. 生成固定的电站数据
            station_data = self._generate_station_data()
            
            # 3. 写入目标文件
            success = self._write_station_data(station_data)
            
            if success:
                logger.info("✓ 中调水电电站转换完成")
                return True
            else:
                raise ConversionError("写入电站数据失败")
                
        except Exception as e:
            logger.error(f"中调水电电站转换失败: {str(e)}")
            return False
    
    def convert_hydropower_units(self, station_id: int = 56) -> bool:
        """
        转换中调水电机组数据
        从源文件读取SD_KT类型的机组数据
        
        Args:
            station_id (int): 电站ID，默认为56
            
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info("开始中调水电机组转换...")
            
            # 1. 创建备份
            backup_path = self.backup_manager.create_backup(
                self.target_file, 
                "中调水电机组转换"
            )
            logger.info(f"已创建备份: {backup_path}")
            
            # 2. 读取源数据
            source_data = self._read_source_data()
            if source_data is None or source_data.empty:
                raise ConversionError("读取源数据失败")
            
            # 3. 转换机组数据
            unit_data = self._convert_unit_data(source_data, station_id)
            if unit_data.empty:
                logger.warning("没有找到符合条件的机组数据")
                return True
            
            # 4. 写入目标文件
            success = self._write_unit_data(unit_data)
            
            if success:
                logger.info(f"✓ 中调水电机组转换完成，共处理 {len(unit_data)} 条记录")
                return True
            else:
                raise ConversionError("写入机组数据失败")
                
        except Exception as e:
            logger.error(f"中调水电机组转换失败: {str(e)}")
            return False
    
    def _generate_station_data(self) -> pd.DataFrame:
        """
        生成固定的中调水电电站数据
        
        Returns:
            pd.DataFrame: 电站数据
        """
        station_record = {
            '名称': self.station_name,
            '类型': self.station_type,
            '检修场地': 0,
            '备用Rmax': 0.1,
            '储能比率': 0,
            '储能效率': 0,
            '储能损耗': 0,
            '期望电量': 1,
            '最小电量': 1,
            '电站约束': 0,
            '流域ID': 0,
            '优化空间': 0
        }
        
        station_data = pd.DataFrame([station_record])
        logger.info("生成中调水电电站数据")
        return station_data
    
    def _read_source_data(self) -> Optional[pd.DataFrame]:
        """
        读取源文件数据
        
        Returns:
            Optional[pd.DataFrame]: 源数据，失败时返回None
        """
        try:
            source_path = Path(self.source_file)
            if not source_path.exists():
                raise FileNotFoundError(f"源文件不存在: {self.source_file}")
            
            # 读取Excel文件
            df = pd.read_excel(
                self.source_file,
                sheet_name='Sheet1',
                engine='openpyxl'
            )
            
            logger.info(f"成功读取源文件，共 {len(df)} 行数据")
            return df
            
        except Exception as e:
            logger.error(f"读取源文件失败: {str(e)}")
            return None
    
    def _convert_unit_data(self, source_data: pd.DataFrame, station_id: int) -> pd.DataFrame:
        """
        转换机组数据
        
        Args:
            source_data (pd.DataFrame): 源数据
            station_id (int): 电站ID
            
        Returns:
            pd.DataFrame: 转换后的机组数据
        """
        try:
            # 筛选SD_KT类型的数据
            filtered_data = source_data[source_data['机组类型'] == self.source_machine_type].copy()
            
            if len(filtered_data) == 0:
                logger.warning(f"未找到机组类型为 {self.source_machine_type} 的数据")
                return pd.DataFrame()
            
            logger.info(f"找到 {len(filtered_data)} 条SD_KT类型的机组数据")
            
            # 重置索引
            filtered_data = filtered_data.reset_index(drop=True)
            
            # 生成机组名称：项目名称 + 机组序号
            for i in range(len(filtered_data)):
                project_name = str(filtered_data.loc[i, '项目名称'])
                unit_number = str(filtered_data.loc[i, '机组序号'])
                filtered_data.loc[i, '项目名称'] = project_name + unit_number
            
            # 重命名列
            filtered_data.rename(columns={
                '项目名称': '名称',
                '机组容量': '单机容量',
                '投产时间': '投产年月'
            }, inplace=True)
            
            # 设置固定值
            filtered_data['电站ID'] = station_id
            filtered_data['有效性'] = 1
            filtered_data['台数'] = 1
            filtered_data['类型'] = 0
            filtered_data['技术出力'] = 0.5
            filtered_data['储能库容'] = 0
            filtered_data['检修天数'] = 30
            filtered_data['特性ID'] = 1
            filtered_data['退役年月'] = 0
            filtered_data['退役进度'] = 0
            filtered_data['动态投资'] = 10000
            filtered_data['变电投资'] = 0
            filtered_data['运维费率'] = 0.01
            filtered_data['运行费'] = 0.001
            filtered_data['燃料单耗'] = 0
            filtered_data['燃料单价'] = 0
            filtered_data['上网电价'] = 0.3296
            filtered_data['汛期电价'] = 0.3296
            filtered_data['爬坡率'] = 0.75
            filtered_data['功频系数'] = 0
            filtered_data['惯性常数'] = 0
            filtered_data['强迫停运'] = 0.05
            
            # 处理投产年月和投产进度
            self._process_production_date(filtered_data)
            
            # 确保列顺序
            columns = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                      '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                      '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                      '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']
            
            return filtered_data[columns]
            
        except Exception as e:
            logger.error(f"转换机组数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _process_production_date(self, data: pd.DataFrame):
        """
        处理投产年月和投产进度逻辑
        
        Args:
            data (pd.DataFrame): 机组数据
        """
        # 初始化投产进度
        data['投产进度'] = 0
        
        for i in range(len(data)):
            try:
                production_date_str = data.loc[i, '投产年月']
                
                if pd.isna(production_date_str) or production_date_str == '':
                    # 投产时间为空
                    data.loc[i, '投产年月'] = "0"
                    data.loc[i, '投产进度'] = 0
                else:
                    # 尝试解析日期
                    production_date = pd.to_datetime(production_date_str, errors='coerce')
                    
                    if pd.isna(production_date):
                        # 无效日期
                        data.loc[i, '投产年月'] = "0"
                        data.loc[i, '投产进度'] = 0
                    elif production_date < self.cutoff_date:
                        # 早于截止日期
                        data.loc[i, '投产年月'] = "0"
                        data.loc[i, '投产进度'] = 0
                    else:
                        # 晚于截止日期
                        data.loc[i, '投产年月'] = production_date.strftime('%Y%m')
                        data.loc[i, '投产进度'] = 101
                        
            except Exception as e:
                logger.warning(f"处理第{i+1}行投产时间失败: {str(e)}")
                data.loc[i, '投产年月'] = "0"
                data.loc[i, '投产进度'] = 0
    
    def _write_station_data(self, station_data: pd.DataFrame) -> bool:
        """
        写入电站数据到目标文件
        
        Args:
            station_data (pd.DataFrame): 电站数据
            
        Returns:
            bool: 写入是否成功
        """
        try:
            # 这里应该调用实际的写入方法
            # 暂时返回True，实际实现时需要调用相应的Excel写入方法
            logger.info("电站数据写入成功（模拟）")
            return True
            
        except Exception as e:
            logger.error(f"写入电站数据失败: {str(e)}")
            return False
    
    def _write_unit_data(self, unit_data: pd.DataFrame) -> bool:
        """
        写入机组数据到目标文件
        
        Args:
            unit_data (pd.DataFrame): 机组数据
            
        Returns:
            bool: 写入是否成功
        """
        try:
            # 这里应该调用实际的写入方法
            # 暂时返回True，实际实现时需要调用相应的Excel写入方法
            logger.info("机组数据写入成功（模拟）")
            return True
            
        except Exception as e:
            logger.error(f"写入机组数据失败: {str(e)}")
            return False
