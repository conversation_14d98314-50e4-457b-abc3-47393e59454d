"""
电站数据转换器
实现从源文件筛选机组类型并生成电站名称的功能
"""
import pandas as pd
from typing import List, Dict, Any
from core.excel.reader import ExcelReader
from core.excel.writer import ExcelWriter
from processors.backup_manager import BackupManager
from utils.logger import get_main_logger
from utils.exceptions import ConversionError, ExcelReadError, ExcelWriteError

logger = get_main_logger()

class StationConverter:
    """电站数据转换器"""
    
    def __init__(self, source_file: str, target_file: str):
        """
        初始化转换器
        
        Args:
            source_file (str): 源文件路径
            target_file (str): 目标文件路径
        """
        self.source_file = source_file
        self.target_file = target_file
        self.backup_manager = BackupManager()
        
        # 转换配置
        self.target_machine_types = ['MD_200', 'MD_300', 'MD_600', 'MD_1000']
        self.source_sheet = 'Sheet1'
        self.target_sheet = '电站表'
        self.target_type_value = 300
        
    def convert_projects_to_stations(self) -> bool:
        """
        将项目转换为电站
        主要功能：筛选指定机组类型的项目，生成电站名称并写入目标文件
        
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info("开始电站数据转换...")
            
            # 1. 创建备份
            backup_path = self.backup_manager.create_backup(
                self.target_file, 
                "电站名称转换"
            )
            logger.info(f"已创建备份: {backup_path}")
            
            # 2. 读取源数据
            logger.info("读取源文件数据...")
            source_data = self._read_source_data()
            if source_data is None or source_data.empty:
                raise ConversionError("读取源数据失败")
            
            # 3. 筛选和转换数据
            logger.info("筛选和转换数据...")
            station_data = self._filter_and_convert_data(source_data)
            if not station_data or len(station_data) == 0:
                logger.warning("没有找到符合条件的数据")
                return True
            
            # 4. 写入目标文件
            logger.info("写入目标文件...")
            success = self._write_target_data(station_data)
            
            if success:
                logger.info(f"✓ 电站数据转换完成，共处理 {len(station_data)} 条记录")
                return True
            else:
                raise ConversionError("写入目标文件失败")
                
        except Exception as e:
            logger.error(f"电站数据转换失败: {str(e)}")
            return False
    
    def _read_source_data(self) -> pd.DataFrame:
        """
        读取源文件数据
        
        Returns:
            pd.DataFrame: 源数据
        """
        try:
            reader = ExcelReader(self.source_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载源文件工作簿", self.source_file)
            
            # 读取Sheet1的数据
            data_list = reader.read_sheet_data(self.source_sheet)
            reader.close()

            if not data_list or len(data_list) == 0:
                raise ExcelReadError("源文件中没有数据", self.source_file, self.source_sheet)
            
            # 转换为DataFrame
            df = pd.DataFrame(data_list)
            logger.info(f"成功读取源数据，共 {len(df)} 行")
            
            return df
            
        except Exception as e:
            raise ExcelReadError(f"读取源文件失败: {str(e)}", self.source_file, self.source_sheet)
    
    def _filter_and_convert_data(self, source_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        筛选和转换数据
        
        Args:
            source_df (pd.DataFrame): 源数据
            
        Returns:
            List[Dict[str, Any]]: 转换后的电站数据
        """
        try:
            # 筛选指定机组类型的数据
            filtered_df = source_df[source_df['机组类型'].isin(self.target_machine_types)]
            logger.info(f"筛选出 {len(filtered_df)} 条符合条件的记录")
            
            if len(filtered_df) == 0:
                return []
            
            # 按项目名称分组，获取唯一项目
            unique_projects = filtered_df['项目名称'].unique()
            logger.info(f"找到 {len(unique_projects)} 个唯一项目")
            
            # 生成电站数据
            station_data = []
            for i, project_name in enumerate(unique_projects, 1):
                # 生成电站名称：MD_1_项目名称1, MD_2_项目名称2, ...
                station_name = f"MD_{i}_{project_name}"
                
                station_record = {
                    '名称': station_name,  # B列
                    '类型': self.target_type_value  # E列
                }
                
                station_data.append(station_record)
                logger.debug(f"生成电站: {station_name}")
            
            logger.info(f"成功生成 {len(station_data)} 个电站记录")
            return station_data
            
        except Exception as e:
            raise ConversionError(f"数据转换失败: {str(e)}")
    
    def _write_target_data(self, station_data: List[Dict[str, Any]]) -> bool:
        """
        写入目标文件

        Args:
            station_data (List[Dict[str, Any]]): 电站数据

        Returns:
            bool: 是否写入成功
        """
        try:
            writer = ExcelWriter(self.target_file)

            if not writer.load_or_create_workbook():
                raise ExcelWriteError("无法加载目标文件工作簿", self.target_file)

            # 检查目标工作表是否存在
            if self.target_sheet not in writer.workbook.sheetnames:
                logger.error(f"目标工作表 '{self.target_sheet}' 不存在")
                raise ExcelWriteError(f"目标工作表 '{self.target_sheet}' 不存在", self.target_file)

            # 定义列映射：字段名 -> 列号
            column_mapping = {
                '名称': 2,  # B列
                '类型': 5   # E列
            }

            # 使用新的更新方法，只更新指定列，保持其他列不变
            success = writer.update_specific_columns(
                sheet_name=self.target_sheet,
                data=station_data,
                column_mapping=column_mapping,
                start_row=2  # 从第2行开始，保留标题行
            )

            if success:
                # 保存文件
                if writer.save():
                    logger.info(f"成功写入 {len(station_data)} 条电站数据到 {self.target_sheet}")
                    writer.close()
                    return True
                else:
                    raise ExcelWriteError("保存文件失败", self.target_file)
            else:
                raise ExcelWriteError("写入数据失败", self.target_file, self.target_sheet)

        except Exception as e:
            raise ExcelWriteError(f"写入目标文件失败: {str(e)}", self.target_file, self.target_sheet)
    
    def get_conversion_preview(self) -> Dict[str, Any]:
        """
        获取转换预览
        
        Returns:
            Dict[str, Any]: 预览信息
        """
        try:
            # 读取源数据
            source_df = self._read_source_data()
            
            # 筛选数据
            filtered_df = source_df[source_df['机组类型'].isin(self.target_machine_types)]
            unique_projects = filtered_df['项目名称'].unique()
            
            # 生成预览
            preview_stations = []
            for i, project_name in enumerate(unique_projects[:10], 1):  # 只预览前10个
                station_name = f"MD_{i}_{project_name}"
                preview_stations.append({
                    'original_project': project_name,
                    'generated_station': station_name
                })
            
            return {
                'total_source_records': len(source_df),
                'filtered_records': len(filtered_df),
                'unique_projects': len(unique_projects),
                'target_machine_types': self.target_machine_types,
                'preview_stations': preview_stations,
                'has_more': len(unique_projects) > 10
            }
            
        except Exception as e:
            logger.error(f"获取转换预览失败: {str(e)}")
            return {}

    def get_generated_stations(self) -> List[Dict[str, Any]]:
        """
        获取已生成的电站数据（用于后续机组转换）

        Returns:
            List[Dict[str, Any]]: 电站数据列表
        """
        try:
            # 读取目标文件的电站表
            reader = ExcelReader(self.target_file)

            if not reader.load_workbook():
                logger.error("无法加载目标文件工作簿")
                return []

            # 读取电站表数据
            station_data = reader.read_sheet_data(self.target_sheet)
            reader.close()

            if not station_data:
                logger.warning("电站表中没有数据")
                return []

            # 转换为DataFrame并筛选类型=300的记录
            df = pd.DataFrame(station_data)

            # 筛选类型为300的电站
            if '类型' in df.columns:
                filtered_df = df[df['类型'] == 300]
                logger.info(f"找到 {len(filtered_df)} 个类型为300的电站")
                return filtered_df.to_dict('records')
            else:
                logger.error("电站表中没有找到'类型'列")
                return []

        except Exception as e:
            logger.error(f"获取已生成电站数据失败: {str(e)}")
            return []
