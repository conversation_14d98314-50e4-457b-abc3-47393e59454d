"""
机组数据转换器
实现从电站数据生成机组数据的功能
"""
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime
from core.excel.reader import ExcelReader
from core.excel.writer import ExcelWriter
from processors.backup_manager import BackupManager
from utils.logger import get_main_logger
from utils.exceptions import ConversionError, ExcelReadError, ExcelWriteError

logger = get_main_logger()

class UnitConverter:
    """机组数据转换器"""
    
    def __init__(self, source_file: str, target_file: str):
        """
        初始化转换器
        
        Args:
            source_file (str): 源文件路径
            target_file (str): 目标文件路径
        """
        self.source_file = source_file
        self.target_file = target_file
        self.backup_manager = BackupManager()
        
        # 转换配置
        self.station_sheet = '电站表'
        self.source_sheet = 'Sheet1'
        self.unit_sheet = '机组表'
        self.characteristic_sheet = '特性表'
        self.cutoff_date = datetime(2025, 6, 12)
        
    def convert_stations_to_units(self) -> bool:
        """
        将电站转换为机组
        主要功能：从电站表提取数据，匹配源文件生成机组记录
        
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info("开始机组数据转换...")
            
            # 1. 创建备份
            backup_path = self.backup_manager.create_backup(
                self.target_file, 
                "机组数据转换"
            )
            logger.info(f"已创建备份: {backup_path}")
            
            # 2. 读取电站数据（类型=300）
            logger.info("读取电站数据...")
            station_data = self._read_station_data()
            if not station_data:
                logger.warning("没有找到符合条件的电站数据")
                return True
            
            # 3. 读取源文件数据
            logger.info("读取源文件数据...")
            source_data = self._read_source_data()
            if source_data is None or source_data.empty:
                raise ConversionError("读取源数据失败")
            
            # 4. 读取特性表数据
            logger.info("读取特性表数据...")
            characteristic_data = self._read_characteristic_data()
            if characteristic_data is None or characteristic_data.empty:
                raise ConversionError("读取特性表数据失败")
            
            # 5. 处理数据转换
            logger.info("处理数据转换...")
            unit_data = self._process_unit_conversion(
                station_data, source_data, characteristic_data
            )
            
            if not unit_data:
                logger.warning("没有生成机组数据")
                return True
            
            # 6. 写入目标文件
            logger.info("写入目标文件...")
            success = self._write_unit_data(unit_data)
            
            if success:
                logger.info(f"✓ 机组数据转换完成，共处理 {len(unit_data)} 条记录")
                return True
            else:
                raise ConversionError("写入目标文件失败")
                
        except Exception as e:
            logger.error(f"机组数据转换失败: {str(e)}")
            return False
    
    def _read_station_data(self) -> List[Dict[str, Any]]:
        """
        读取电站数据（类型=300）
        
        Returns:
            List[Dict[str, Any]]: 电站数据
        """
        try:
            reader = ExcelReader(self.target_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载目标文件工作簿", self.target_file)
            
            # 读取电站表数据
            data_list = reader.read_sheet_data(self.station_sheet)
            reader.close()

            if not data_list:
                logger.warning("电站表中没有数据")
                return []
            
            # 转换为DataFrame并筛选
            df = pd.DataFrame(data_list)
            
            # 筛选类型为300的电站
            if '类型' in df.columns:
                filtered_df = df[df['类型'] == 300]
                logger.info(f"找到 {len(filtered_df)} 个类型为300的电站")
                return filtered_df.to_dict('records')
            else:
                logger.error("电站表中没有找到'类型'列")
                return []
            
        except Exception as e:
            raise ExcelReadError(f"读取电站数据失败: {str(e)}", self.target_file, self.station_sheet)
    
    def _read_source_data(self) -> pd.DataFrame:
        """
        读取源文件数据
        
        Returns:
            pd.DataFrame: 源数据
        """
        try:
            reader = ExcelReader(self.source_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载源文件工作簿", self.source_file)
            
            # 读取Sheet1的数据
            data_list = reader.read_sheet_data(self.source_sheet)
            reader.close()

            if not data_list:
                raise ExcelReadError("源文件中没有数据", self.source_file, self.source_sheet)
            
            # 转换为DataFrame
            df = pd.DataFrame(data_list)
            logger.info(f"成功读取源数据，共 {len(df)} 行")
            
            return df
            
        except Exception as e:
            raise ExcelReadError(f"读取源文件失败: {str(e)}", self.source_file, self.source_sheet)
    
    def _read_characteristic_data(self) -> pd.DataFrame:
        """
        读取特性表数据
        
        Returns:
            pd.DataFrame: 特性数据
        """
        try:
            reader = ExcelReader(self.target_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载目标文件工作簿", self.target_file)
            
            # 读取特性表数据
            data_list = reader.read_sheet_data(self.characteristic_sheet)
            reader.close()

            if not data_list:
                raise ExcelReadError("特性表中没有数据", self.target_file, self.characteristic_sheet)
            
            # 转换为DataFrame
            df = pd.DataFrame(data_list)
            logger.info(f"成功读取特性数据，共 {len(df)} 行")
            
            return df
            
        except Exception as e:
            raise ExcelReadError(f"读取特性表失败: {str(e)}", self.target_file, self.characteristic_sheet)
    
    def _extract_project_name_from_station(self, station_name: str) -> str:
        """
        从电站名称中提取项目名称
        例如：MD_1_博贺电厂二期 -> 博贺电厂二期
        
        Args:
            station_name (str): 电站名称
            
        Returns:
            str: 项目名称
        """
        if not station_name:
            return ""
        
        # 分割电站名称，格式：MD_序号_项目名称
        parts = station_name.split('_', 2)
        if len(parts) >= 3:
            return parts[2]  # 返回项目名称部分
        else:
            logger.warning(f"电站名称格式不正确: {station_name}")
            return station_name
    
    def _parse_production_date(self, date_str: str) -> str:
        """
        解析投产时间
        
        Args:
            date_str (str): 投产时间字符串
            
        Returns:
            str: 格式化的投产年月（YYYYMM）或 "0"
        """
        if not date_str:
            return "0"
        
        try:
            # 尝试解析日期
            if isinstance(date_str, str):
                # 处理常见的日期格式
                for fmt in ['%Y/%m/%d', '%Y-%m-%d', '%Y/%m', '%Y-%m']:
                    try:
                        date_obj = datetime.strptime(date_str, fmt)
                        break
                    except ValueError:
                        continue
                else:
                    logger.warning(f"无法解析投产时间: {date_str}")
                    return "0"
            else:
                # 如果是datetime对象
                date_obj = date_str
            
            # 检查是否早于截止日期
            if date_obj < self.cutoff_date:
                return "0"
            else:
                return date_obj.strftime("%Y%m")
                
        except Exception as e:
            logger.warning(f"解析投产时间失败: {date_str}, 错误: {str(e)}")
            return "0"

    def _extract_unit_type_number(self, unit_type: str) -> str:
        """
        从机组类型中提取数字部分
        例如：MD_1000 -> 1000

        Args:
            unit_type (str): 机组类型

        Returns:
            str: 数字部分
        """
        if not unit_type:
            return ""

        # 去除MD_前缀
        if unit_type.startswith('MD_'):
            return unit_type[3:]
        else:
            return unit_type

    def _find_characteristic(self, unit_type_number: str, characteristic_data: pd.DataFrame) -> Dict[str, Any]:
        """
        在特性表中查找匹配的特性数据

        Args:
            unit_type_number (str): 机组类型数字（如1000）
            characteristic_data (pd.DataFrame): 特性表数据

        Returns:
            Dict[str, Any]: 特性数据
        """
        try:
            # 构造查找模式：数字 + MW
            search_pattern = f"{unit_type_number}MW"

            # 在机组型号列中查找
            if '机组型号' in characteristic_data.columns:
                matched_rows = characteristic_data[
                    characteristic_data['机组型号'].astype(str).str.contains(
                        search_pattern, case=False, na=False
                    )
                ]

                if not matched_rows.empty:
                    # 返回第一个匹配的记录
                    result = matched_rows.iloc[0].to_dict()
                    logger.debug(f"找到特性匹配: {unit_type_number} -> {result}")
                    return result
                else:
                    logger.warning(f"未找到机组型号匹配: {search_pattern}")
                    return {}
            else:
                logger.error("特性表中没有找到'机组型号'列")
                return {}

        except Exception as e:
            logger.error(f"查找特性数据失败: {str(e)}")
            return {}

    def _process_unit_conversion(self, station_data: List[Dict[str, Any]],
                               source_data: pd.DataFrame,
                               characteristic_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        处理机组数据转换

        Args:
            station_data: 电站数据
            source_data: 源文件数据
            characteristic_data: 特性表数据

        Returns:
            List[Dict[str, Any]]: 机组数据列表
        """
        unit_records = []

        try:
            for station in station_data:
                station_name = station.get('名称', '')
                station_id = station.get('电站ID', 0)

                if not station_name:
                    logger.warning("电站名称为空，跳过")
                    continue

                # 提取项目名称
                project_name = self._extract_project_name_from_station(station_name)
                if not project_name:
                    logger.warning(f"无法提取项目名称: {station_name}")
                    continue

                logger.debug(f"处理电站: {station_name} -> 项目: {project_name}")

                # 在源数据中查找匹配的项目
                if '项目名称' in source_data.columns:
                    matched_projects = source_data[
                        source_data['项目名称'].astype(str).str.strip() == project_name.strip()
                    ]

                    if matched_projects.empty:
                        logger.warning(f"未找到匹配的项目: {project_name}")
                        continue

                    logger.info(f"找到 {len(matched_projects)} 个匹配的机组记录")

                    # 处理每个匹配的机组
                    for _, unit_row in matched_projects.iterrows():
                        unit_record = self._create_unit_record(
                            station_name, station_id, unit_row, characteristic_data
                        )

                        if unit_record:
                            unit_records.append(unit_record)
                            logger.debug(f"生成机组记录: {unit_record.get('名称', '')}")
                else:
                    logger.error("源数据中没有找到'项目名称'列")

            logger.info(f"共生成 {len(unit_records)} 个机组记录")
            return unit_records

        except Exception as e:
            logger.error(f"处理机组转换失败: {str(e)}")
            return []

    def _create_unit_record(self, station_name: str, station_id: int,
                          unit_row: pd.Series, characteristic_data: pd.DataFrame) -> Dict[str, Any]:
        """
        创建单个机组记录

        Args:
            station_name: 电站名称
            station_id: 电站ID
            unit_row: 源数据中的机组行
            characteristic_data: 特性表数据

        Returns:
            Dict[str, Any]: 机组记录
        """
        try:
            # 提取基础数据
            unit_sequence = unit_row.get('机组序号', '')
            unit_capacity = unit_row.get('机组容量', 0)
            unit_type = unit_row.get('机组类型', '')
            production_time = unit_row.get('投产时间', '')
            retirement_time = unit_row.get('退役时间', '')

            # 生成机组名称：电站名称_机组序号
            unit_name = f"{station_name}_{unit_sequence}"

            # 解析投产时间
            production_month = self._parse_production_date(production_time)

            # 根据投产年月确定投产进度
            # 投产年月为0时，投产进度也为0；否则为101
            production_progress = 101 if production_month != "0" else 0

            # 提取机组类型数字并查找特性
            unit_type_number = self._extract_unit_type_number(unit_type)
            characteristic = self._find_characteristic(unit_type_number, characteristic_data)

            # 获取技术出力和特性ID
            technical_output = characteristic.get('最小出力', 0)  # 根据实际列名调整
            characteristic_id = characteristic.get('特性ID', '')  # 根据实际列名调整

            # 创建机组记录
            unit_record = {
                '名称': unit_name,                    # B列
                '电站ID': station_id,                # D列
                '单机容量': unit_capacity,            # E列
                '台数': 1,                           # F列
                '类型': 0,                           # G列
                '技术出力': technical_output,         # H列
                '储能库容': 0,                       # I列
                '检修天数': 30,                      # J列
                '特性ID': characteristic_id,         # K列
                '投产年月': production_month,         # L列
                '投产进度': production_progress,      # M列
                '退役年月': 0,                       # N列
                '退役进度': 0,                       # O列
                '动态投资': 3700,                    # P列
                '变电投资': 0,                       # Q列
                '运维费率': 0.04,                    # R列
                '运行费': 0.041,                     # S列
                '燃料单耗': 320,                     # T列
                '燃料单价': 850,                     # U列
                '上网电价': 0.453,                   # V列
                '汛期电价': 0.453,                   # W列
                '爬坡率': 0.02,                      # X列
                '功频系数': 0,                       # Y列
                '惯性常数': 0,                       # Z列
                '强迫停运': 0.05                     # AA列
            }

            logger.debug(f"创建机组记录: {unit_name}")
            return unit_record

        except Exception as e:
            logger.error(f"创建机组记录失败: {str(e)}")
            return {}

    def _write_unit_data(self, unit_data: List[Dict[str, Any]]) -> bool:
        """
        写入机组数据到目标文件

        Args:
            unit_data: 机组数据列表

        Returns:
            bool: 是否写入成功
        """
        try:
            writer = ExcelWriter(self.target_file)

            if not writer.load_or_create_workbook():
                raise ExcelWriteError("无法加载目标文件工作簿", self.target_file)

            # 检查目标工作表是否存在
            if self.unit_sheet not in writer.workbook.sheetnames:
                logger.error(f"目标工作表 '{self.unit_sheet}' 不存在")
                raise ExcelWriteError(f"目标工作表 '{self.unit_sheet}' 不存在", self.target_file)

            # 定义列映射：字段名 -> 列号
            column_mapping = {
                '名称': 2,        # B列
                '电站ID': 4,      # D列
                '单机容量': 5,    # E列
                '台数': 6,        # F列
                '类型': 7,        # G列
                '技术出力': 8,    # H列
                '储能库容': 9,    # I列
                '检修天数': 10,   # J列
                '特性ID': 11,     # K列
                '投产年月': 12,   # L列
                '投产进度': 13,   # M列
                '退役年月': 14,   # N列
                '退役进度': 15,   # O列
                '动态投资': 16,   # P列
                '变电投资': 17,   # Q列
                '运维费率': 18,   # R列
                '运行费': 19,     # S列
                '燃料单耗': 20,   # T列
                '燃料单价': 21,   # U列
                '上网电价': 22,   # V列
                '汛期电价': 23,   # W列
                '爬坡率': 24,     # X列
                '功频系数': 25,   # Y列
                '惯性常数': 26,   # Z列
                '强迫停运': 27    # AA列
            }

            # 使用更新方法写入数据
            success = writer.update_specific_columns(
                sheet_name=self.unit_sheet,
                data=unit_data,
                column_mapping=column_mapping,
                start_row=2  # 从第2行开始，保留标题行
            )

            if success:
                # 保存文件
                if writer.save():
                    logger.info(f"成功写入 {len(unit_data)} 条机组数据到 {self.unit_sheet}")
                    writer.close()
                    return True
                else:
                    raise ExcelWriteError("保存文件失败", self.target_file)
            else:
                raise ExcelWriteError("写入数据失败", self.target_file, self.unit_sheet)

        except Exception as e:
            raise ExcelWriteError(f"写入机组数据失败: {str(e)}", self.target_file, self.unit_sheet)

    def get_conversion_preview(self) -> Dict[str, Any]:
        """
        获取转换预览

        Returns:
            Dict[str, Any]: 预览信息
        """
        try:
            # 读取电站数据
            station_data = self._read_station_data()
            if not station_data:
                return {'error': '没有找到符合条件的电站数据'}

            # 读取源数据
            source_data = self._read_source_data()
            if source_data is None or source_data.empty:
                return {'error': '读取源数据失败'}

            # 统计信息
            preview_info = {
                'station_count': len(station_data),
                'source_records': len(source_data),
                'preview_units': []
            }

            # 生成预览（只处理前5个电站）
            for station in station_data[:5]:
                station_name = station.get('名称', '')
                project_name = self._extract_project_name_from_station(station_name)

                if '项目名称' in source_data.columns:
                    matched_projects = source_data[
                        source_data['项目名称'].astype(str).str.strip() == project_name.strip()
                    ]

                    for _, unit_row in matched_projects.iterrows():
                        unit_sequence = unit_row.get('机组序号', '')
                        unit_name = f"{station_name}_{unit_sequence}"

                        preview_info['preview_units'].append({
                            'station_name': station_name,
                            'project_name': project_name,
                            'unit_name': unit_name,
                            'unit_capacity': unit_row.get('机组容量', 0),
                            'unit_type': unit_row.get('机组类型', '')
                        })

            return preview_info

        except Exception as e:
            logger.error(f"获取转换预览失败: {str(e)}")
            return {'error': f'获取预览失败: {str(e)}'}
