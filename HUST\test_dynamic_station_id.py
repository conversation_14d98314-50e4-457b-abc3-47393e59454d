"""
测试动态电站ID分配功能
验证电站ID能够正确延续现有文件中的最大ID
"""
import sys
from pathlib import Path
import pandas as pd
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.station_id_manager import get_next_station_id, get_or_create_station_id, check_station_exists

def test_station_id_manager():
    """测试电站ID管理器的各种功能"""
    print("=" * 60)
    print("测试电站ID管理器功能")
    print("=" * 60)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir_path = Path(temp_dir)
            test_file = temp_dir_path / "测试电站表.xlsx"
            
            # 1. 测试空文件的情况
            print("1. 测试空文件情况:")
            next_id = get_next_station_id(str(test_file))
            print(f"   空文件的下一个ID: {next_id}")
            assert next_id == 1, f"期望ID为1，实际为{next_id}"
            
            # 2. 创建包含现有数据的测试文件
            print("\n2. 创建包含现有数据的测试文件:")
            existing_data = pd.DataFrame({
                '电站ID': [1, 2, 5, 10],
                '名称': ['电站A', '电站B', '电站C', '电站D'],
                '类型': [320, 325, 360, 371]
            })
            
            with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                existing_data.to_excel(writer, sheet_name='电站表', index=False)
            
            print(f"   创建测试文件，包含电站ID: {list(existing_data['电站ID'])}")
            
            # 3. 测试获取下一个ID
            print("\n3. 测试获取下一个ID:")
            next_id = get_next_station_id(str(test_file))
            print(f"   现有最大ID: 10，下一个ID: {next_id}")
            assert next_id == 11, f"期望ID为11，实际为{next_id}"
            
            # 4. 测试检查电站是否存在
            print("\n4. 测试检查电站是否存在:")
            exists, station_id = check_station_exists(str(test_file), '电站A')
            print(f"   电站A是否存在: {exists}, ID: {station_id}")
            assert exists == True and station_id == 1, f"期望存在且ID为1，实际存在={exists}, ID={station_id}"
            
            exists, station_id = check_station_exists(str(test_file), '不存在的电站')
            print(f"   不存在的电站是否存在: {exists}, ID: {station_id}")
            assert exists == False and station_id == None, f"期望不存在，实际存在={exists}"
            
            # 5. 测试获取或创建电站ID
            print("\n5. 测试获取或创建电站ID:")
            
            # 5.1 获取现有电站ID
            station_id, is_new = get_or_create_station_id(str(test_file), '电站A')
            print(f"   现有电站A: ID={station_id}, 是否新建={is_new}")
            assert station_id == 1 and is_new == False, f"期望ID=1且非新建，实际ID={station_id}, 新建={is_new}"
            
            # 5.2 创建新电站ID
            station_id, is_new = get_or_create_station_id(str(test_file), '中调水电')
            print(f"   新电站中调水电: ID={station_id}, 是否新建={is_new}")
            assert station_id == 11 and is_new == True, f"期望ID=11且为新建，实际ID={station_id}, 新建={is_new}"
            
            # 5.3 再次获取刚创建的电站ID
            station_id2, is_new2 = get_or_create_station_id(str(test_file), '中调水电')
            print(f"   再次获取中调水电: ID={station_id2}, 是否新建={is_new2}")
            # 注意：由于我们没有实际保存到文件，所以这里仍然会是新建
            
            print("\n✓ 所有电站ID管理器测试通过！")
            return True
            
    except Exception as e:
        print(f"❌ 电站ID管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_wind_power_id_assignment():
    """测试风电电站ID分配"""
    print("\n" + "=" * 60)
    print("测试风电电站ID分配")
    print("=" * 60)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir_path = Path(temp_dir)
            test_file = temp_dir_path / "风电测试.xlsx"
            
            # 1. 创建包含现有电站的文件
            print("1. 创建包含现有电站的测试文件:")
            existing_stations = pd.DataFrame({
                '电站ID': [1, 2, 3, 56],  # 包含中调水电的ID 56
                '名称': ['调峰气电A', '燃气电站B', '核电站C', '中调水电'],
                '类型': [320, 325, 360, 371],
                '有效性': [1, 1, 1, 1]
            })
            
            with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                existing_stations.to_excel(writer, sheet_name='电站表', index=False)
            
            print(f"   现有电站: {list(existing_stations['名称'])}")
            print(f"   现有ID: {list(existing_stations['电站ID'])}")
            
            # 2. 测试陆上风电ID分配
            print("\n2. 测试陆上风电ID分配:")
            onshore_id, is_new = get_or_create_station_id(str(test_file), '陆上风电')
            print(f"   陆上风电: ID={onshore_id}, 是否新建={is_new}")
            expected_onshore_id = 57  # 应该是56+1
            assert onshore_id == expected_onshore_id and is_new == True, f"期望ID={expected_onshore_id}且为新建"
            
            # 3. 模拟保存陆上风电后，测试海上风电ID分配
            print("\n3. 模拟保存陆上风电后的状态:")
            # 添加陆上风电到现有数据
            updated_stations = pd.concat([existing_stations, pd.DataFrame({
                '电站ID': [onshore_id],
                '名称': ['陆上风电'],
                '类型': [390],
                '有效性': [1]
            })], ignore_index=True)
            
            # 重新保存文件
            with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                updated_stations.to_excel(writer, sheet_name='电站表', index=False)
            
            print(f"   更新后的电站: {list(updated_stations['名称'])}")
            print(f"   更新后的ID: {list(updated_stations['电站ID'])}")
            
            # 4. 测试海上风电ID分配
            print("\n4. 测试海上风电ID分配:")
            offshore_id, is_new = get_or_create_station_id(str(test_file), '海上风电')
            print(f"   海上风电: ID={offshore_id}, 是否新建={is_new}")
            expected_offshore_id = 58  # 应该是57+1
            assert offshore_id == expected_offshore_id and is_new == True, f"期望ID={expected_offshore_id}且为新建"
            
            # 5. 测试重复添加的情况
            print("\n5. 测试重复添加陆上风电:")
            duplicate_id, is_new = get_or_create_station_id(str(test_file), '陆上风电')
            print(f"   重复的陆上风电: ID={duplicate_id}, 是否新建={is_new}")
            assert duplicate_id == onshore_id and is_new == False, f"期望使用现有ID且非新建"
            
            print("\n✓ 所有风电ID分配测试通过！")
            return True
            
    except Exception as e:
        print(f"❌ 风电ID分配测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir_path = Path(temp_dir)
            
            # 1. 测试文件不存在的情况
            print("1. 测试文件不存在:")
            non_existent_file = temp_dir_path / "不存在的文件.xlsx"
            next_id = get_next_station_id(str(non_existent_file))
            print(f"   不存在文件的下一个ID: {next_id}")
            assert next_id == 1, f"期望ID为1，实际为{next_id}"
            
            # 2. 测试空的电站表
            print("\n2. 测试空的电站表:")
            empty_file = temp_dir_path / "空电站表.xlsx"
            empty_data = pd.DataFrame(columns=['电站ID', '名称', '类型'])
            
            with pd.ExcelWriter(empty_file, engine='openpyxl') as writer:
                empty_data.to_excel(writer, sheet_name='电站表', index=False)
            
            next_id = get_next_station_id(str(empty_file))
            print(f"   空电站表的下一个ID: {next_id}")
            assert next_id == 1, f"期望ID为1，实际为{next_id}"
            
            # 3. 测试包含非数值ID的情况
            print("\n3. 测试包含非数值ID:")
            mixed_data = pd.DataFrame({
                '电站ID': [1, 'ABC', 3, None, 5],
                '名称': ['电站1', '电站2', '电站3', '电站4', '电站5'],
                '类型': [320, 325, 360, 371, 390]
            })
            
            mixed_file = temp_dir_path / "混合ID.xlsx"
            with pd.ExcelWriter(mixed_file, engine='openpyxl') as writer:
                mixed_data.to_excel(writer, sheet_name='电站表', index=False)
            
            next_id = get_next_station_id(str(mixed_file))
            print(f"   混合ID的下一个ID: {next_id}")
            assert next_id == 6, f"期望ID为6，实际为{next_id}"  # 应该忽略非数值，取最大的5+1
            
            print("\n✓ 所有边界情况测试通过！")
            return True
            
    except Exception as e:
        print(f"❌ 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试动态电站ID分配功能...")
    
    # 运行各项测试
    test_results = []
    
    test_results.append(("电站ID管理器", test_station_id_manager()))
    test_results.append(("风电ID分配", test_wind_power_id_assignment()))
    test_results.append(("边界情况", test_edge_cases()))
    
    # 总结测试结果
    print("\n" + "=" * 70)
    print("动态电站ID分配测试总结")
    print("=" * 70)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 所有动态ID分配测试通过！")
        print("\n✅ 修复确认:")
        print("  ✓ 电站ID不再硬编码为57、58")
        print("  ✓ 能够正确读取现有文件中的最大ID")
        print("  ✓ 新电站ID会自动延续现有ID序列")
        print("  ✓ 重复添加同名电站会使用现有ID")
        print("  ✓ 处理各种边界情况（空文件、非数值ID等）")
        
        print(f"\n🚀 现在用户可以:")
        print("  1. 多次运行程序添加风电数据")
        print("  2. 电站ID会自动按序分配，不会冲突")
        print("  3. 如果电站已存在，会使用现有ID")
        print("  4. 不需要手动删除重复数据")
        
    else:
        print(f"\n❌ 部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
