"""
测试中调水电转换功能
"""
import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger
from config.settings import FILES
from type_convert.unit_type_one import unit_type_convert_one_row
from get_cols.unit_cols_one import unit_get_cols_one_row

def test_hydropower_conversion():
    """测试中调水电转换功能"""
    print("=" * 60)
    print("测试中调水电转换功能")
    print("=" * 60)
    
    # 初始化日志
    logger = init_main_logger()
    
    try:
        # 1. 测试源文件读取
        source_file = project_root / FILES['source_file']
        if not source_file.exists():
            print(f"❌ 源文件不存在: {source_file}")
            return False
        
        print(f"✓ 找到源文件: {source_file}")
        
        # 2. 读取源数据
        print("\n读取源数据...")
        source_data = pd.read_excel(
            source_file,
            sheet_name='Sheet1',
            engine='openpyxl'
        )
        print(f"✓ 成功读取源数据，共 {len(source_data)} 行")
        
        # 3. 检查SD_KT类型数据
        sd_kt_data = source_data[source_data['机组类型'] == 'SD_KT']
        print(f"✓ 找到 {len(sd_kt_data)} 条SD_KT类型的数据")
        
        if len(sd_kt_data) == 0:
            print("❌ 没有找到SD_KT类型的数据，无法进行转换测试")
            return False
        
        # 显示前几条SD_KT数据
        print("\n前5条SD_KT数据:")
        print("-" * 50)
        for i, (idx, row) in enumerate(sd_kt_data.head().iterrows()):
            print(f"{i+1}. 项目名称: {row['项目名称']}, 机组序号: {row['机组序号']}, 机组容量: {row['机组容量']}")
        
        # 4. 测试机组转换
        print("\n测试机组转换...")
        unit_data = unit_type_convert_one_row(source_data.copy(), '中调水电')
        
        if unit_data.empty:
            print("❌ 机组转换失败，返回空数据")
            return False
        
        print(f"✓ 机组转换成功，生成 {len(unit_data)} 条记录")
        
        # 显示转换后的机组名称
        print("\n转换后的机组名称:")
        print("-" * 30)
        for i, name in enumerate(unit_data['名称'].head()):
            print(f"{i+1}. {name}")
        
        # 5. 测试列处理
        print("\n测试列处理...")
        unit_cols_name = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                         '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                         '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                         '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']
        
        processed_data = unit_get_cols_one_row(unit_data.copy(), unit_cols_name, '中调水电')
        
        if processed_data.empty:
            print("❌ 列处理失败，返回空数据")
            return False
        
        print(f"✓ 列处理成功，最终数据包含 {len(processed_data)} 行 {len(processed_data.columns)} 列")
        
        # 6. 验证关键字段值
        print("\n验证关键字段值:")
        print("-" * 30)
        sample_row = processed_data.iloc[0]
        
        key_fields = {
            '电站ID': 56,
            '技术出力': 0.5,
            '特性ID': 1,
            '动态投资': 10000,
            '上网电价': 0.3296,
            '爬坡率': 0.75,
            '运维费率': 0.01
        }
        
        all_correct = True
        for field, expected_value in key_fields.items():
            actual_value = sample_row[field]
            if actual_value == expected_value:
                print(f"✓ {field}: {actual_value}")
            else:
                print(f"❌ {field}: 期望 {expected_value}, 实际 {actual_value}")
                all_correct = False
        
        # 7. 验证投产时间逻辑
        print("\n验证投产时间逻辑:")
        print("-" * 30)
        
        for i, row in processed_data.head(3).iterrows():
            production_month = row['投产年月']
            production_progress = row['投产进度']
            
            if production_month == "0":
                expected_progress = 0
            else:
                expected_progress = 101
            
            if production_progress == expected_progress:
                print(f"✓ 记录{i+1}: 投产年月={production_month}, 投产进度={production_progress}")
            else:
                print(f"❌ 记录{i+1}: 投产年月={production_month}, 投产进度={production_progress} (期望{expected_progress})")
                all_correct = False
        
        # 8. 生成测试报告
        print("\n" + "=" * 60)
        print("测试报告")
        print("=" * 60)
        
        if all_correct:
            print("🎉 所有测试通过！中调水电转换功能正常工作")
            
            # 保存测试结果
            output_file = project_root / "中调水电转换测试结果.xlsx"
            processed_data.to_excel(output_file, index=False)
            print(f"✓ 测试结果已保存到: {output_file}")
            
            return True
        else:
            print("❌ 部分测试失败，请检查转换逻辑")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_station_data_generation():
    """测试电站数据生成"""
    print("\n" + "=" * 60)
    print("测试中调水电电站数据生成")
    print("=" * 60)
    
    try:
        # 生成固定的电站数据
        station_data = pd.DataFrame({
            '电站ID': [56],
            '名称': ['中调水电'],
            '有效性': [1],
            '节点ID': [1],
            '类型': [371],
            '检修场地': [0],
            '备用Rmax': [0.1],
            '储能比率': [0],
            '储能效率': [0],
            '储能损耗': [0],
            '期望电量': [1],
            '最小电量': [1],
            '电站约束': [0],
            '流域ID': [0],
            '优化空间': [0]
        })
        
        print("✓ 成功生成中调水电电站数据")
        print("\n电站数据内容:")
        print("-" * 30)
        for col, value in station_data.iloc[0].items():
            print(f"{col}: {value}")
        
        # 保存电站数据
        station_output_file = project_root / "中调水电电站数据.xlsx"
        station_data.to_excel(station_output_file, index=False)
        print(f"\n✓ 电站数据已保存到: {station_output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 电站数据生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始中调水电转换功能测试...")
    
    # 测试机组转换
    unit_test_result = test_hydropower_conversion()
    
    # 测试电站数据生成
    station_test_result = test_station_data_generation()
    
    print("\n" + "=" * 60)
    print("总体测试结果")
    print("=" * 60)
    
    if unit_test_result and station_test_result:
        print("🎉 所有测试通过！中调水电转换功能完整且正常工作")
    else:
        print("❌ 部分测试失败，需要进一步检查和修复")
        if not unit_test_result:
            print("  - 机组转换测试失败")
        if not station_test_result:
            print("  - 电站数据生成测试失败")
