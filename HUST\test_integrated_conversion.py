"""
测试集成后的转换功能
模拟主程序的转换流程
"""
import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger
from config.settings import FILES
from type_convert.unit_type_one import unit_type_convert_one_row
from get_cols.unit_cols_one import unit_get_cols_one_row

def test_integrated_hydropower_conversion():
    """测试集成后的中调水电转换功能"""
    print("=" * 60)
    print("测试集成后的中调水电转换功能")
    print("=" * 60)
    
    try:
        # 模拟主程序的参数
        type_val = '中调水电'
        single_row = True
        
        # 读取源数据
        source_file = project_root / FILES['source_file']
        data = pd.read_excel(source_file, sheet_name='Sheet1', engine='openpyxl')
        print(f"✓ 成功读取源数据，共 {len(data)} 行")
        
        # 1. 创建电站数据（模拟主程序逻辑）
        if single_row and type_val == '中调水电':
            station_data = pd.DataFrame({
                '电站ID': [56],
                '名称': ['中调水电'],
                '有效性': [1],
                '节点ID': [1],
                '类型': [371],
                '检修场地': [0],
                '备用Rmax': [0.1],
                '储能比率': [0],
                '储能效率': [0],
                '储能损耗': [0],
                '期望电量': [1],
                '最小电量': [1],
                '电站约束': [0],
                '流域ID': [0],
                '优化空间': [0],
            })
            print("✓ 成功创建中调水电电站数据")
        
        # 2. 处理机组数据（模拟主程序逻辑）
        if single_row and type_val == '中调水电':
            unit_data = unit_type_convert_one_row(data, type_val)
            print(f"✓ 机组类型转换完成，获得 {len(unit_data)} 条记录")
            
            # 获取电站ID
            station_id = station_data.iloc[0]['电站ID'] if not station_data.empty else 56
            
            # 列处理
            unit_cols_name = ['有效性', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                              '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                              '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                              '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']
            
            unit_data = unit_get_cols_one_row(unit_data, unit_cols_name, type_val, station_id)
            print(f"✓ 机组列处理完成，最终数据包含 {len(unit_data)} 行 {len(unit_data.columns)} 列")
        
        # 3. 验证数据完整性
        print("\n验证数据完整性:")
        print("-" * 30)
        
        # 验证电站数据
        print("电站数据验证:")
        for col, value in station_data.iloc[0].items():
            print(f"  {col}: {value}")
        
        # 验证机组数据关键字段
        print(f"\n机组数据验证 (前3条记录):")
        key_fields = ['名称', '电站ID', '单机容量', '技术出力', '动态投资', '上网电价']
        for i, row in unit_data.head(3).iterrows():
            print(f"  记录{i+1}:")
            for field in key_fields:
                print(f"    {field}: {row[field]}")
        
        # 4. 保存测试结果
        station_output_file = project_root / "集成测试_中调水电电站数据.xlsx"
        unit_output_file = project_root / "集成测试_中调水电机组数据.xlsx"
        
        station_data.to_excel(station_output_file, index=False)
        unit_data.to_excel(unit_output_file, index=False)
        
        print(f"\n✓ 电站数据已保存到: {station_output_file}")
        print(f"✓ 机组数据已保存到: {unit_output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_wind_conversion():
    """测试集成后的风电转换功能"""
    print("\n" + "=" * 60)
    print("测试集成后的风电转换功能")
    print("=" * 60)
    
    try:
        # 读取源数据
        source_file = project_root / FILES['source_file']
        data = pd.read_excel(source_file, sheet_name='Sheet1', engine='openpyxl')

        # 定义通用的列名
        unit_cols_name = ['有效性', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                          '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                          '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                          '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']

        # 测试陆上风电
        print("测试陆上风电:")
        print("-" * 30)

        type_val = '陆上风电'
        single_row = True
        
        # 创建电站数据
        station_data = pd.DataFrame({
            '电站ID': [57],
            '名称': ['陆上风电'],
            '有效性': [1],
            '节点ID': [1],
            '类型': [390],
            '检修场地': [0],
            '备用Rmax': [0.1],
            '储能比率': [1],
            '储能效率': [0],
            '储能损耗': [0],
            '期望电量': [0],
            '最小电量': [0],
            '电站约束': [0],
            '流域ID': [0],
            '优化空间': [0],
        })
        
        # 处理机组数据
        unit_data = data[data['机组类型'] == 'FD'].copy()
        if not unit_data.empty:
            unit_data = unit_data.reset_index(drop=True)
            unit_data.rename(columns={'项目名称': '名称', '机组容量': '单机容量', '投产时间': '投产年月'}, inplace=True)
            
            station_id = station_data.iloc[0]['电站ID']
            unit_data = unit_get_cols_one_row(unit_data, unit_cols_name, type_val, station_id)
            
            print(f"✓ 陆上风电转换成功，电站1个，机组{len(unit_data)}个")
            
            # 保存结果
            station_output_file = project_root / "集成测试_陆上风电电站数据.xlsx"
            unit_output_file = project_root / "集成测试_陆上风电机组数据.xlsx"
            
            station_data.to_excel(station_output_file, index=False)
            unit_data.to_excel(unit_output_file, index=False)
            
            print(f"✓ 陆上风电数据已保存")
        
        # 测试海上风电
        print("\n测试海上风电:")
        print("-" * 30)
        
        type_val = '海上风电'
        
        # 创建电站数据
        station_data = pd.DataFrame({
            '电站ID': [58],
            '名称': ['海上风电'],
            '有效性': [1],
            '节点ID': [1],
            '类型': [392],
            '检修场地': [0],
            '备用Rmax': [0.1],
            '储能比率': [1],
            '储能效率': [0],
            '储能损耗': [0],
            '期望电量': [0],
            '最小电量': [0],
            '电站约束': [0],
            '流域ID': [0],
            '优化空间': [0],
        })
        
        # 处理机组数据
        unit_data = data[data['机组类型'].isin(['FD_JH', 'FD_SH'])].copy()
        if not unit_data.empty:
            unit_data = unit_data.reset_index(drop=True)
            unit_data.rename(columns={'项目名称': '名称', '机组容量': '单机容量', '投产时间': '投产年月'}, inplace=True)
            
            station_id = station_data.iloc[0]['电站ID']
            unit_data = unit_get_cols_one_row(unit_data, unit_cols_name, type_val, station_id)
            
            print(f"✓ 海上风电转换成功，电站1个，机组{len(unit_data)}个")
            
            # 保存结果
            station_output_file = project_root / "集成测试_海上风电电站数据.xlsx"
            unit_output_file = project_root / "集成测试_海上风电机组数据.xlsx"
            
            station_data.to_excel(station_output_file, index=False)
            unit_data.to_excel(unit_output_file, index=False)
            
            print(f"✓ 海上风电数据已保存")
        
        return True
        
    except Exception as e:
        print(f"❌ 风电集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始集成转换功能测试...")
    
    # 初始化日志
    logger = init_main_logger()
    
    # 测试中调水电
    hydropower_result = test_integrated_hydropower_conversion()
    
    # 测试风电
    wind_result = test_integrated_wind_conversion()
    
    print("\n" + "=" * 60)
    print("集成测试总结")
    print("=" * 60)
    
    if hydropower_result and wind_result:
        print("🎉 所有集成测试通过！")
        print("✓ 中调水电转换功能已正确集成")
        print("✓ 风电转换功能已正确集成")
        print("✓ 主程序现在可以正常处理这些类型")
    else:
        print("❌ 部分集成测试失败")
        if not hydropower_result:
            print("  - 中调水电集成测试失败")
        if not wind_result:
            print("  - 风电集成测试失败")
