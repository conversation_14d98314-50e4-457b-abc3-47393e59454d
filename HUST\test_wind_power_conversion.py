"""
测试风电转换功能
"""
import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger
from config.settings import FILES

def test_wind_power_data_analysis():
    """分析源数据中的风电类型"""
    print("=" * 60)
    print("分析源数据中的风电类型")
    print("=" * 60)
    
    try:
        # 读取源数据
        source_file = project_root / FILES['source_file']
        if not source_file.exists():
            print(f"❌ 源文件不存在: {source_file}")
            return False
        
        print(f"✓ 找到源文件: {source_file}")
        
        source_data = pd.read_excel(
            source_file,
            sheet_name='Sheet1',
            engine='openpyxl'
        )
        print(f"✓ 成功读取源数据，共 {len(source_data)} 行")
        
        # 分析机组类型
        machine_types = source_data['机组类型'].value_counts()
        print(f"\n所有机组类型统计:")
        print("-" * 40)
        for machine_type, count in machine_types.items():
            print(f"{machine_type}: {count} 条")
        
        # 检查风电相关类型
        wind_types = ['FD', 'FD_JH', 'FD_SH']
        wind_data_summary = {}
        
        print(f"\n风电类型数据分析:")
        print("-" * 40)
        
        for wind_type in wind_types:
            wind_data = source_data[source_data['机组类型'] == wind_type]
            wind_data_summary[wind_type] = len(wind_data)
            print(f"{wind_type}: {len(wind_data)} 条")
            
            if len(wind_data) > 0:
                print(f"  前3个项目: {list(wind_data['项目名称'].head(3))}")
        
        return wind_data_summary
        
    except Exception as e:
        print(f"❌ 数据分析失败: {str(e)}")
        return False

def test_onshore_wind_conversion():
    """测试陆上风电转换"""
    print("\n" + "=" * 60)
    print("测试陆上风电转换功能")
    print("=" * 60)
    
    try:
        # 读取源数据
        source_file = project_root / FILES['source_file']
        source_data = pd.read_excel(
            source_file,
            sheet_name='Sheet1',
            engine='openpyxl'
        )
        
        # 筛选FD类型数据
        fd_data = source_data[source_data['机组类型'] == 'FD'].copy()
        
        if len(fd_data) == 0:
            print("❌ 没有找到FD类型的数据")
            return False
        
        print(f"✓ 找到 {len(fd_data)} 条FD类型的数据")
        
        # 模拟转换过程
        fd_data = fd_data.reset_index(drop=True)
        
        # 重命名列
        fd_data.rename(columns={
            '项目名称': '名称',
            '机组容量': '单机容量',
            '投产时间': '投产年月'
        }, inplace=True)
        
        # 设置陆上风电的固定值
        fd_data['电站ID'] = 57
        fd_data['有效性'] = 1
        fd_data['台数'] = 1
        fd_data['类型'] = 0
        fd_data['技术出力'] = 0
        fd_data['储能库容'] = 0
        fd_data['检修天数'] = 0
        fd_data['特性ID'] = 0
        fd_data['退役年月'] = 0
        fd_data['退役进度'] = 0
        fd_data['动态投资'] = 4000
        fd_data['变电投资'] = 0
        fd_data['运维费率'] = 0.05
        fd_data['运行费'] = 0
        fd_data['燃料单耗'] = 0.45
        fd_data['燃料单价'] = 0.45
        fd_data['上网电价'] = 0.45
        fd_data['汛期电价'] = 0.45
        fd_data['爬坡率'] = 0
        fd_data['功频系数'] = 0
        fd_data['惯性常数'] = 0
        fd_data['强迫停运'] = 0.05
        fd_data['投产进度'] = 0  # 先设为0，后面处理
        
        # 处理投产时间逻辑
        cutoff_date = pd.to_datetime('2025-06-12')
        for i in range(len(fd_data)):
            try:
                production_date_str = fd_data.loc[i, '投产年月']
                if pd.isna(production_date_str) or production_date_str == '':
                    fd_data.loc[i, '投产年月'] = "0"
                    fd_data.loc[i, '投产进度'] = 0
                else:
                    production_date = pd.to_datetime(production_date_str, errors='coerce')
                    if pd.isna(production_date) or production_date < cutoff_date:
                        fd_data.loc[i, '投产年月'] = "0"
                        fd_data.loc[i, '投产进度'] = 0
                    else:
                        fd_data.loc[i, '投产年月'] = production_date.strftime('%Y%m')
                        fd_data.loc[i, '投产进度'] = 101
            except:
                fd_data.loc[i, '投产年月'] = "0"
                fd_data.loc[i, '投产进度'] = 0
        
        print(f"✓ 陆上风电机组转换成功，生成 {len(fd_data)} 条记录")
        
        # 显示前几条记录
        print("\n前5条陆上风电机组记录:")
        print("-" * 50)
        for i, row in fd_data.head().iterrows():
            print(f"{i+1}. {row['名称']}, 容量: {row['单机容量']}MW, 投产: {row['投产年月']}")
        
        # 验证关键字段
        print("\n验证关键字段值:")
        print("-" * 30)
        sample_row = fd_data.iloc[0]
        key_fields = {
            '电站ID': 57,
            '技术出力': 0,
            '特性ID': 0,
            '动态投资': 4000,
            '上网电价': 0.45,
            '运维费率': 0.05
        }
        
        all_correct = True
        for field, expected_value in key_fields.items():
            actual_value = sample_row[field]
            if actual_value == expected_value:
                print(f"✓ {field}: {actual_value}")
            else:
                print(f"❌ {field}: 期望 {expected_value}, 实际 {actual_value}")
                all_correct = False
        
        # 保存测试结果
        output_file = project_root / "陆上风电转换测试结果.xlsx"
        fd_data.to_excel(output_file, index=False)
        print(f"\n✓ 测试结果已保存到: {output_file}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 陆上风电转换测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_offshore_wind_conversion():
    """测试海上风电转换"""
    print("\n" + "=" * 60)
    print("测试海上风电转换功能")
    print("=" * 60)
    
    try:
        # 读取源数据
        source_file = project_root / FILES['source_file']
        source_data = pd.read_excel(
            source_file,
            sheet_name='Sheet1',
            engine='openpyxl'
        )
        
        # 筛选FD_JH和FD_SH类型数据
        offshore_types = ['FD_JH', 'FD_SH']
        offshore_data = source_data[source_data['机组类型'].isin(offshore_types)].copy()
        
        if len(offshore_data) == 0:
            print("❌ 没有找到FD_JH或FD_SH类型的数据")
            return False
        
        print(f"✓ 找到 {len(offshore_data)} 条海上风电类型的数据")
        
        # 显示类型分布
        type_counts = offshore_data['机组类型'].value_counts()
        print("类型分布:")
        for machine_type, count in type_counts.items():
            print(f"  {machine_type}: {count} 条")
        
        # 模拟转换过程
        offshore_data = offshore_data.reset_index(drop=True)
        
        # 重命名列
        offshore_data.rename(columns={
            '项目名称': '名称',
            '机组容量': '单机容量',
            '投产时间': '投产年月'
        }, inplace=True)
        
        # 设置海上风电的固定值
        offshore_data['电站ID'] = 58
        offshore_data['有效性'] = 1
        offshore_data['台数'] = 1
        offshore_data['类型'] = 0
        offshore_data['技术出力'] = 0
        offshore_data['储能库容'] = 0
        offshore_data['检修天数'] = 0
        offshore_data['特性ID'] = 0
        offshore_data['退役年月'] = 0
        offshore_data['退役进度'] = 0
        offshore_data['动态投资'] = 17000
        offshore_data['变电投资'] = 0
        offshore_data['运维费率'] = 0.05
        offshore_data['运行费'] = 0
        offshore_data['燃料单耗'] = 0
        offshore_data['燃料单价'] = 0
        offshore_data['上网电价'] = 0.55
        offshore_data['汛期电价'] = 0.55
        offshore_data['爬坡率'] = 0
        offshore_data['功频系数'] = 0
        offshore_data['惯性常数'] = 0
        offshore_data['强迫停运'] = 0.05
        offshore_data['投产进度'] = 0  # 先设为0，后面处理
        
        # 处理投产时间逻辑
        cutoff_date = pd.to_datetime('2025-06-12')
        for i in range(len(offshore_data)):
            try:
                production_date_str = offshore_data.loc[i, '投产年月']
                if pd.isna(production_date_str) or production_date_str == '':
                    offshore_data.loc[i, '投产年月'] = "0"
                    offshore_data.loc[i, '投产进度'] = 0
                else:
                    production_date = pd.to_datetime(production_date_str, errors='coerce')
                    if pd.isna(production_date) or production_date < cutoff_date:
                        offshore_data.loc[i, '投产年月'] = "0"
                        offshore_data.loc[i, '投产进度'] = 0
                    else:
                        offshore_data.loc[i, '投产年月'] = production_date.strftime('%Y%m')
                        offshore_data.loc[i, '投产进度'] = 101
            except:
                offshore_data.loc[i, '投产年月'] = "0"
                offshore_data.loc[i, '投产进度'] = 0
        
        print(f"✓ 海上风电机组转换成功，生成 {len(offshore_data)} 条记录")
        
        # 显示前几条记录
        print("\n前5条海上风电机组记录:")
        print("-" * 50)
        for i, row in offshore_data.head().iterrows():
            print(f"{i+1}. {row['名称']}, 容量: {row['单机容量']}MW, 投产: {row['投产年月']}")
        
        # 验证关键字段
        print("\n验证关键字段值:")
        print("-" * 30)
        sample_row = offshore_data.iloc[0]
        key_fields = {
            '电站ID': 58,
            '技术出力': 0,
            '特性ID': 0,
            '动态投资': 17000,
            '上网电价': 0.55,
            '运维费率': 0.05
        }
        
        all_correct = True
        for field, expected_value in key_fields.items():
            actual_value = sample_row[field]
            if actual_value == expected_value:
                print(f"✓ {field}: {actual_value}")
            else:
                print(f"❌ {field}: 期望 {expected_value}, 实际 {actual_value}")
                all_correct = False
        
        # 保存测试结果
        output_file = project_root / "海上风电转换测试结果.xlsx"
        offshore_data.to_excel(output_file, index=False)
        print(f"\n✓ 测试结果已保存到: {output_file}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 海上风电转换测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_wind_station_data_generation():
    """测试风电电站数据生成"""
    print("\n" + "=" * 60)
    print("测试风电电站数据生成")
    print("=" * 60)
    
    try:
        # 生成陆上风电电站数据
        onshore_station_data = pd.DataFrame({
            '电站ID': [57],
            '名称': ['陆上风电'],
            '有效性': [1],
            '节点ID': [1],
            '类型': [390],
            '检修场地': [0],
            '备用Rmax': [0.1],
            '储能比率': [1],
            '储能效率': [0],
            '储能损耗': [0],
            '期望电量': [0],
            '最小电量': [0],
            '电站约束': [0],
            '流域ID': [0],
            '优化空间': [0]
        })
        
        # 生成海上风电电站数据
        offshore_station_data = pd.DataFrame({
            '电站ID': [58],
            '名称': ['海上风电'],
            '有效性': [1],
            '节点ID': [1],
            '类型': [392],
            '检修场地': [0],
            '备用Rmax': [0.1],
            '储能比率': [1],
            '储能效率': [0],
            '储能损耗': [0],
            '期望电量': [0],
            '最小电量': [0],
            '电站约束': [0],
            '流域ID': [0],
            '优化空间': [0]
        })
        
        print("✓ 成功生成风电电站数据")
        
        print("\n陆上风电电站数据:")
        print("-" * 30)
        for col, value in onshore_station_data.iloc[0].items():
            print(f"{col}: {value}")
        
        print("\n海上风电电站数据:")
        print("-" * 30)
        for col, value in offshore_station_data.iloc[0].items():
            print(f"{col}: {value}")
        
        # 保存电站数据
        onshore_output_file = project_root / "陆上风电电站数据.xlsx"
        offshore_output_file = project_root / "海上风电电站数据.xlsx"
        
        onshore_station_data.to_excel(onshore_output_file, index=False)
        offshore_station_data.to_excel(offshore_output_file, index=False)
        
        print(f"\n✓ 陆上风电电站数据已保存到: {onshore_output_file}")
        print(f"✓ 海上风电电站数据已保存到: {offshore_output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风电电站数据生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始风电转换功能测试...")
    
    # 初始化日志
    logger = init_main_logger()
    
    # 分析源数据
    wind_data_summary = test_wind_power_data_analysis()
    
    # 测试陆上风电转换
    onshore_test_result = test_onshore_wind_conversion()
    
    # 测试海上风电转换
    offshore_test_result = test_offshore_wind_conversion()
    
    # 测试电站数据生成
    station_test_result = test_wind_station_data_generation()
    
    print("\n" + "=" * 60)
    print("总体测试结果")
    print("=" * 60)
    
    if onshore_test_result and offshore_test_result and station_test_result:
        print("🎉 所有风电转换测试通过！功能完整且正常工作")
    else:
        print("❌ 部分测试失败，需要进一步检查和修复")
        if not onshore_test_result:
            print("  - 陆上风电转换测试失败")
        if not offshore_test_result:
            print("  - 海上风电转换测试失败")
        if not station_test_result:
            print("  - 电站数据生成测试失败")
