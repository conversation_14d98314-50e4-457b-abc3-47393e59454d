import pandas as pd

def unit_type_convert(data, type_name, name_id_mapping):
    # 选择“机组类型”为type_name的行
    # 电源明细表 -> 机组表
    data = data[(data['机组类型'] == type_name)]
    data = data[data.机组类型.isin([type_name])]
    # 修改索引
    data = data.reset_index(drop=True)
    # 根据"名称"对应字典找到"电站ID"
    data['电站ID'] = 0
    for i in range(len(data)):
        data['电站ID'][i] = name_id_mapping[data['项目名称'][i]]
    # “机组类型”添加到“项目名称”前置
    type_name = type_name + '_'
    for i in range(len(data)):
        data['项目名称'][i] = type_name[:3] + str(i + 1) + type_name[2:6] + data['项目名称'][i]
    # “机组序号”添加到“项目名称”后置
    for i in range(len(data)):
        data['项目名称'][i] = data['项目名称'][i] + '_' + str(data['机组序号'][i])
    # 只保留“项目类型”列
    data.rename(columns={'项目名称': '名称'}, inplace=True)
    return data
