import pandas as pd

def unit_type_convert_one_row(data, type_name):
    """
    单行转换器 - 用于中调水电等特殊类型
    直接从电源明细表读取数据，因为电站表里只有一行
    
    Args:
        data: 源数据DataFrame
        type_name: 类型名称（'中调水电'）
    
    Returns:
        转换后的机组数据DataFrame
    """
    # 处理中调水电类型映射
    source_type = type_name
    if type_name == '中调水电':
        source_type = 'SD_KT'  # 在源文件中查找SD_KT类型
    
    # 筛选指定机组类型的数据
    filtered_data = data[data['机组类型'] == source_type].copy()
    
    if len(filtered_data) == 0:
        print(f"警告：未找到机组类型为 {source_type} 的数据")
        return pd.DataFrame()
    
    # 重置索引
    filtered_data = filtered_data.reset_index(drop=True)
    
    # 生成机组名称：项目名称 + 机组序号
    # 例如：新丰江1，新丰江2
    for i in range(len(filtered_data)):
        project_name = str(filtered_data.loc[i, '项目名称'])
        unit_number = str(filtered_data.loc[i, '机组序号'])
        filtered_data.loc[i, '项目名称'] = project_name + unit_number
    
    # 重命名列以匹配目标格式
    column_mapping = {
        '项目名称': '名称',
        '机组容量': '单机容量',
        '投产时间': '投产年月'
    }
    
    filtered_data.rename(columns=column_mapping, inplace=True)
    
    print(f"成功转换 {len(filtered_data)} 条中调水电机组记录")
    return filtered_data
