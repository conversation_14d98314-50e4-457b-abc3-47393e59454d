import pandas as pd
import os
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import sys
from pathlib import Path

# 获取上一级目录的绝对路径
parent_dir = str(Path(__file__).resolve().parent.parent)
sys.path.append(parent_dir)

from extract_chinese import extract_chinese_text

def update_excel_one_row_format(dataframe, excel_path, sheet_name, key_column='名称', center_alignment=True):
    """
    更新已有Excel文件中特定sheet的单行数据，保留原有格式（如颜色、样式等）
    专门用于中调水电、风电等单行类型的电站数据更新

    参数:
    dataframe (pd.DataFrame): 包含新数据的DataFrame（通常只有一行）
    excel_path (str): 已有Excel文件的路径
    sheet_name (str): 要更新的sheet名称
    key_column (str): 用于匹配行的键列名称，默认为'名称'
    center_alignment (bool): 是否设置单元格居中对齐

    返回:
    dict: 项目名称与电站ID的映射关系
    """
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"指定的Excel文件不存在: {excel_path}")

    # 检查DataFrame是否为空
    if dataframe.empty:
        print("警告：DataFrame为空，没有数据需要更新")
        return {}

    # 加载工作簿
    wb = load_workbook(excel_path)

    # 检查sheet是否存在
    if sheet_name not in wb.sheetnames:
        raise ValueError(f"Excel文件中不存在名为 '{sheet_name}' 的sheet")

    # 获取指定sheet
    ws = wb[sheet_name]

    # 获取DataFrame的列名
    df_columns = dataframe.columns.tolist()

    # 获取Excel表头行（假设表头在第1行）
    header = [cell.value for cell in ws[1]]

    # 确保表头至少有一列
    if not header:
        header = [key_column]

    # 创建列名到列索引的映射
    column_mapping = {}
    for i, col_name in enumerate(header):
        if col_name in df_columns:
            column_mapping[col_name] = i + 1  # Excel列索引从1开始

    # 如果没有找到匹配的列，添加新列
    missing_columns = [col for col in df_columns if col not in header]
    if missing_columns:
        print(f"添加新列: {missing_columns}")
        # 在表头行添加新列
        for i, col_name in enumerate(missing_columns):
            col_index = len(header) + i + 1
            ws.cell(row=1, column=col_index, value=col_name)
            column_mapping[col_name] = col_index
            header.append(col_name)

    # 创建项目名称与电站ID的映射
    name_id_mapping = {}

    # 处理DataFrame中的每一行（对于单行类型，通常只有一行）
    for index, row in dataframe.iterrows():
        key_value = row[key_column]
        
        # 查找是否已存在相同的记录
        existing_row = None
        for row_num in range(2, ws.max_row + 1):  # 从第2行开始（跳过表头）
            if key_column in column_mapping:
                cell_value = ws.cell(row=row_num, column=column_mapping[key_column]).value
                if cell_value == key_value:
                    existing_row = row_num
                    break

        # 如果没有找到现有记录，添加新行
        if existing_row is None:
            existing_row = ws.max_row + 1
            print(f"添加新记录: {key_value}")
        else:
            print(f"更新现有记录: {key_value}")

        # 更新行数据
        for col_name, value in row.items():
            if col_name in column_mapping:
                col_index = column_mapping[col_name]
                cell = ws.cell(row=existing_row, column=col_index)
                cell.value = value
                
                # 设置居中对齐
                if center_alignment:
                    cell.alignment = Alignment(horizontal='center', vertical='center')

        # 添加到映射中
        if '电站ID' in row:
            # 提取原始项目名称（去除数字后缀）
            original_name = extract_chinese_text(key_value)
            name_id_mapping[original_name] = row['电站ID']

    # 保存工作簿
    try:
        wb.save(excel_path)
        print(f"成功更新Excel文件: {excel_path}")
    except Exception as e:
        print(f"保存Excel文件时出错: {str(e)}")
        raise

    return name_id_mapping

def append_excel_one_row_format(dataframe, excel_path, sheet_name, center_alignment=True):
    """
    向已有Excel文件中特定sheet追加单行数据，保留原有格式
    
    参数:
    dataframe (pd.DataFrame): 包含新数据的DataFrame
    excel_path (str): 已有Excel文件的路径
    sheet_name (str): 要更新的sheet名称
    center_alignment (bool): 是否设置单元格居中对齐
    """
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"指定的Excel文件不存在: {excel_path}")

    # 检查DataFrame是否为空
    if dataframe.empty:
        print("警告：DataFrame为空，没有数据需要追加")
        return

    # 加载工作簿
    wb = load_workbook(excel_path)

    # 检查sheet是否存在
    if sheet_name not in wb.sheetnames:
        raise ValueError(f"Excel文件中不存在名为 '{sheet_name}' 的sheet")

    # 获取指定sheet
    ws = wb[sheet_name]

    # 获取DataFrame的列名
    df_columns = dataframe.columns.tolist()

    # 获取Excel表头行（假设表头在第1行）
    header = [cell.value for cell in ws[1]]

    # 创建列名到列索引的映射
    column_mapping = {}
    for i, col_name in enumerate(header):
        if col_name in df_columns:
            column_mapping[col_name] = i + 1  # Excel列索引从1开始

    # 如果没有找到匹配的列，添加新列
    missing_columns = [col for col in df_columns if col not in header]
    if missing_columns:
        print(f"添加新列: {missing_columns}")
        # 在表头行添加新列
        for i, col_name in enumerate(missing_columns):
            col_index = len(header) + i + 1
            ws.cell(row=1, column=col_index, value=col_name)
            column_mapping[col_name] = col_index

    # 追加数据行
    for index, row in dataframe.iterrows():
        new_row = ws.max_row + 1
        
        # 添加行数据
        for col_name, value in row.items():
            if col_name in column_mapping:
                col_index = column_mapping[col_name]
                cell = ws.cell(row=new_row, column=col_index)
                cell.value = value
                
                # 设置居中对齐
                if center_alignment:
                    cell.alignment = Alignment(horizontal='center', vertical='center')

    # 保存工作簿
    try:
        wb.save(excel_path)
        print(f"成功追加数据到Excel文件: {excel_path}")
    except Exception as e:
        print(f"保存Excel文件时出错: {str(e)}")
        raise
