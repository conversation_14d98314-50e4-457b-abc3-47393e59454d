import pandas as pd
import os
from openpyxl import load_workbook
from openpyxl.styles import Alignment

def update_excel_unit_with_format(dataframe, excel_path, sheet_name, key_column='名称', center_alignment=True):
    """
    更新已有Excel文件中特定sheet的数据，保留原有格式（如颜色、样式等），处理机组ID

    参数:
    dataframe (pd.DataFrame): 包含新数据的DataFrame
    excel_path (str): 已有Excel文件的路径
    sheet_name (str): 要更新的sheet名称
    key_column (str): 用于匹配行的键列名称
    center_alignment (bool): 是否设置单元格居中对齐

    返回:
    None
    """
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"指定的Excel文件不存在: {excel_path}")

    # 加载工作簿
    wb = load_workbook(excel_path)

    # 检查sheet是否存在
    if sheet_name not in wb.sheetnames:
        raise ValueError(f"Excel文件中不存在名为 '{sheet_name}' 的sheet")

    # 获取指定sheet
    ws = wb[sheet_name]

    # 获取DataFrame的列名
    df_columns = dataframe.columns.tolist()

    # 获取Excel表头行（假设表头在第1行）
    header = [cell.value for cell in ws[1]]

    # 确保表头至少有一列
    if not header:
        header = [key_column]
        ws.cell(row=1, column=1).value = key_column

    # 获取键列在Excel中的列索引（从1开始）
    if key_column in header:
        key_col_idx = header.index(key_column) + 1
    else:
        # 如果Excel中不存在键列，添加该键列
        key_col_idx = len(header) + 1
        ws.cell(row=1, column=key_col_idx).value = key_column
        header.append(key_column)

    # 处理机组ID列逻辑
    unit_id_col = '机组ID'
    if unit_id_col not in header:
        # 如果不存在机组ID列，添加到表头
        unit_id_idx = len(header) + 1
        ws.cell(row=1, column=unit_id_idx).value = unit_id_col
        header.append(unit_id_col)
    else:
        # 如果存在，获取其列索引
        unit_id_idx = header.index(unit_id_col) + 1

    # 获取当前最大机组ID值
    max_id = 0
    if ws.max_row > 1:  # 确保有数据行
        # 遍历所有数据行，查找最大机组ID
        for row_idx in range(2, ws.max_row + 1):
            id_value = ws.cell(row=row_idx, column=unit_id_idx).value
            # 确保ID值是整数类型并更新最大值
            if id_value and isinstance(id_value, int) and id_value > max_id:
                max_id = id_value

    # 创建一个字典，映射键值到Excel中的行号
    key_to_row = {}
    for row_idx in range(2, ws.max_row + 1):  # 从第2行开始（跳过表头）
        key_value = ws.cell(row=row_idx, column=key_col_idx).value
        if key_value is not None:
            key_to_row[key_value] = row_idx

    # 设置居中对齐样式
    if center_alignment:
        alignment = Alignment(horizontal='center', vertical='center')

    # 遍历DataFrame中的每一行数据
    for _, row_data in dataframe.iterrows():
        # 如果DataFrame中不存在键列，使用行索引作为键值
        key_value = row_data.get(key_column, _)

        # 如果键值存在于Excel中，则更新该行数据
        if key_value in key_to_row:
            row_idx = key_to_row[key_value]
            # 获取现有行的机组ID
            existing_id = ws.cell(row=row_idx, column=unit_id_idx).value

            # 更新每一列的数据
            for col_name in df_columns:
                if col_name in header:
                    col_idx = header.index(col_name) + 1
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.value = row_data[col_name]
                    # 设置居中对齐
                    if center_alignment:
                        cell.alignment = alignment
        else:
            # 如果键值不存在，则在Excel中新增一行
            new_row_idx = ws.max_row + 1
            max_id += 1  # 新增行的机组ID递增

            # 设置机组ID单元格并居中对齐
            id_cell = ws.cell(row=new_row_idx, column=unit_id_idx)
            id_cell.value = max_id
            if center_alignment:
                id_cell.alignment = alignment

            # 填充新行的其他数据
            for col_name in df_columns:
                if col_name in header:
                    col_idx = header.index(col_name) + 1
                    cell = ws.cell(row=new_row_idx, column=col_idx)
                    cell.value = row_data[col_name]
                    # 设置居中对齐
                    if center_alignment:
                        cell.alignment = alignment

    # 保存工作簿
    wb.save(excel_path)
    print(f"成功更新Excel文件 '{excel_path}' 的sheet '{sheet_name}'，保留原有格式")
