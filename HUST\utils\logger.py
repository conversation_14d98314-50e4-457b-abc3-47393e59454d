"""
日志工具模块
"""
import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

def setup_logger(name: str, 
                log_file: Optional[str] = None,
                level: str = 'INFO',
                format_string: Optional[str] = None,
                max_bytes: int = 10*1024*1024,
                backup_count: int = 5) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name (str): 日志记录器名称
        log_file (Optional[str]): 日志文件路径
        level (str): 日志级别
        format_string (Optional[str]): 日志格式字符串
        max_bytes (int): 日志文件最大字节数
        backup_count (int): 备份文件数量
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 设置日志格式
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    formatter = logging.Formatter(format_string)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name (str): 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器
    """
    return logging.getLogger(name)

class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志记录器"""
        return get_logger(self.__class__.__name__)

def log_function_call(func):
    """
    装饰器：记录函数调用
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        logger.debug(f"调用函数 {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise
    
    return wrapper

def log_execution_time(func):
    """
    装饰器：记录函数执行时间
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    import time
    
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.2f}秒): {str(e)}")
            raise
    
    return wrapper

class ProgressLogger:
    """进度日志记录器"""
    
    def __init__(self, logger: logging.Logger, total: int, step: int = 100):
        """
        初始化进度日志记录器
        
        Args:
            logger (logging.Logger): 日志记录器
            total (int): 总数量
            step (int): 日志记录步长
        """
        self.logger = logger
        self.total = total
        self.step = step
        self.current = 0
        self.last_logged = 0
    
    def update(self, count: int = 1):
        """
        更新进度
        
        Args:
            count (int): 增加的数量
        """
        self.current += count
        
        # 检查是否需要记录日志
        if (self.current - self.last_logged >= self.step or 
            self.current >= self.total):
            
            percentage = (self.current / self.total) * 100
            self.logger.info(f"进度: {self.current}/{self.total} ({percentage:.1f}%)")
            self.last_logged = self.current
    
    def finish(self):
        """完成进度记录"""
        if self.current < self.total:
            self.current = self.total
        
        self.logger.info(f"完成: {self.current}/{self.total} (100.0%)")

# 默认日志配置
DEFAULT_LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 全局日志记录器
main_logger = None

def init_main_logger(log_file: Optional[str] = None, **kwargs):
    """
    初始化主日志记录器

    Args:
        log_file (Optional[str]): 日志文件路径
        **kwargs: 其他日志配置参数
    """
    global main_logger

    config = DEFAULT_LOG_CONFIG.copy()
    config.update(kwargs)

    # 处理参数名映射
    if 'format' in config:
        config['format_string'] = config.pop('format')

    main_logger = setup_logger(
        'HUST',
        log_file=log_file,
        **config
    )

    return main_logger

def get_main_logger() -> logging.Logger:
    """
    获取主日志记录器
    
    Returns:
        logging.Logger: 主日志记录器
    """
    global main_logger
    
    if main_logger is None:
        main_logger = init_main_logger()
    
    return main_logger
