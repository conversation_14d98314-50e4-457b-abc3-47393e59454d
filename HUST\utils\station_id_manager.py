"""
电站ID管理器
用于动态分配电站ID，避免ID冲突
"""
import pandas as pd
import os
from openpyxl import load_workbook
import logging

logger = logging.getLogger(__name__)

def get_next_station_id(excel_path, sheet_name='电站表', default_start_id=1):
    """
    获取下一个可用的电站ID
    
    Args:
        excel_path (str): Excel文件路径
        sheet_name (str): 工作表名称，默认为'电站表'
        default_start_id (int): 如果文件不存在时的起始ID，默认为1
        
    Returns:
        int: 下一个可用的电站ID
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            logger.info(f"目标文件不存在，使用默认起始ID: {default_start_id}")
            return default_start_id
        
        # 读取现有的电站数据
        try:
            existing_data = pd.read_excel(excel_path, sheet_name=sheet_name, engine='openpyxl')
            logger.info(f"成功读取现有电站数据，共 {len(existing_data)} 行")
        except Exception as e:
            logger.warning(f"读取电站数据失败: {str(e)}，使用默认起始ID: {default_start_id}")
            return default_start_id
        
        # 检查是否有电站ID列
        if '电站ID' not in existing_data.columns:
            logger.info(f"未找到电站ID列，使用默认起始ID: {default_start_id}")
            return default_start_id
        
        # 检查是否有数据
        if existing_data.empty:
            logger.info(f"电站表为空，使用默认起始ID: {default_start_id}")
            return default_start_id
        
        # 获取现有的最大电站ID
        existing_ids = existing_data['电站ID'].dropna()
        if existing_ids.empty:
            logger.info(f"没有有效的电站ID，使用默认起始ID: {default_start_id}")
            return default_start_id
        
        # 转换为数值类型并获取最大值
        try:
            numeric_ids = pd.to_numeric(existing_ids, errors='coerce').dropna()
            if numeric_ids.empty:
                logger.info(f"没有有效的数值型电站ID，使用默认起始ID: {default_start_id}")
                return default_start_id
            
            max_id = int(numeric_ids.max())
            next_id = max_id + 1
            logger.info(f"现有最大电站ID: {max_id}，分配新ID: {next_id}")
            return next_id
            
        except Exception as e:
            logger.warning(f"处理电站ID时出错: {str(e)}，使用默认起始ID: {default_start_id}")
            return default_start_id
            
    except Exception as e:
        logger.error(f"获取下一个电站ID时出错: {str(e)}，使用默认起始ID: {default_start_id}")
        return default_start_id

def check_station_exists(excel_path, station_name, sheet_name='电站表'):
    """
    检查指定名称的电站是否已存在
    
    Args:
        excel_path (str): Excel文件路径
        station_name (str): 电站名称
        sheet_name (str): 工作表名称，默认为'电站表'
        
    Returns:
        tuple: (是否存在, 电站ID或None)
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            return False, None
        
        # 读取现有的电站数据
        try:
            existing_data = pd.read_excel(excel_path, sheet_name=sheet_name, engine='openpyxl')
        except Exception as e:
            logger.warning(f"读取电站数据失败: {str(e)}")
            return False, None
        
        # 检查是否有名称列
        if '名称' not in existing_data.columns:
            return False, None
        
        # 查找匹配的电站
        matching_stations = existing_data[existing_data['名称'] == station_name]
        
        if not matching_stations.empty:
            # 电站已存在，返回其ID
            station_id = matching_stations.iloc[0]['电站ID'] if '电站ID' in matching_stations.columns else None
            logger.info(f"电站 '{station_name}' 已存在，ID: {station_id}")
            return True, station_id
        else:
            logger.info(f"电站 '{station_name}' 不存在")
            return False, None
            
    except Exception as e:
        logger.error(f"检查电站是否存在时出错: {str(e)}")
        return False, None

def get_or_create_station_id(excel_path, station_name, sheet_name='电站表'):
    """
    获取现有电站ID或创建新的电站ID
    
    Args:
        excel_path (str): Excel文件路径
        station_name (str): 电站名称
        sheet_name (str): 工作表名称，默认为'电站表'
        
    Returns:
        tuple: (电站ID, 是否为新创建)
    """
    # 首先检查电站是否已存在
    exists, existing_id = check_station_exists(excel_path, station_name, sheet_name)
    
    if exists and existing_id is not None:
        # 电站已存在，返回现有ID
        return existing_id, False
    else:
        # 电站不存在，分配新ID
        new_id = get_next_station_id(excel_path, sheet_name)
        return new_id, True

def clean_duplicate_stations(excel_path, sheet_name='电站表'):
    """
    清理重复的电站记录（可选功能）
    
    Args:
        excel_path (str): Excel文件路径
        sheet_name (str): 工作表名称，默认为'电站表'
        
    Returns:
        bool: 是否成功清理
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            logger.info("文件不存在，无需清理")
            return True
        
        # 读取现有数据
        wb = load_workbook(excel_path)
        if sheet_name not in wb.sheetnames:
            logger.info(f"工作表 '{sheet_name}' 不存在，无需清理")
            return True
        
        # 读取数据
        existing_data = pd.read_excel(excel_path, sheet_name=sheet_name, engine='openpyxl')
        
        if existing_data.empty or '名称' not in existing_data.columns:
            logger.info("没有数据或缺少名称列，无需清理")
            return True
        
        # 查找重复的电站名称
        duplicates = existing_data[existing_data.duplicated(subset=['名称'], keep='first')]
        
        if not duplicates.empty:
            logger.info(f"发现 {len(duplicates)} 条重复的电站记录")
            
            # 删除重复记录，保留第一条
            cleaned_data = existing_data.drop_duplicates(subset=['名称'], keep='first')
            
            # 保存清理后的数据
            with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                cleaned_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info(f"成功清理重复记录，保留 {len(cleaned_data)} 条记录")
            return True
        else:
            logger.info("没有发现重复记录")
            return True
            
    except Exception as e:
        logger.error(f"清理重复电站记录时出错: {str(e)}")
        return False
