# HUST数据转换系统
## 电源明细表一键转换为GOPT和HUST所需输入数据

---

## 🎯 系统概述

**HUST数据转换系统** 是一个专业的电力系统数据转换工具，能够将电源明细表一键转换为GOPT和HUST系统所需的标准输入数据格式。

### 核心价值
- ⚡ **一键转换**：自动化数据处理，告别手工操作
- 🔒 **数据安全**：自动备份，支持版本管理
- 📊 **精准转换**：严格按照业务规则进行数据映射
- 🛠️ **易于维护**：模块化架构，便于扩展和调试

---

## 📋 功能架构

### 输入数据源
```
📁 01 电源明细表-模板.xlsx
├── Sheet1 (源数据)
│   ├── A列: ID
│   ├── B列: 项目名称
│   ├── C列: 机组序号
│   ├── D列: 机组容量
│   ├── E列: 机组类型 (MD_200/300/600/1000)
│   ├── 投产时间、退役时间等
```

### 输出目标文件
```
📁 电源明细表手动转HUST.xlsx
├── 电站表 (Station Data)
├── 机组表 (Unit Data)
├── 特性表 (Characteristic Data)
```

---

## 🔄 转换流程

### 第一步：电站数据转换
```mermaid
graph LR
    A[源文件筛选] --> B[机组类型过滤]
    B --> C[MD_200/300/600/1000]
    C --> D[生成电站名称]
    D --> E[MD_X_项目名称X]
    E --> F[写入电站表]
```

**转换规则**：
- 筛选条件：E列机组类型 = MD_200/MD_300/MD_600/MD_1000
- 生成格式：`MD_1_博贺电厂二期`, `MD_2_某某电厂`...
- 输出位置：电站表 B列(名称) + E列(类型=300)

### 第二步：机组数据转换
```mermaid
graph LR
    A[电站表筛选] --> B[类型=300]
    B --> C[匹配源项目]
    C --> D[提取机组信息]
    D --> E[特性表匹配]
    E --> F[生成机组记录]
    F --> G[写入机组表]
```

**转换规则**：
- 机组名称：`电站名称_机组序号`
- 投产逻辑：早于2025/6/12→投产年月=0，投产进度=0
- 特性匹配：MD_1000 → 1000MW → 提取技术出力
- 固定参数：台数=1，检修天数=30，运维费率=0.04等

---

## 💡 核心技术特性

### 智能数据处理
- **条件筛选**：自动识别符合条件的机组类型
- **名称生成**：按规则自动生成标准化名称
- **关联匹配**：跨表数据智能匹配和关联
- **日期处理**：复杂的投产时间逻辑判断

### 数据完整性保障
- **自动备份**：每次转换前自动创建带时间戳的备份
- **结构保持**：只更新指定列，保持原文件结构
- **异常处理**：完善的错误处理和日志记录
- **数据验证**：转换前后数据一致性检查

### 用户友好设计
- **预览功能**：转换前可预览生成结果
- **进度提示**：实时显示转换进度和状态
- **错误定位**：详细的错误信息和解决建议
- **批量处理**：支持大量数据的高效处理

---

## 📊 转换示例

### 输入示例
| ID | 项目名称 | 机组序号 | 机组容量 | 机组类型 | 投产时间 |
|----|----------|----------|----------|----------|----------|
| 1  | 博贺电厂二期 | 3 | 1000 | MD_1000 | 2025/6/30 |
| 2  | 博贺电厂二期 | 4 | 1000 | MD_1000 | 2025/7/15 |

### 输出结果

**电站表**：
| 名称 | 类型 |
|------|------|
| MD_1_博贺电厂二期 | 300 |

**机组表**：
| 名称 | 电站ID | 单机容量 | 技术出力 | 投产年月 | 投产进度 |
|------|--------|----------|----------|----------|----------|
| MD_1_博贺电厂二期_3 | 0 | 1000 | 0.3 | 202506 | 101 |
| MD_1_博贺电厂二期_4 | 0 | 1000 | 0.3 | 202507 | 101 |

---

## 🚀 使用方式

### 命令行执行
```bash
# 完整流程转换
python test_complete_flow.py

# 仅电站转换
python run_conversion.py

# 仅机组转换
python run_unit_conversion.py

# 数据结构调试
python debug_data_structure.py
```

### 转换步骤
1. **准备数据**：确保源文件和目标文件存在
2. **选择模式**：完整流程 / 单步转换
3. **预览确认**：查看转换预览，确认无误
4. **执行转换**：一键执行，自动完成
5. **结果验证**：检查生成数据的准确性

---

## 🔧 技术架构

### 三层混合架构
```
┌─────────────────┐
│   用户界面层     │  ← 命令行工具 + 预览功能
├─────────────────┤
│   业务逻辑层     │  ← 转换器 + 数据处理
├─────────────────┤
│   数据访问层     │  ← Excel读写 + SQLite支持
└─────────────────┘
```

### 核心模块
- **转换器模块**：StationConverter, UnitConverter
- **Excel操作**：ExcelReader, ExcelWriter
- **备份管理**：BackupManager
- **日志系统**：完整的操作日志记录
- **异常处理**：统一的错误处理机制

---

## 📈 项目优势

### 对比传统手工方式
| 项目 | 手工处理 | HUST系统 |
|------|----------|----------|
| **处理时间** | 数小时 | 数分钟 |
| **错误率** | 高 | 极低 |
| **数据一致性** | 难保证 | 自动保证 |
| **可重复性** | 差 | 完美 |
| **备份管理** | 手动 | 自动 |

### 业务价值
- 🎯 **效率提升**：处理速度提升10倍以上
- 🔍 **质量保障**：消除人工错误，确保数据准确性
- 📋 **标准化**：统一的数据格式和处理流程
- 🔄 **可扩展**：易于添加新的转换规则和功能

---

## 🎉 总结

**HUST数据转换系统**成功实现了电源明细表到GOPT/HUST格式的一键转换，具备：

✅ **完整功能**：电站转换 + 机组转换 + 特性匹配  
✅ **可靠性**：自动备份 + 异常处理 + 数据验证  
✅ **易用性**：预览功能 + 进度提示 + 详细日志  
✅ **可维护性**：模块化设计 + 清晰架构 + 完善文档  

**让数据转换从繁琐变简单，从手工变自动！**
