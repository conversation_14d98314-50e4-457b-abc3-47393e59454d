const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
const ExcelJS = require('exceljs');

const app = express();
const port = 3000;

// 中间件
app.use(cors());
app.use(bodyParser.json());

// 模拟数据
let lineStations = [
  {
    id: '1',
    name: '珠东-区外HK',
    startDate: '20100101',
    endDate: '21000101',
    startStation: 'GD_ZDN',
    endStation: 'QW_HK',
    region: '默认区域',
    capacity: 5000,
    type: '330kV线路',
    status: '已投建',
    length: 100,
    resistance: 0.28,
    reactance: 0.03,
    susceptance: 0.000359,
    thermalLimit: 5000,
    investment_cost: 10000,
    payback_period: 5,
    trip_rate: 0.1,
    fault_recovery_time: 2,
    reclosing_success_rate: 0.95,
    transient_stability_limit: 5500,
    relay_protection_time_avg: 0.1,
    relay_protection_time_stddev: 0.02,
    distance_relay_protection_setting: '设定值A',
    protection_refusal_probability: 0.001,
    protection_malfunction_probability: 0.0005,
    line_status_detail: '正常运行',
    fault_rate_gain: 1.0
  },
  {
    id: '2',
    name: '粤北-珠西北',
    startDate: '20100101',
    endDate: '21000101',
    startStation: 'GD_YB',
    endStation: 'GD_ZXB',
    region: '默认区域',
    capacity: 7000,
    type: '330kV线路',
    status: '已投建',
    length: 100,
    resistance: 0.28,
    reactance: 0.03,
    susceptance: 0.000359,
    thermalLimit: 7000,
    investment_cost: 15000,
    payback_period: 6,
    trip_rate: 0.08,
    fault_recovery_time: 1.8,
    reclosing_success_rate: 0.97,
    transient_stability_limit: 7500,
    relay_protection_time_avg: 0.09,
    relay_protection_time_stddev: 0.015,
    distance_relay_protection_setting: '设定值B',
    protection_refusal_probability: 0.0008,
    protection_malfunction_probability: 0.0004,
    line_status_detail: '正常运行',
    fault_rate_gain: 1.0
  }
];

// API 路由
app.get('/api/line-stations', (req, res) => {
  res.json(lineStations);
});

app.post('/api/line-stations', (req, res) => {
  const newStation = {
    id: String(lineStations.length + 1),
    ...req.body
  };
  lineStations.push(newStation);
  res.json(newStation);
});

app.put('/api/line-stations/:id', (req, res) => {
  const { id } = req.params;
  const index = lineStations.findIndex(station => station.id === id);
  if (index !== -1) {
    lineStations[index] = { ...lineStations[index], ...req.body };
    res.json(lineStations[index]);
  } else {
    res.status(404).json({ message: '线路不存在' });
  }
});

app.delete('/api/line-stations/:id', (req, res) => {
  const { id } = req.params;
  lineStations = lineStations.filter(station => station.id !== id);
  res.json({ message: '删除成功' });
});

// 导入
const upload = multer({ storage: multer.memoryStorage() });
app.post('/api/line-stations/import', upload.single('file'), async (req, res) => {
  if (!req.file) {
    return res.status(400).send('No file uploaded.');
  }

  try {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);
    const worksheet = workbook.worksheets[0];
    const importedData = [];
    const header = worksheet.getRow(1).values.slice(1);

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        const rowData = {};
        const rowValues = row.values.slice(1);
        header.forEach((key, index) => {
          rowData[key] = rowValues[index];
        });
        importedData.push(rowData);
      }
    });

    lineStations = importedData; // 简单替换
    res.status(200).json({ message: '导入成功', data: lineStations });
  } catch (error) {
    console.error('导入失败:', error);
    res.status(500).send('Error processing file.');
  }
});


// 导出
app.get('/api/line-stations/export', async (req, res) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Line Stations');

    if (lineStations.length > 0) {
      const headers = Object.keys(lineStations[0]);
      worksheet.getRow(1).values = headers;
      lineStations.forEach((station, index) => {
        const row = worksheet.getRow(index + 2);
        headers.forEach((header, i) => {
          row.getCell(i + 1).value = station[header];
        });
      });
    }

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=' + 'line-stations.xlsx'
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('导出失败:', error);
    res.status(500).send('Error generating file.');
  }
});


// 启动服务器
app.listen(port, () => {
  console.log(`后端服务运行在 http://localhost:${port}`);
}); 