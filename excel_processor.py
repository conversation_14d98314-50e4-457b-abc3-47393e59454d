import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import time
import os

def process_excel():
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 选择输入文件
    input_file = filedialog.askopenfilename(
        title="请选择电源明细表Excel文件",
        filetypes=[("Excel files", "*.xlsx")]
    )
    
    if not input_file:
        messagebox.showinfo("提示", "未选择文件，程序退出")
        return

    try:
        # 显示正在读取文件的提示
        messagebox.showinfo("提示", "正在读取Excel文件，请稍候...")
        
        print(f"开始读取文件: {input_file}")
        print("文件大小:", round(os.path.getsize(input_file) / (1024*1024), 2), "MB")
        
        start_time = time.time()
        print("开始读取时间:", time.strftime("%H:%M:%S"))
        
        # 读取Excel文件，添加性能优化参数
        df = pd.read_excel(
            input_file,
            sheet_name='Sheet1',
            engine='openpyxl',  # 使用openpyxl引擎
            dtype={
                '机组类型': str,
                '项目名称': str,
                '机组序号': str
            },  # 指定数据类型
            usecols=['机组类型', '项目名称', '机组序号'],  # 只读取需要的列
            na_filter=False  # 禁用NA过滤
        )
        
        end_time = time.time()
        print("读取完成时间:", time.strftime("%H:%M:%S"))
        print(f"读取耗时: {end_time - start_time:.2f} 秒")
        
        # 显示列名
        print("\n文件列名:")
        for col in df.columns:
            print(f"- {col}")
            
        # 检查必要的列是否存在
        required_columns = ['机组类型', '项目名称', '机组序号']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            error_msg = f"Excel文件缺少以下必要的列：\n{', '.join(missing_columns)}\n\n"
            error_msg += "请确保Excel文件包含以下列：\n"
            error_msg += "- 机组类型\n- 项目名称\n- 机组序号"
            messagebox.showerror("错误", error_msg)
            return
        
        # 显示读取完成提示
        messagebox.showinfo("提示", "文件读取完成，正在处理数据...")
        
        # 组合机组名称
        df['机组名称'] = df['机组类型'].astype(str) + '#' + df['项目名称'].astype(str) + '#' + df['机组序号'].astype(str)
        
        # 检查重复项
        duplicates = df[df['机组名称'].duplicated(keep=False)]
        
        if not duplicates.empty:
            duplicate_message = "发现以下重复的机组名称：\n\n"
            for name in duplicates['机组名称'].unique():
                duplicate_message += f"{name}\n"
            
            duplicate_message += "\n这些重复项可能会导致后续导入gopt时报错。是否要一键去重？"
            
            if messagebox.askyesno("重复项警告", duplicate_message):
                df = df.drop_duplicates(subset=['机组名称'], keep='first')
                messagebox.showinfo("提示", "已成功去重")
        
        # 选择输出文件
        output_file = filedialog.asksaveasfilename(
            title="请选择输出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")]
        )
        
        if not output_file:
            messagebox.showinfo("提示", "未选择输出文件，程序退出")
            return
        
        # 显示正在处理输出文件的提示
        messagebox.showinfo("提示", "正在处理输出文件，请稍候...")
        
        # 读取输出文件
        output_df = pd.read_excel(
            output_file,
            sheet_name='机组',
            engine='openpyxl',
            dtype={'机组名称': str},
            na_filter=False
        )
        
        # 将组合后的机组名称插入到输出文件的机组名称列
        output_df['机组名称'] = df['机组名称']
        
        # 保存结果
        output_df.to_excel(
            output_file,
            sheet_name='机组',
            index=False,
            engine='openpyxl'
        )
        messagebox.showinfo("成功", "处理完成！")
        
    except Exception as e:
        error_msg = f"处理过程中出现错误：\n{str(e)}\n\n"
        error_msg += "错误类型：" + type(e).__name__ + "\n\n"
        error_msg += "请检查：\n"
        error_msg += "1. Excel文件格式是否正确\n"
        error_msg += "2. 是否包含必要的列（机组类型、项目名称、机组序号）\n"
        error_msg += "3. 数据是否完整"
        messagebox.showerror("错误", error_msg)
        # 打印详细的错误信息到控制台
        print(f"错误详情：{str(e)}")
        print("错误类型:", type(e).__name__)

if __name__ == "__main__":
    process_excel() 