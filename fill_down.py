import openpyxl
from openpyxl.utils import get_column_letter
import re
import tkinter as tk
from tkinter import filedialog, messagebox

def fill_down_formula_machine_sheet():
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="请选择要处理的Excel文件",
        filetypes=[("Excel files", "*.xlsx")]
    )
    if not file_path:
        messagebox.showinfo("提示", "未选择文件，程序退出")
        return

    wb = openpyxl.load_workbook(file_path)
    ws = wb["机组"]  # sheet名如有不同请修改

    start_col = 9  # I列是第9列
    max_col = ws.max_column
    max_row = ws.max_row

    for col in range(start_col, max_col + 1):
        for row in range(2, max_row + 1):  # 假设第1行为表头
            cell = ws.cell(row=row, column=col)
            if (cell.value is None or str(cell.value).strip() == ""):
                # 查找上一行
                prev_cell = ws.cell(row=row-1, column=col)
                if prev_cell.value and isinstance(prev_cell.value, str) and prev_cell.value.startswith("="):
                    # 用正则替换公式中的所有行号
                    def replace_rownum(m):
                        col_letter = m.group(1)
                        # 保持$符号
                        return f"{col_letter}{row}"
                    # 匹配如G2、$H2等
                    new_formula = re.sub(r'(\$?[A-Z]+)\d+', replace_rownum, prev_cell.value)
                    cell.value = new_formula

    wb.save(file_path)
    messagebox.showinfo("成功", "公式向下填充并自动调整行号完成！")

if __name__ == "__main__":
    fill_down_formula_machine_sheet()
