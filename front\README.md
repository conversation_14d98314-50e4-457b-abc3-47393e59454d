# 电力系统数据处理平台

## 项目说明
本项目是一个基于Vue和Element UI的电力系统数据处理平台，用于管理节点和机组信息，以及水电三段式出力数据。

## 功能模块
1. **节点信息管理**：管理电力节点的基本信息和属性，支持导入导出Excel
2. **机组信息管理**：管理发电机组的详细信息和三段式曲线配置
3. **水电三段式处理**：以表格形式展示和编辑水电站的月度出力数据，包括平水年/枯水年和平均/预测/强迫出力等多种类型

## 启动方式
1. 使用npm启动（推荐）：
   ```
   npm run serve
   ```
   或双击 `启动前端.bat` 文件

2. 如果依赖安装有问题，可以使用CDN方式：
   双击 `start.bat` 文件

## 数据存储
本应用使用浏览器的localStorage存储数据，确保数据在页面刷新后不会丢失。

## 导入/导出功能
- 支持Excel格式的数据导入和导出
- 节点信息、三段式曲线数据和水电出力数据均可导入导出

## 技术栈
- Vue.js 2.x
- Element UI
- xlsx.js (Excel处理)
- FileSaver.js (文件下载)
