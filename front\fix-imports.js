// 修复组件导入问题的脚本
const fs = require('fs');
const path = require('path');

// 需要修复的导入映射
const importFixes = [
  {
    // UnitPrice 组件
    file: 'src/components/UnitPrice/index.vue',
    oldImport: "import { unitPriceService } from '@/services/unitPriceService';",
    newImport: "import * as unitPriceService from '@/services/unitPriceService';"
  },
  {
    // WindArea 组件
    file: 'src/components/WindArea/index.vue',
    oldImport: "import { windAreaService } from '@/services/windAreaService';",
    newImport: "import * as windAreaService from '@/services/windAreaService';"
  },
  {
    // WindStation 组件
    file: 'src/components/WindStation/index.vue',
    oldImport: "import { windStationService } from '@/services/windStationService';",
    newImport: "import * as windStationService from '@/services/windStationService';"
  },
  {
    // WindAreaCorrelation 组件
    file: 'src/components/WindAreaCorrelation/index.vue',
    oldImport: "import { windAreaCorrelationService } from '@/services/windAreaCorrelationService';",
    newImport: "import * as windAreaCorrelationService from '@/services/windAreaCorrelationService';"
  },
  {
    // SolarArea 组件
    file: 'src/components/SolarArea/index.vue',
    oldImport: "import { solarAreaService } from '@/services/solarAreaService';",
    newImport: "import * as solarAreaService from '@/services/solarAreaService';"
  },
  {
    // SolarStation 组件
    file: 'src/components/SolarStation/index.vue',
    oldImport: "import { solarStationService } from '@/services/solarStationService';",
    newImport: "import * as solarStationService from '@/services/solarStationService';"
  },
  {
    // SolarAreaCorrelation 组件
    file: 'src/components/SolarAreaCorrelation/index.vue',
    oldImport: "import { solarAreaCorrelationService } from '@/services/solarAreaCorrelationService';",
    newImport: "import * as solarAreaCorrelationService from '@/services/solarAreaCorrelationService';"
  },
  {
    // LineStation 组件
    file: 'src/components/LineStation/index.vue',
    oldImport: "import { lineStationService } from '@/services/lineStationService';",
    newImport: "import * as lineStationService from '@/services/lineStationService';"
  },
  {
    // LoadCurv 组件
    file: 'src/components/LoadCurv/index.vue',
    oldImport: "import loadCurvService from '@/services/loadCurvService';",
    newImport: "import * as loadCurvService from '@/services/loadCurvService';"
  },
  {
    // LoadModelingDataDialog 组件
    file: 'src/components/LoadCurv/LoadModelingDataDialog.vue',
    oldImport: "import loadModelingDataService from '@/services/loadModelingDataService';",
    newImport: "import * as loadModelingDataService from '@/services/loadModelingDataService';"
  },
  {
    // UnitSpecifiedOutput 组件
    file: 'src/components/UnitSpecifiedOutput/index.vue',
    oldImport: "import { unitSpecifiedOutputService } from '@/services/unitSpecifiedOutputService';",
    newImport: "import * as unitSpecifiedOutputService from '@/services/unitSpecifiedOutputService';"
  },
  {
    // UnitModelingDataDialog 组件
    file: 'src/components/UnitSpecifiedOutput/UnitModelingDataDialog.vue',
    oldImport: "import { unitModelingDataService } from '@/services/unitModelingDataService';",
    newImport: "import * as unitModelingDataService from '@/services/unitModelingDataService';"
  },
  {
    // SectionSpecifiedOutput 组件
    file: 'src/components/SectionSpecifiedOutput/index.vue',
    oldImport: "import sectionSpecifiedOutputService from '@/services/sectionSpecifiedOutputService';",
    newImport: "import * as sectionSpecifiedOutputService from '@/services/sectionSpecifiedOutputService';"
  }
];

// 执行修复
function fixImports() {
  let fixedCount = 0;
  let errorCount = 0;

  importFixes.forEach(fix => {
    const filePath = path.join(__dirname, fix.file);
    
    try {
      if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        
        if (content.includes(fix.oldImport)) {
          content = content.replace(fix.oldImport, fix.newImport);
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`✓ 修复: ${fix.file}`);
          fixedCount++;
        } else {
          console.log(`- 跳过: ${fix.file} (未找到目标导入语句)`);
        }
      } else {
        console.log(`! 文件不存在: ${fix.file}`);
      }
    } catch (error) {
      console.error(`✗ 错误: ${fix.file} - ${error.message}`);
      errorCount++;
    }
  });

  console.log(`\n修复完成: ${fixedCount} 个文件已修复, ${errorCount} 个错误`);
}

// 运行修复
fixImports();
