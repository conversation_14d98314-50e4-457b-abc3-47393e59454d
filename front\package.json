{"name": "power-system-simulation-platform", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "start": "vue-cli-service serve"}, "dependencies": {"axios": "^1.10.0", "core-js": "^3.8.3", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "vue": "^2.6.14", "vue-router": "^3.6.5", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"vue/no-deprecated-slot-attribute": "off", "vue/no-deprecated-slot-scope-attribute": "off", "vue/no-deprecated-v-bind-sync": "off", "vue/no-deprecated-v-on-native-modifier": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}