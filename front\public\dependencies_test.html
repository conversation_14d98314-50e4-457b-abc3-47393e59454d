<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>依赖测试 - 电力系统运行模拟平台</title>
  <!-- 引入Vue 2 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <!-- 引入Element UI样式 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/theme-chalk/index.css">
  <!-- 引入Element UI组件库 -->
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/index.js"></script>
  <!-- 引入中文语言包 -->
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/umd/locale/zh-CN.js"></script>
  <!-- 引入xlsx和file-saver -->
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
  <style>
    body {
      font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    }
    .test-result {
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
    }
    .success {
      background-color: #f0f9eb;
      border: 1px solid #e1f3d8;
      color: #67c23a;
    }
    .error {
      background-color: #fef0f0;
      border: 1px solid #fde2e2;
      color: #f56c6c;
    }
    .loading {
      text-align: center;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="container">
      <h1>依赖测试 - 电力系统运行模拟平台</h1>
      
      <el-divider content-position="left">测试结果</el-divider>
      
      <div v-if="loading" class="loading">
        <el-spinner></el-spinner>
        <p>正在测试依赖...</p>
      </div>
      
      <template v-else>
        <div v-for="(result, index) in testResults" :key="index" 
             class="test-result" :class="result.success ? 'success' : 'error'">
          <h3>{{ result.name }}</h3>
          <p>{{ result.message }}</p>
        </div>
        
        <el-divider></el-divider>
        
        <div style="text-align: center; margin-top: 20px;">
          <el-button @click="runTests" type="primary">重新测试</el-button>
          <el-button @click="openMainApp">返回主应用</el-button>
        </div>
      </template>
    </div>
  </div>

  <script>
    // 设置Element UI语言为中文
    ELEMENT.locale(ELEMENT.lang.zhCN);

    new Vue({
      el: '#app',
      data: {
        loading: true,
        testResults: []
      },
      mounted() {
        this.runTests();
      },
      methods: {
        runTests() {
          this.loading = true;
          this.testResults = [];
          
          // 给异步效果
          setTimeout(() => {
            // 检测Vue
            try {
              if (Vue && Vue.version.startsWith("2.")) {
                this.testResults.push({
                  name: "Vue",
                  success: true,
                  message: `Vue 2.x 加载成功 (版本: ${Vue.version})`
                });
              } else {
                this.testResults.push({
                  name: "Vue",
                  success: false,
                  message: `Vue版本不兼容: ${Vue.version || '未知'}`
                });
              }
            } catch (e) {
              this.testResults.push({
                name: "Vue",
                success: false,
                message: `加载失败: ${e.message}`
              });
            }
            
            // 检测Element UI
            try {
              if (ELEMENT) {
                this.testResults.push({
                  name: "Element UI",
                  success: true,
                  message: "Element UI加载成功"
                });
              }
            } catch (e) {
              this.testResults.push({
                name: "Element UI",
                success: false,
                message: `加载失败: ${e.message}`
              });
            }
            
            // 检测XLSX
            try {
              if (XLSX) {
                this.testResults.push({
                  name: "XLSX",
                  success: true,
                  message: "XLSX加载成功"
                });
              }
            } catch (e) {
              this.testResults.push({
                name: "XLSX",
                success: false,
                message: `加载失败: ${e.message}`
              });
            }
            
            // 检测FileSaver
            try {
              if (saveAs) {
                this.testResults.push({
                  name: "FileSaver",
                  success: true,
                  message: "FileSaver加载成功"
                });
              }
            } catch (e) {
              this.testResults.push({
                name: "FileSaver",
                success: false,
                message: `加载失败: ${e.message}`
              });
            }
            
            this.loading = false;
          }, 1000);
        },
        openMainApp() {
          window.location.href = "direct.html";
        }
      }
    });
  </script>
</body>
</html> 