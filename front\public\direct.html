<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电力系统运行模拟平台</title>
  <!-- 引入Vue 2 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <!-- 引入Element UI样式 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/theme-chalk/index.css">
  <!-- 引入Element UI组件库 -->
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/index.js"></script>
  <!-- 引入中文语言包 -->
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/umd/locale/zh-CN.js"></script>
  <!-- 引入xlsx和file-saver -->
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
    }
    .el-header {
      padding: 0 20px;
      background-color: #409EFF;
      color: white;
      line-height: 60px;
    }
    .el-aside {
      background-color: #545c64;
      color: white;
    }
    .el-menu {
      border-right: none;
    }
    .el-main {
      padding: 0;
      background-color: #f0f2f5;
    }
    .empty-content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }
  </style>
</head>
<body>
  <div id="app">
    <el-container style="height: 100vh;">
      <el-header style="background-color: #409EFF; color: white; display: flex; align-items: center; justify-content: space-between;">
        <span>调度运行仿真平台</span>
        <div>
          <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">首页</el-link>
          <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">个人中心</el-link>
          <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">系统配置</el-link>
          <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">退出登录</el-link>
        </div>
      </el-header>
      
      <!-- 主体部分 -->
      <el-container>
        <!-- 左侧菜单 -->
        <el-aside width="200px">
          <el-menu
            :default-active="activeIndex"
            class="el-menu-vertical-demo"
            background-color="#545c64"
            text-color="#fff"
            active-text-color="#ffd04b"
            @select="handleMenuSelect">
            
            <el-submenu index="1">
              <template slot="title">
                <i class="el-icon-document"></i>
                <span>基础数据</span>
              </template>
              <el-submenu index="1-1">
                <template slot="title">资源库</template>
                <el-menu-item-group>
                  <template slot="title">数据维护</template>
                  <el-menu-item index="1-1-1">节点信息</el-menu-item>
                  <el-menu-item index="1-1-2">机组信息</el-menu-item>
                </el-menu-item-group>
              </el-submenu>
            </el-submenu>
            
            <!-- 其他左侧菜单项 -->
            <el-menu-item index="2">
              <i class="el-icon-data-line"></i>
              <span>全景网架</span>
            </el-menu-item>
            <el-menu-item index="3">
              <i class="el-icon-folder"></i>
              <span>模型库</span>
            </el-menu-item>
            <el-menu-item index="4">
              <i class="el-icon-coin"></i>
              <span>资源库</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 右侧主内容区 -->
        <el-container>
          <!-- 运行模拟和结果展示的顶部横向标签页 -->
          <el-header height="50px" style="padding: 0;">
            <el-menu 
              :default-active="topActiveIndex" 
              mode="horizontal" 
              style="border-bottom: none;">
              <el-menu-item index="5">运行模拟</el-menu-item>
              <el-menu-item index="6">结果展示</el-menu-item>
              <el-menu-item index="7">方案对比</el-menu-item>
            </el-menu>
          </el-header>
          
          <!-- 主内容区 -->
          <el-main>
            <div class="empty-content">
              <el-card style="width: 400px; text-align: center;">
                <div slot="header">
                  <span>电力系统运行模拟平台</span>
                </div>
                <el-button type="primary" @click="showMessage">测试按钮</el-button>
                <p>Element UI 和 Vue 2 运行正常！</p>
                <p>请使用 npm run serve 启动完整应用</p>
              </el-card>
            </div>
          </el-main>
        </el-container>
      </el-container>
    </el-container>
  </div>

  <script>
    // 设置Element UI语言为中文
    ELEMENT.locale(ELEMENT.lang.zhCN);

    new Vue({
      el: '#app',
      data: {
        activeIndex: '1-1-1',
        topActiveIndex: '5',
        currentComponent: 'NodeInfo'
      },
      methods: {
        showMessage() {
          this.$message.success('Element UI 和 Vue 2 运行正常！');
        },
        handleMenuSelect(index) {
          this.activeIndex = index;
        }
      }
    });
  </script>
</body>
</html> 