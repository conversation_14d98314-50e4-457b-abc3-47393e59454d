<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>XLSX和FileSaver测试</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
</head>
<body>
  <h1>XLSX和FileSaver库测试</h1>
  <button id="testExport">测试导出Excel</button>
  <input type="file" id="testImport" accept=".xlsx, .xls">
  <div id="result"></div>

  <script>
    document.getElementById('testExport').addEventListener('click', function() {
      // 创建测试数据
      const data = [
        { "名称": "测试1", "值": 100 },
        { "名称": "测试2", "值": 200 }
      ];

      try {
        // 创建工作簿和工作表
        const worksheet = XLSX.utils.json_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "测试数据");

        // 导出Excel文件
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
        saveAs(blob, '测试文件.xlsx');

        document.getElementById('result').innerHTML = "导出成功!";
      } catch (error) {
        document.getElementById('result').innerHTML = "导出失败: " + error.message;
        console.error(error);
      }
    });

    document.getElementById('testImport').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const data = e.target.result;
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          document.getElementById('result').innerHTML = 
            "导入成功! 数据: " + JSON.stringify(jsonData);
        } catch (error) {
          document.getElementById('result').innerHTML = "导入失败: " + error.message;
          console.error(error);
        }
      };
      reader.readAsArrayBuffer(file);
    });
  </script>
</body>
</html> 