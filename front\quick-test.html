<!DOCTYPE html>
<html>
<head>
    <title>快速导入测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h2>快速导入测试</h2>
    <input type="file" id="fileInput" accept=".xlsx,.xls" />
    <button onclick="testImport()">测试导入</button>
    <div id="results"></div>

    <script>
        function testImport() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    let results = '<h3>Excel文件分析结果：</h3>';
                    results += `<p>工作表数量: ${workbook.SheetNames.length}</p>`;
                    results += '<ul>';
                    
                    workbook.SheetNames.forEach(sheetName => {
                        const worksheet = workbook.Sheets[sheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet);
                        
                        results += `<li><strong>${sheetName}</strong>: ${jsonData.length} 行数据`;
                        
                        if (jsonData.length > 0) {
                            results += '<br>列名: ' + Object.keys(jsonData[0]).join(', ');
                            results += '<br>第一行数据: ' + JSON.stringify(jsonData[0]);
                        }
                        results += '</li><br>';
                    });
                    
                    results += '</ul>';
                    document.getElementById('results').innerHTML = results;
                    
                } catch (error) {
                    document.getElementById('results').innerHTML = `<p style="color: red;">解析失败: ${error.message}</p>`;
                }
            };
            
            reader.readAsArrayBuffer(file);
        }
    </script>
</body>
</html>
