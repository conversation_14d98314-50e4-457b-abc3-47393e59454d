<!-- eslint-disable -->
<template>
  <div id="app">
    <el-container>
      <el-aside width="200px">
        <el-menu
          :router="true"
          :default-active="activeIndex"
          class="el-menu-vertical"
        >
          <el-menu-item index="/unit-status">
            <i class="el-icon-monitor"></i>
            <span slot="title">机组状态</span>
          </el-menu-item>
          <el-menu-item index="/unit-price">
            <i class="el-icon-money"></i>
            <span slot="title">机组电价</span>
          </el-menu-item>
          <el-menu-item index="/unit-specified-output">
            <i class="el-icon-s-data"></i>
            <span slot="title">机组指定出力</span>
          </el-menu-item>
          <el-submenu index="solar">
            <template slot="title">
              <i class="el-icon-sunny"></i>
              <span>光伏信息</span>
            </template>
            <el-menu-item index="/solar-area">
              <i class="el-icon-collection-tag"></i>
              <span slot="title">光区信息</span>
            </el-menu-item>
          <el-menu-item index="/solar-station">
              <i class="el-icon-place"></i>
            <span slot="title">光伏电站</span>
          </el-menu-item>
            <el-menu-item index="/solar-area-correlation">
              <i class="el-icon-link"></i>
              <span slot="title">光区之间相关系数</span>
            </el-menu-item>
          </el-submenu>
          <el-submenu index="wind">
            <template slot="title">
              <i class="el-icon-wind-power"></i>
              <span>风电信息</span>
            </template>
            <el-menu-item index="/wind-area">
              <i class="el-icon-collection-tag"></i>
              <span slot="title">风区信息</span>
          </el-menu-item>
          <el-menu-item index="/wind-station">
              <i class="el-icon-place"></i>
              <span slot="title">风电场</span>
            </el-menu-item>
            <el-menu-item index="/wind-area-correlation">
              <i class="el-icon-link"></i>
              <span slot="title">风区之间相关系数</span>
          </el-menu-item>
          </el-submenu>
          <el-menu-item index="/line-station">
            <i class="el-icon-connection"></i>
            <span slot="title">线路管理</span>
          </el-menu-item>
          <el-submenu index="1">
            <template slot="title">
              <i class="el-icon-setting"></i>
              <span>节点信息</span>
            </template>
            <el-menu-item index="/node-info">
              <i class="el-icon-document"></i>
              <span slot="title">节点</span>
            </el-menu-item>
          </el-submenu>
          <el-submenu index="2">
            <template slot="title">
              <i class="el-icon-setting"></i>
              <span>机组信息</span>
            </template>
            <el-menu-item index="/unit-info">
              <i class="el-icon-document"></i>
              <span slot="title">机组</span>
            </el-menu-item>
            <el-menu-item index="/hydropower-output">
              <i class="el-icon-document"></i>
              <span slot="title">水电三段式出力</span>
            </el-menu-item>
          </el-submenu>
          <el-submenu index="6">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>负荷信息</span>
            </template>
            <el-menu-item-group>
              <el-menu-item index="/load-curv">负荷曲线</el-menu-item>
            </el-menu-item-group>
          </el-submenu>
          <el-submenu index="8">
            <template slot="title">
              <i class="el-icon-setting"></i>
              <span>线路断面信息</span>
            </template>
            <el-menu-item index="/line-station">
              <i class="el-icon-document"></i>
              <span slot="title">线路</span>
            </el-menu-item>
            <el-menu-item index="/section">
              <i class="el-icon-document"></i>
              <span slot="title">断面</span>
            </el-menu-item>
            <el-menu-item index="/section-line-relation">
              <i class="el-icon-document"></i>
              <span slot="title">断面线路包含关系</span>
            </el-menu-item>
            <el-menu-item index="/section-specified-output">
              <i class="el-icon-document"></i>
              <span slot="title">断面指定出力</span>
            </el-menu-item>
          </el-submenu>
          <el-divider></el-divider>
          <el-menu-item index="/service-test">
            <i class="el-icon-setting"></i>
            <span slot="title">服务测试</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-container>
        <el-header style="display: flex; justify-content: space-between; align-items: center; background-color: #fff; border-bottom: 1px solid #dcdfe6;">
          <div>
            <el-button size="mini" @click="showDebugPanel = !showDebugPanel"
                       :type="showDebugPanel ? 'warning' : 'info'">
              {{ showDebugPanel ? '隐藏调试' : '显示调试' }}
            </el-button>
          </div>
          <div>
            <el-button type="primary" @click="handleFullImport" icon="el-icon-upload">全量导入</el-button>
          </div>
        </el-header>
        <el-main>
          <router-view></router-view>
        </el-main>
      </el-container>
    </el-container>
    <input type="file" ref="fileInput" @change="onFileSelected" style="display: none" accept=".xlsx, .xls" />

    <!-- 调试面板 -->
    <DebugPanel v-if="showDebugPanel" />
  </div>
</template>

<script>
import fullImportService from '@/services/fullImportService';
import DebugPanel from '@/components/DebugPanel.vue';

export default {
  name: 'App',
  components: {
    DebugPanel
  },
  data() {
    return {
      defaultPath: '/unit-status',
      showDebugPanel: true  // 默认显示调试面板
    }
  },
  computed: {
    activeIndex() {
      return this.$route ? this.$route.path : this.defaultPath
    }
  },
  created() {
    if (this.$route.path === '/') {
      this.$router.push(this.defaultPath)
    }
  },
  methods: {
    handleFullImport() {
      this.$confirm('此操作将清空所有现有数据并从文件导入，是否继续？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.fileInput.click();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导入'
        });
      });
    },
    async onFileSelected(event) {
      const file = event.target.files[0];
      if (!file) {
        return;
      }

      const loading = this.$loading({
        lock: true,
        text: '正在准备导入...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const progressCallback = (text) => {
        loading.text = text;
      };

      try {
        const result = await fullImportService.importAllDataFromFile(file, progressCallback);
        loading.close();
        this.$alert(result.details, result.message, {
          confirmButtonText: '确定',
          callback: () => {
            window.location.reload();
          }
        });
      } catch (error) {
        loading.close();
        this.$message.error(`导入失败: ${error.message}`);
      } finally {
        this.$refs.fileInput.value = '';
      }
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #304156;
}

.el-menu {
  border-right: none;
}

.el-menu-vertical {
  height: 100%;
}

.el-main {
  padding: 0;
  background-color: #f0f2f5;
}
</style>