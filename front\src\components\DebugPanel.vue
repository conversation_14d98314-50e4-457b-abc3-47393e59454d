<template>
  <div class="debug-panel">
    <el-card class="debug-card">
      <div slot="header" class="debug-header">
        <span>调试面板</span>
        <div class="debug-actions">
          <el-button size="mini" @click="testAllServices">测试所有服务</el-button>
          <el-button size="mini" @click="getServiceReport">服务状态报告</el-button>
          <el-button size="mini" @click="clearLogs">清空日志</el-button>
          <el-button size="mini" type="primary" @click="visible = !visible">
            {{ visible ? '隐藏' : '显示' }}
          </el-button>
        </div>
      </div>
      
      <div v-show="visible" class="debug-content">
        <!-- 服务状态概览 -->
        <el-collapse v-model="activeNames">
          <el-collapse-item title="服务状态概览" name="1">
            <div v-if="serviceReport">
              <p><strong>总服务数:</strong> {{ serviceReport.totalServices }}</p>
              <p><strong>正常服务:</strong> {{ serviceReport.workingServices }}</p>
              <p><strong>问题服务:</strong> {{ serviceReport.problemServices.length }}</p>
              
              <el-table :data="serviceReport.summary" size="mini" style="margin-top: 10px;">
                <el-table-column prop="name" label="服务名称" width="200"></el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 'OK' ? 'success' : 'danger'" size="mini">
                      {{ scope.row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="errors" label="错误信息">
                  <template slot-scope="scope">
                    <div v-if="scope.row.errors.length > 0">
                      <div v-for="error in scope.row.errors" :key="error" class="error-text">
                        {{ error }}
                      </div>
                    </div>
                    <span v-else class="success-text">正常</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else>
              <p>点击"服务状态报告"按钮获取服务状态</p>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="调试日志" name="2">
            <div class="log-container">
              <div v-for="(log, index) in debugLogs" :key="index" 
                   :class="['log-entry', `log-${log.level.toLowerCase()}`]">
                <div class="log-header">
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                  <span :class="['log-level', `level-${log.level.toLowerCase()}`]">{{ log.level }}</span>
                </div>
                <div class="log-message">{{ log.message }}</div>
                <div v-if="log.data" class="log-data">
                  <pre>{{ log.data }}</pre>
                </div>
              </div>
              <div v-if="debugLogs.length === 0" class="no-logs">
                暂无调试日志
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="单个服务测试" name="3">
            <div class="service-test">
              <el-select v-model="selectedService" placeholder="选择要测试的服务" style="width: 200px;">
                <el-option
                  v-for="serviceName in Object.keys(sheetServiceMap)"
                  :key="serviceName"
                  :label="serviceName"
                  :value="serviceName">
                </el-option>
              </el-select>
              <el-button @click="testSingleService" :disabled="!selectedService" style="margin-left: 10px;">
                测试服务
              </el-button>
            </div>
            
            <div v-if="singleTestResult" class="test-result">
              <h4>{{ singleTestResult.serviceName }} 测试结果:</h4>
              <p><strong>clearAll方法:</strong> 
                <el-tag :type="singleTestResult.clearAllWorks ? 'success' : 'danger'" size="mini">
                  {{ singleTestResult.clearAllWorks ? '正常' : '异常' }}
                </el-tag>
              </p>
              <p><strong>batchImport方法:</strong> 
                <el-tag :type="singleTestResult.batchImportWorks ? 'success' : 'danger'" size="mini">
                  {{ singleTestResult.batchImportWorks ? '正常' : '异常' }}
                </el-tag>
              </p>
              <div v-if="singleTestResult.errors.length > 0">
                <h5>错误信息:</h5>
                <ul>
                  <li v-for="error in singleTestResult.errors" :key="error" class="error-text">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script>
import fullImportService from '@/services/fullImportService';

export default {
  name: 'DebugPanel',
  data() {
    return {
      visible: false,
      activeNames: ['1'],
      debugLogs: [],
      serviceReport: null,
      selectedService: '',
      singleTestResult: null,
      sheetServiceMap: fullImportService.sheetServiceMap
    };
  },
  methods: {
    async testAllServices() {
      try {
        this.$message.info('正在测试所有服务...');
        const results = await fullImportService.testAllServices();
        this.debugLogs = fullImportService.getDebugLogs();
        this.$message.success('服务测试完成');
      } catch (error) {
        this.$message.error(`测试失败: ${error.message}`);
      }
    },
    
    async getServiceReport() {
      try {
        this.serviceReport = await fullImportService.getServiceStatusReport();
        this.$message.success('服务状态报告已生成');
      } catch (error) {
        this.$message.error(`获取报告失败: ${error.message}`);
      }
    },
    
    async testSingleService() {
      if (!this.selectedService) return;
      
      try {
        this.singleTestResult = await fullImportService.testSingleService(this.selectedService);
        this.debugLogs = fullImportService.getDebugLogs();
        this.$message.success(`${this.selectedService} 测试完成`);
      } catch (error) {
        this.$message.error(`测试失败: ${error.message}`);
      }
    },
    
    clearLogs() {
      fullImportService.clearDebugLogs();
      this.debugLogs = [];
      this.$message.success('日志已清空');
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    }
  }
};
</script>

<style scoped>
.debug-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 600px;
  max-height: 80vh;
  z-index: 9999;
  overflow-y: auto;
}

.debug-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-actions {
  display: flex;
  gap: 5px;
}

.debug-content {
  max-height: 60vh;
  overflow-y: auto;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  background: #f9f9f9;
}

.log-entry {
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #ccc;
}

.log-entry.log-info {
  border-left-color: #409eff;
  background: #f0f9ff;
}

.log-entry.log-success {
  border-left-color: #67c23a;
  background: #f0f9ff;
}

.log-entry.log-warn {
  border-left-color: #e6a23c;
  background: #fdf6ec;
}

.log-entry.log-error {
  border-left-color: #f56c6c;
  background: #fef0f0;
}

.log-entry.log-fatal {
  border-left-color: #f56c6c;
  background: #fef0f0;
  font-weight: bold;
}

.log-header {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 4px;
}

.log-time {
  color: #666;
}

.log-level {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.level-info { background: #409eff; color: white; }
.level-success { background: #67c23a; color: white; }
.level-warn { background: #e6a23c; color: white; }
.level-error { background: #f56c6c; color: white; }
.level-fatal { background: #f56c6c; color: white; }

.log-message {
  font-weight: 500;
  margin-bottom: 4px;
}

.log-data {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 3px;
  font-size: 11px;
}

.log-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 20px;
}

.service-test {
  margin-bottom: 15px;
}

.test-result {
  margin-top: 15px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
}

.success-text {
  color: #67c23a;
  font-size: 12px;
}
</style>
