<template>
  <el-table
    :data="data"
    border
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" fixed align="center"></el-table-column>
    
    <el-table-column label="电厂名称" prop="powerPlantName" fixed width="180">
      <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.powerPlantName" @input="handleInput(scope.$index, 'powerPlantName', $event)"></el-input>
        <span v-else>{{ scope.row.powerPlantName }}</span>
      </template>
    </el-table-column>

    <el-table-column label="起始时间" prop="startTime" width="120">
       <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.startTime" @input="handleInput(scope.$index, 'startTime', $event)"></el-input>
        <span v-else>{{ scope.row.startTime }}</span>
      </template>
    </el-table-column>

    <el-table-column label="输入类型A" prop="inputTypeA" width="120">
       <template slot-scope="scope">
        <el-select v-if="isEditing" :value="scope.row.inputTypeA" @change="handleInput(scope.$index, 'inputTypeA', $event)">
            <el-option label="平水年" value="平水年"></el-option>
            <el-option label="枯水年" value="枯水年"></el-option>
        </el-select>
        <span v-else>{{ scope.row.inputTypeA }}</span>
      </template>
    </el-table-column>

    <el-table-column label="输入类型B" prop="inputTypeB" width="120">
       <template slot-scope="scope">
        <el-select v-if="isEditing" :value="scope.row.inputTypeB" @change="handleInput(scope.$index, 'inputTypeB', $event)">
            <el-option label="平均出力" value="平均出力"></el-option>
            <el-option label="预想出力" value="预想出力"></el-option>
            <el-option label="强迫出力" value="强迫出力"></el-option>
        </el-select>
        <span v-else>{{ scope.row.inputTypeB }}</span>
      </template>
    </el-table-column>

    <el-table-column v-for="month in 12" :key="month" :label="month + '月'" :prop="'month' + month" width="100">
        <template slot-scope="scope">
            <el-input-number v-if="isEditing" controls-position="right" style="width: 100%" :value="scope.row['month' + month]" @change="handleInput(scope.$index, 'month' + month, $event)"></el-input-number>
            <span v-else>{{ scope.row['month' + month] }}</span>
        </template>
    </el-table-column>

  </el-table>
</template>

<script>
export default {
  name: 'HydropowerDataTable',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    isEditing: {
        type: Boolean,
        default: false,
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    handleInput(index, key, value) {
        // This is a more direct way to handle data changes for inline editing.
        // But it requires the parent to manage the data state.
        // An alternative is to manage a local copy and emit a save event.
        // For simplicity with the current structure, we emit the change to the parent.
        this.$emit('data-change', { index, key, value });
    }
  },
};
</script>
<style scoped>
.el-input-number {
    width: 100%;
}
</style> 