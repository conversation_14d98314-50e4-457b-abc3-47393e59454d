<template>
  <div class="toolbar-container">
    <div class="buttons-left">
      <!-- Editing Mode Buttons -->
      <template v-if="isEditing">
        <el-button type="primary" icon="el-icon-check" @click="$emit('save')">保存</el-button>
        <el-button icon="el-icon-close" @click="$emit('cancel')">取消修改</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="$emit('add')">添加行</el-button>
      </template>
      
      <!-- Normal Mode Buttons -->
      <template v-else>
        <el-button type="primary" icon="el-icon-edit" @click="$emit('edit')">修改</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="$emit('delete')">删除</el-button>
        <el-button type="success" icon="el-icon-upload2" @click="$emit('import')">导入Excel</el-button>
        <el-button type="warning" icon="el-icon-download" @click="$emit('export')">导出Excel</el-button>
      </template>

    </div>
    <div class="search-right" v-if="!isEditing">
       <el-form :inline="true" :model="searchForm" @submit.native.prevent>
         <el-form-item label="电厂名称">
           <el-input v-model="searchForm.powerPlantName" placeholder="请输入电厂名称" @keyup.enter.native="handleSearch"></el-input>
         </el-form-item>
         <el-form-item>
           <el-button type="primary" @click="handleSearch">查询</el-button>
           <el-button @click="handleReset">重置</el-button>
         </el-form-item>
       </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HydropowerToolbar',
  props: {
      isEditing: {
          type: Boolean,
          default: false,
      }
  },
  data() {
    return {
      searchForm: {
        powerPlantName: '',
      },
    };
  },
  methods: {
      handleSearch() {
          this.$emit('search', this.searchForm.powerPlantName);
      },
      handleReset() {
          this.searchForm.powerPlantName = '';
          this.$emit('search', '');
      }
  }
};
</script>

<style scoped>
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.buttons-left > .el-button {
    margin-right: 10px;
}
.search-right {
    display: flex;
}
</style> 