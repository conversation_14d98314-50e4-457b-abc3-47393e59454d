<template>
  <div class="hydropower-output-container">
    <HydropowerToolbar 
      :is-editing="isEditing"
      @add="handleAdd" 
      @edit="handleEdit"
      @cancel="handleCancel"
      @save="handleSave"
      @delete="handleDelete" 
      @import="handleImport" 
      @export="handleExport"
      @search="handleSearch"
    />
    <HydropowerDataTable 
      ref="dataTable" 
      :data="filteredData" 
      :is-editing="isEditing"
      @selection-change="handleSelectionChange" 
      @data-change="handleDataChange"
    />
  </div>
</template>

<script>
import HydropowerToolbar from './HydropowerToolbar.vue';
import HydropowerDataTable from './HydropowerDataTable.vue';
import * as hydropowerOutputService from '../../services/hydropowerOutputService';
import { deepClone } from '../../utils/deepClone'; // Assuming a deepClone utility

export default {
  name: 'HydropowerOutput',
  components: {
    HydropowerToolbar,
    HydropowerDataTable,
  },
  data() {
    return {
      tableData: [],
      filteredData: [],
      originalData: [], // For cancel functionality
      isEditing: false,
      selectedRows: [],
      searchQuery: '',
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.tableData = hydropowerOutputService.getHydropowerOutputList();
      this.handleSearch(this.searchQuery);
    },
    handleSearch(query) {
      this.searchQuery = query;
      if (!query) {
        this.filteredData = this.tableData;
      } else {
        this.filteredData = this.tableData.filter(item => 
          item.powerPlantName && item.powerPlantName.toLowerCase().includes(query.toLowerCase())
        );
      }
    },
    handleAdd() {
      if (!this.isEditing) {
        this.handleEdit(); // Enter edit mode first
      }
      const newRecord = hydropowerOutputService.createEmptyRecord();
      this.filteredData.unshift(newRecord);
    },
    handleEdit() {
      this.isEditing = true;
      this.originalData = deepClone(this.filteredData);
    },
    handleCancel() {
        this.isEditing = false;
        // Find which items are new (don't exist in originalData)
        const newItems = this.filteredData.filter(item => !this.originalData.some(orig => orig.id === item.id));
        
        // Restore original data
        this.filteredData = deepClone(this.originalData);
        
        // If there were new items that are now cancelled, we need to refresh the main tableData as well
        if (newItems.length > 0) {
            this.tableData = this.tableData.filter(item => !newItems.some(newItem => newItem.id === item.id));
        }

        this.$message.info('已取消修改');
    },
    handleSave() {
        hydropowerOutputService.batchSaveHydropowerOutput(this.filteredData);
        this.tableData = hydropowerOutputService.getHydropowerOutputList();
        this.handleSearch(this.searchQuery);
        this.isEditing = false;
        this.$message.success('保存成功!');
    },
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据进行删除');
        return;
      }
      this.$confirm('此操作将永久删除所选数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        hydropowerOutputService.deleteHydropowerOutputByIds(ids);
        this.fetchData();
        this.$message.success('删除成功!');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleDataChange({index, key, value}) {
        // This is for inline editing. The child component will emit changes.
        if (this.filteredData[index]) {
            this.filteredData[index][key] = value;
        }
    },
    handleImport() {
        this.$message.info('导入功能待实现');
    },
    handleExport() {
        this.$message.info('导出功能待实现');
    },
  },
};
</script>

<style scoped>
.hydropower-output-container {
  padding: 20px;
}
</style> 