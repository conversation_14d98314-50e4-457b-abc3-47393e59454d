<template>
  <div class="line-data-table">
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="线路名称" min-width="120" />
      <el-table-column prop="startDate" label="起始日期" width="100" />
      <el-table-column prop="endDate" label="终止如期" width="100" />
      <el-table-column prop="startStation" label="起始节点" width="100" />
      <el-table-column prop="endStation" label="终止节点" width="100" />
      <el-table-column prop="region" label="线路所属区域" width="120" />
      <el-table-column prop="capacity" label="线路设计容量(MW)" width="150" />
      <el-table-column prop="type" label="线路类型" width="100" />
      <el-table-column prop="status" label="计划类型" width="100" />
      <el-table-column prop="length" label="线路长度(km)" width="120" />
      <el-table-column prop="resistance" label="电阻R" width="100" />
      <el-table-column prop="reactance" label="电抗X" width="100" />
      <el-table-column prop="susceptance" label="1/2充电电容" width="120" />
      <el-table-column prop="thermalLimit" label="稳态热极限(MW)"></el-table-column>

      <!-- 新增的隐藏列 -->
      <el-table-column v-if="showAdvancedColumns" prop="investment_cost" label="投资(万元)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="payback_period" label="回收期(年)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="trip_rate" label="线路跳闸率(次/年/百千米)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="fault_recovery_time" label="线路故障恢复时间(h)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="reclosing_success_rate" label="重合闸成功率"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="transient_stability_limit" label="线路暂态安全稳定极限(MW)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="relay_protection_time_avg" label="一段继电保护平均动作时间(秒)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="relay_protection_time_stddev" label="一段继电保护动作时间的标准差(秒)"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="distance_relay_protection_setting" label="线路三段距离继电保护整定值"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="protection_refusal_probability" label="保护拒动概率"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="protection_malfunction_probability" label="保护误动概率"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="line_status_detail" label="线路状态"></el-table-column>
      <el-table-column v-if="showAdvancedColumns" prop="fault_rate_gain" label="线路故障率增益"></el-table-column>
      <el-table-column fixed="right" label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="$emit('edit-node', scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="$emit('delete-node', scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'LineDataTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    showAdvancedColumns: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // The original script block was empty, so we'll keep it empty.
    // If there were methods here, they would be added.
  }
}
</script>

<style scoped>
.line-data-table {
  margin-top: 20px;
}

.el-table {
  margin-bottom: 20px;
}

.el-button + .el-button {
  margin-left: 5px;
}
</style> 