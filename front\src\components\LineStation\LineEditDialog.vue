<template>
  <el-dialog
    :title="editMode === 'add' ? '添加线路' : '编辑线路'"
    :visible.sync="dialogVisible"
    width="650px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="线路名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入线路名称"></el-input>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起始日期" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="选择日期"
              format="yyyyMMdd"
              value-format="yyyyMMdd"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终止日期" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              placeholder="选择日期"
              format="yyyyMMdd"
              value-format="yyyyMMdd"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="起始节点" prop="startStation">
            <el-input v-model="form.startStation" placeholder="请输入起始节点"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终止节点" prop="endStation">
            <el-input v-model="form.endStation" placeholder="请输入终止节点"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="线路所属区域" prop="region">
        <el-input v-model="form.region" placeholder="请输入所属区域"></el-input>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="线路设计容量" prop="capacity">
            <el-input-number 
              v-model="form.capacity" 
              :min="0"
              :precision="0"
              placeholder="请输入容量(MW)"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="线路类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择线路类型">
              <el-option label="330kV线路" value="330kV线路" />
              <el-option label="500kV线路" value="500kV线路" />
              <el-option label="750kV线路" value="750kV线路" />
              <el-option label="1000kV线路" value="1000kV线路" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划类型" prop="status">
            <el-select v-model="form.status" placeholder="请选择计划类型">
              <el-option label="已投建" value="已投建" />
              <el-option label="在建" value="在建" />
              <el-option label="规划" value="规划" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="线路长度" prop="length">
            <el-input-number 
              v-model="form.length" 
              :min="0" 
              :precision="2"
              :step="0.1"
              placeholder="请输入长度(km)"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="电阻R" prop="resistance">
            <el-input-number 
              v-model="form.resistance" 
              :min="0" 
              :precision="4"
              :step="0.0001"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="电抗X" prop="reactance">
            <el-input-number 
              v-model="form.reactance" 
              :min="0" 
              :precision="4"
              :step="0.0001"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="1/2充电电容" prop="susceptance">
            <el-input-number 
              v-model="form.susceptance" 
              :min="0" 
              :precision="6"
              :step="0.000001"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="热稳定限值" prop="thermalLimit">
        <el-input-number 
          v-model="form.thermalLimit" 
          :min="0"
          :precision="0"
          placeholder="请输入热稳定限值(MW)"
        ></el-input-number>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="投资(万元)" prop="investment_cost">
            <el-input v-model.number="form.investment_cost" type="number" placeholder="请输入投资金额"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="回收期(年)" prop="payback_period">
            <el-input v-model.number="form.payback_period" type="number" placeholder="请输入回收期"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="线路跳闸率" prop="trip_rate">
            <el-input v-model.number="form.trip_rate" type="number" placeholder="次/年/百千米"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="故障恢复时间" prop="fault_recovery_time">
            <el-input v-model.number="form.fault_recovery_time" type="number" placeholder="小时"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重合闸成功率" prop="reclosing_success_rate">
            <el-input v-model.number="form.reclosing_success_rate" type="number" placeholder="请输入成功率"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="暂态稳定极限" prop="transient_stability_limit">
            <el-input v-model.number="form.transient_stability_limit" type="number" placeholder="MW"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="继电保护平均动作时间" prop="relay_protection_time_avg">
            <el-input v-model.number="form.relay_protection_time_avg" type="number" placeholder="秒"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="继电保护动作时间标准差" prop="relay_protection_time_stddev">
            <el-input v-model.number="form.relay_protection_time_stddev" type="number" placeholder="秒"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="三段距离继电保护整定值" prop="distance_relay_protection_setting">
            <el-input v-model="form.distance_relay_protection_setting" placeholder="请输入整定值"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="保护拒动概率" prop="protection_refusal_probability">
            <el-input v-model.number="form.protection_refusal_probability" type="number" placeholder="请输入概率"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护误动概率" prop="protection_malfunction_probability">
            <el-input v-model.number="form.protection_malfunction_probability" type="number" placeholder="请输入概率"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="线路故障率增益" prop="fault_rate_gain">
            <el-input v-model.number="form.fault_rate_gain" type="number" placeholder="请输入增益"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="线路详细状态" prop="line_status_detail">
        <el-input v-model="form.line_status_detail" placeholder="请输入线路详细状态"></el-input>
      </el-form-item>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'LineEditDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    editMode: {
      type: String,
      default: 'add'
    },
    currentNode: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        name: '',
        startDate: '',
        endDate: '',
        startStation: '',
        endStation: '',
        region: '',
        capacity: 0,
        type: '',
        status: '',
        length: 0,
        resistance: 0,
        reactance: 0,
        susceptance: 0,
        thermalLimit: 0,
        investment_cost: 0,
        payback_period: 0,
        trip_rate: 0,
        fault_recovery_time: 0,
        reclosing_success_rate: 0,
        transient_stability_limit: 0,
        relay_protection_time_avg: 0,
        relay_protection_time_stddev: 0,
        distance_relay_protection_setting: '',
        protection_refusal_probability: 0,
        protection_malfunction_probability: 0,
        fault_rate_gain: 0,
        line_status_detail: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入线路名称', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择终止日期', trigger: 'change' }
        ],
        startStation: [
          { required: true, message: '请输入起始节点', trigger: 'blur' }
        ],
        endStation: [
          { required: true, message: '请输入终止节点', trigger: 'blur' }
        ],
        region: [
          { required: true, message: '请输入所属区域', trigger: 'blur' }
        ],
        capacity: [
          { required: true, message: '请输入线路设计容量', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择线路类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择计划类型', trigger: 'change' }
        ],
        length: [
          { required: true, message: '请输入线路长度', trigger: 'blur' }
        ],
        thermalLimit: [{ required: true, message: '请输入稳态热极限', trigger: 'blur' }],
        investment_cost: [{ type: 'number', message: '投资必须为数字'}],
        payback_period: [{ type: 'number', message: '回收期必须为数字'}],
        trip_rate: [{ type: 'number', message: '跳闸率必须为数字'}],
        fault_recovery_time: [{ type: 'number', message: '故障恢复时间必须为数字'}],
        reclosing_success_rate: [{ type: 'number', message: '重合闸成功率必须为数字'}],
        transient_stability_limit: [{ type: 'number', message: '暂态稳定极限必须为数字'}],
        relay_protection_time_avg: [{ type: 'number', message: '平均动作时间必须为数字'}],
        relay_protection_time_stddev: [{ type: 'number', message: '标准差必须为数字'}],
        protection_refusal_probability: [{ type: 'number', message: '拒动概率必须为数字'}],
        protection_malfunction_probability: [{ type: 'number', message: '误动概率必须为数字'}],
        fault_rate_gain: [{ type: 'number', message: '故障率增益必须为数字'}],
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    currentNode: {
      handler(val) {
        if (val) {
          this.form = { ...val }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('confirm', this.form)
        }
      })
    }
  }
}
</script>

<style scoped>
.el-input-number {
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-date-picker {
  width: 100%;
}
</style> 