<template>
  <div class="line-toolbar">
    <div class="left-buttons">
      <el-button type="primary" @click="$emit('add-node')">
        <i class="el-icon-plus"></i> 新增线路
      </el-button>
      <el-button type="primary" @click="handleExportExcel">
        <i class="el-icon-download"></i> 导出Excel
      </el-button>
      <el-button type="primary" @click="handleImportExcel">
        <i class="el-icon-upload2"></i> 导入Excel
      </el-button>
      <el-button type="info" plain icon="el-icon-s-operation" @click="toggleAdvancedColumns">{{ showAdvanced ? '隐藏高级列' : '显示高级列' }}</el-button>
    </div>
    <div class="right-search">
      <el-input
        v-model="searchText"
        placeholder="线路名称"
        style="width: 200px;"
        clearable
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input>
      <el-button type="primary" plain @click="handleSearch">查询</el-button>
      <el-button type="info" plain @click="handleReset">重置</el-button>
    </div>
    <input
      ref="fileInput"
      type="file"
      style="display: none"
      accept=".xlsx,.xls"
      @change="handleFileChange"
    />
  </div>
</template>

<script>
export default {
  name: 'LineToolbar',
  data() {
    return {
      searchText: '',
      showAdvanced: false
    }
  },
  methods: {
    handleSearch() {
      this.$emit('search', this.searchText)
    },
    handleReset() {
      this.searchText = ''
      this.$emit('search', '')
    },
    handleImportExcel() {
      this.$refs.fileInput.click()
    },
    handleExportExcel() {
      this.$emit('export-data')
    },
    handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        this.$emit('import-data', file)
      }
      // 清除文件选择，以便可以选择相同的文件
      event.target.value = ''
    },
    toggleAdvancedColumns() {
      this.showAdvanced = !this.showAdvanced;
      this.$emit('toggle-advanced-columns', this.showAdvanced);
    }
  }
}
</script>

<style scoped>
.line-toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  gap: 10px;
}

.right-search {
  display: flex;
  gap: 10px;
  align-items: center;
}

.el-button [class^="el-icon-"] {
  margin-right: 5px;
}
</style>