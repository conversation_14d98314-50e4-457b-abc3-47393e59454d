<template>
  <div class="line-tree-menu">
    <el-input
      v-model="filterText"
      placeholder="输入关键字进行过滤"
      class="filter-input"
    />
    <el-tree
      ref="tree"
      :data="treeData"
      :props="defaultProps"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
      default-expand-all
    />
  </div>
</template>

<script>
export default {
  name: 'LineTreeMenu',
  data() {
    return {
      filterText: '',
      treeData: [
        {
          id: '1',
          label: '华中区域',
          children: [
            {
              id: '1-1',
              label: '500kV线路',
              children: [
                { id: '1-1-1', label: '武汉-南昌线路' },
                { id: '1-1-2', label: '武汉-长沙线路' }
              ]
            },
            {
              id: '1-2',
              label: '220kV线路',
              children: [
                { id: '1-2-1', label: '武汉-黄石线路' },
                { id: '1-2-2', label: '武汉-鄂州线路' }
              ]
            }
          ]
        },
        {
          id: '2',
          label: '华南区域',
          children: [
            {
              id: '2-1',
              label: '500kV线路',
              children: [
                { id: '2-1-1', label: '广州-深圳线路' },
                { id: '2-1-2', label: '广州-珠海线路' }
              ]
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleNodeClick(data) {
      if (!data.children) {
        this.$emit('node-selected', data)
      }
    }
  }
}
</script>

<style scoped>
.line-tree-menu {
  padding: 20px;
}

.filter-input {
  margin-bottom: 20px;
}
</style> 