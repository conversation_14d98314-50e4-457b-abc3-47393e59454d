<template>
  <div class="line-station-container">
    <LineToolbar 
      @add-node="handleAddNode"
      @import-data="handleImportData"
      @export-data="handleExportData"
      @toggle-advanced-columns="handleToggleAdvanced"
    />
    <LineDataTable 
      :tableData="tableData"
      :show-advanced-columns="showAdvancedColumns"
      @edit-node="handleEditNode"
      @delete-node="handleDeleteNode"
    />
    <LineEditDialog
      v-model="dialogVisible"
      :editMode="editMode"
      :currentNode="currentNode"
      @confirm="handleDialogConfirm"
    />
  </div>
</template>

<script>
import LineToolbar from './LineToolbar.vue'
import LineDataTable from './LineDataTable.vue'
import LineEditDialog from './LineEditDialog.vue'
import { lineStationService } from '@/services/lineStationService'

export default {
  name: 'LineStation',
  components: {
    LineToolbar,
    LineDataTable,
    LineEditDialog
  },
  data() {
    return {
      tableData: [],
      dialogVisible: false,
      editMode: 'add',
      currentNode: null,
      showAdvancedColumns: false
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      try {
        const data = await lineStationService.getAllLineStations()
        this.tableData = data
      } catch (error) {
        console.error('获取线路数据失败:', error)
        this.$message.error('获取线路数据失败')
      }
    },
    handleAddNode() {
      this.editMode = 'add'
      this.currentNode = {}
      this.dialogVisible = true
    },
    handleEditNode(node) {
      this.editMode = 'edit'
      this.currentNode = { ...node }
      this.dialogVisible = true
    },
    async handleDeleteNode(node) {
      try {
        await this.$confirm('确认删除该线路?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await lineStationService.deleteLineStation(node.id)
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除线路失败:', error)
          this.$message.error('删除线路失败')
        }
      }
    },
    async handleDialogConfirm(formData) {
      try {
        if (this.editMode === 'add') {
          await lineStationService.addLineStation(formData)
          this.$message.success('添加成功')
        } else {
          await lineStationService.updateLineStation(formData)
          this.$message.success('更新成功')
        }
        this.dialogVisible = false
        this.loadData()
      } catch (error) {
        console.error('保存线路数据失败:', error)
        this.$message.error('保存失败')
      }
    },
    async handleImportData(file) {
      try {
        await lineStationService.importData(file)
        this.$message.success('导入成功')
        this.loadData()
      } catch (error) {
        console.error('导入数据失败:', error)
        this.$message.error('导入失败')
      }
    },
    async handleExportData() {
      try {
        await lineStationService.exportData()
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出数据失败:', error)
        this.$message.error('导出失败')
      }
    },
    handleToggleAdvanced(show) {
      this.showAdvancedColumns = show;
    }
  }
}
</script>

<style scoped>
.line-station-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}
</style> 