<template>
  <el-table
    :data="tableData"
    v-loading="loading"
    style="width: 100%"
    @selection-change="handleSelectionChange"
    border
  >
    <el-table-column type="selection" width="55" align="center"></el-table-column>
    <el-table-column prop="name" label="区域名称" align="center"></el-table-column>
    <el-table-column prop="modelingCount" label="建模数" align="center">
      <template slot-scope="scope">
        <el-link type="primary" @click="openModelingDialog(scope.row)">{{ scope.row.modelingCount }}</el-link>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'LoadCurvDataTable',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    openModelingDialog(row) {
      this.$emit('open-modeling-dialog', row);
    },
  },
};
</script> 