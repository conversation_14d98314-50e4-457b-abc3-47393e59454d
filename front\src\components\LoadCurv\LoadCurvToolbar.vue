<template>
  <div class="toolbar">
    <el-dropdown @command="handleCommand">
      <el-button type="primary">
        批量维护<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="import">导入</el-dropdown-item>
        <el-dropdown-item command="export">导出</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-button @click="handleDelete" type="danger">删除</el-button>
    <el-input
      v-model="searchName"
      placeholder="请输入区域名称"
      class="search-input"
      clearable
      @clear="handleSearch"
      @keyup.enter.native="handleSearch"
    >
      <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
    </el-input>
  </div>
</template>

<script>
export default {
  name: 'LoadCurvToolbar',
  data() {
    return {
      searchName: '',
    };
  },
  methods: {
    handleCommand(command) {
      if (command === 'import') {
        this.$emit('batch-import');
      } else if (command === 'export') {
        this.$emit('batch-export');
      }
    },
    handleDelete() {
      this.$emit('delete');
    },
    handleSearch() {
      this.$emit('search', { name: this.searchName });
    },
  },
};
</script>

<style scoped>
.toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.search-input {
  width: 300px;
  margin-left: auto;
}
</style> 