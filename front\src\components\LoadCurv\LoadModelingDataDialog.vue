<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
  >
    <div class="dialog-toolbar">
      <el-button @click="toggleEdit(true)" type="primary" :disabled="isEditing">修改</el-button>
      <el-button @click="toggleEdit(false)" :disabled="!isEditing">取消修改</el-button>
      <el-button @click="handleSave" type="success" :disabled="!isEditing">保存</el-button>
      <el-button @click="handleExport">导出Excel</el-button>
      <el-button @click="handleImport">导入Excel</el-button>
      <el-button @click="handleClear" type="danger">清空</el-button>
    </div>
    <el-table :data="modelingData" border height="500px" v-loading="loading">
      <el-table-column prop="areaName" label="区域名称" align="center" width="120"></el-table-column>
      <el-table-column prop="startDate" label="起始日期" align="center" width="150"></el-table-column>
      <el-table-column prop="endDate" label="结束日期" align="center" width="150"></el-table-column>
      <el-table-column
        v-for="i in 24"
        :key="i"
        :prop="'p' + i"
        :label="'时段' + i + ' (MW)'"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <el-input
            v-if="isEditing"
            v-model="scope.row['p' + i]"
            size="mini"
          ></el-input>
          <span v-else>{{ scope.row['p' + i] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import * as loadModelingDataService from '@/services/loadModelingDataService';

export default {
  name: 'LoadModelingDataDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    loadCurvInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      isEditing: false,
      loading: false,
      modelingData: [],
      originalData: [], // For cancelling edits
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    dialogTitle() {
      return this.loadCurvInfo ? `负荷建模数据 - ${this.loadCurvInfo.name}` : '负荷建模数据';
    },
  },
  watch: {
    visible(newVal) {
      if (newVal && this.loadCurvInfo) {
        this.fetchModelingData();
      } else {
        this.isEditing = false;
        this.modelingData = [];
      }
    },
  },
  methods: {
    fetchModelingData() {
      this.loading = true;
      loadModelingDataService.query(this.loadCurvInfo.id).then(data => {
        this.modelingData = data;
        this.originalData = JSON.parse(JSON.stringify(data)); // Deep copy
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    toggleEdit(isEditing) {
        this.isEditing = isEditing;
        if (!isEditing) {
            // Cancel edit, restore original data
            this.modelingData = JSON.parse(JSON.stringify(this.originalData));
        }
    },
    handleSave() {
      this.loading = true;
      loadModelingDataService.save(this.loadCurvInfo.id, this.modelingData).then(() => {
        this.$message.success('保存成功');
        this.isEditing = false;
        this.fetchModelingData(); // Refresh data
      }).catch(err => {
        this.$message.error(`保存失败: ${err.message}`);
        this.loading = false;
      });
    },
    handleExport() {
      loadModelingDataService.getTemplate(this.loadCurvInfo.id).then(() => {
        this.$message.success('导出成功');
      }).catch(err => {
        this.$message.error(`导出失败: ${err.message}`);
      });
    },
    handleImport() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx, .xls';
        input.onchange = e => {
            const file = e.target.files[0];
            if (file) {
                loadModelingDataService.importData(this.loadCurvInfo.id, file).then(() => {
                    this.$message.success('导入成功');
                    this.fetchModelingData();
                }).catch(err => {
                    this.$message.error(`导入失败: ${err.message}`);
                });
            }
        };
        input.click();
    },
    handleClear() {
      this.$confirm('确定要清空该区域的所有建模数据吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true;
        loadModelingDataService.clear(this.loadCurvInfo.id).then(() => {
          this.$message.success('数据已清空');
          this.fetchModelingData();
        }).catch(err => {
          this.$message.error(`清空失败: ${err.message}`);
          this.loading = false;
        });
      });
    },
    handleClose() {
      if (this.isEditing) {
        this.$confirm('正在编辑，确认关闭？')
          .then(_ => {
            this.dialogVisible = false;
          })
          .catch(_ => {});
      } else {
        this.dialogVisible = false;
      }
    },
  },
};
</script>

<style scoped>
.dialog-toolbar {
  margin-bottom: 16px;
}
</style> 