<template>
  <div>
    <load-curv-toolbar
      @search="handleSearch"
      @delete="handleDelete"
      @batch-import="handleBatchImport"
      @batch-export="handleBatchExport"
    />
    <load-curv-data-table
      :table-data="tableData"
      :loading="loading"
      @selection-change="handleSelectionChange"
      @open-modeling-dialog="openModelingDialog"
    />
    <load-modeling-data-dialog
      :visible.sync="dialogVisible"
      :load-curv-info="currentLoadCurv"
    />
  </div>
</template>

<script>
import LoadCurvToolbar from './LoadCurvToolbar.vue';
import LoadCurvDataTable from './LoadCurvDataTable.vue';
import LoadModelingDataDialog from './LoadModelingDataDialog.vue';
import * as loadCurvService from '@/services/loadCurvService';
import { MessageBox } from 'element-ui';

export default {
  name: 'LoadCurv',
  components: {
    LoadCurvToolbar,
    LoadCurvDataTable,
    LoadModelingDataDialog,
  },
  data() {
    return {
      tableData: [],
      loading: false,
      searchParams: {},
      selectedRows: [],
      dialogVisible: false,
      currentLoadCurv: null,
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.loading = true;
      loadCurvService.query(this.searchParams).then(data => {
        this.tableData = data;
        this.loading = false;
      });
    },
    handleSearch(params) {
      this.searchParams = params;
      this.fetchData();
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一项进行删除');
        return;
      }
      MessageBox.confirm('确定要删除所选项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        loadCurvService.remove(ids).then(() => {
          this.$message.success('删除成功');
          this.fetchData();
        });
      });
    },
    handleBatchImport() {
      // 触发文件选择
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.xlsx, .xls';
      input.onchange = e => {
        const file = e.target.files[0];
        if (file) {
          loadCurvService.batchImport(file).then(() => {
            this.$message.success('批量导入成功');
            this.fetchData();
          }).catch(err => {
            this.$message.error(`导入失败: ${err.message}`);
          });
        }
      };
      input.click();
    },
    handleBatchExport() {
        loadCurvService.batchExport().then(() => {
            this.$message.success('导出任务已开始，请稍后在下载内容中查看');
        }).catch(err => {
            this.$message.error(`导出失败: ${err.message}`);
        });
    },
    openModelingDialog(row) {
      this.currentLoadCurv = row;
      this.dialogVisible = true;
    },
  },
};
</script> 