<!-- eslint-disable -->
<template>
  <div>
    <!-- 1. 操作与筛选区域 -->
    <el-card class="box-card">
      <div class="filter-container">
        <el-input v-model="listQuery.name" placeholder="节点名称/编号" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
        <el-select v-model="listQuery.region" placeholder="所属区域" clearable style="width: 140px" class="filter-item">
          <el-option v-for="item in regionOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item" plain @click="resetFilter">
          重置
        </el-button>
        <el-button class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate">
          新增节点
        </el-button>
         <el-button class="filter-item" type="danger" icon="el-icon-delete" @click="handleBatchDelete" :disabled="multipleSelection.length === 0">
          批量删除
        </el-button>
        <el-button class="filter-item" type="success" icon="el-icon-download" @click="handleExport">
          导出Excel
        </el-button>
        <el-button class="filter-item" type="primary" icon="el-icon-upload2" @click="handleImportClick">
          导入Excel
        </el-button>
        <!-- 添加重置数据按钮 -->
        <el-button class="filter-item" type="info" icon="el-icon-refresh" @click="clearAllData">
          重置默认数据
        </el-button>
        <!-- 隐藏的文件输入元素 -->
        <input
          ref="excelUpload"
          type="file"
          accept=".xlsx, .xls"
          style="display: none;"
          @change="handleImportFile"
        />
      </div>

      <!-- 2. 数据表格 -->
      <el-table
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%; margin-top: 20px;"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="节点ID" prop="id" width="120" />
        <el-table-column label="节点名称" prop="name" width="120" />
        <el-table-column label="起始日期" prop="startDate" width="100" />
        <el-table-column label="退役日期" prop="endDate" width="100" />
        <el-table-column label="节点所属区域" prop="region" width="120" />
        <el-table-column label="是否两控区" prop="isTwoControlArea" width="100" />
        <el-table-column label="节点切除损失" prop="nodeCutLoss" width="120" />
        <el-table-column label="节点负荷占比(%)" prop="nodeLoadRatio" width="150" />
        <el-table-column label="固定负荷" prop="fixedLoad" width="100" />
        <el-table-column label="节点强迫停运率" prop="forcedOutageRate" width="150" />
        <el-table-column label="节点故障恢复时间" prop="faultRecoveryTime" width="150" />
        <el-table-column label="供电区域" prop="powerSupplyArea" width="100" />
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 3. 分页器 -->
      <div style="margin-top: 20px; text-align: right;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="listQuery.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="listQuery.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 4. 新增/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="temp" label-position="left" label-width="120px" style="width: 500px; margin-left:50px;">
        <el-form-item label="节点ID" prop="id">
          <el-input v-model="temp.id" :disabled="dialogStatus==='update'" />
        </el-form-item>
        <el-form-item label="节点名称" prop="name">
          <el-input v-model="temp.name" />
        </el-form-item>
        <el-form-item label="起始日期" prop="startDate">
          <el-date-picker
            v-model="temp.startDate"
            type="date"
            placeholder="选择日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="退役日期" prop="endDate">
          <el-date-picker
            v-model="temp.endDate"
            type="date"
            placeholder="选择日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="节点所属区域" prop="region">
          <el-input v-model="temp.region" />
        </el-form-item>
        <el-form-item label="是否两控区" prop="isTwoControlArea">
          <el-select v-model="temp.isTwoControlArea" placeholder="请选择">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="节点切除损失" prop="nodeCutLoss">
          <el-input v-model="temp.nodeCutLoss" />
        </el-form-item>
        <el-form-item label="节点负荷占比(%)" prop="nodeLoadRatio">
          <el-input v-model="temp.nodeLoadRatio" />
        </el-form-item>
        <el-form-item label="固定负荷" prop="fixedLoad">
          <el-input v-model="temp.fixedLoad" />
        </el-form-item>
        <el-form-item label="节点强迫停运率" prop="forcedOutageRate">
          <el-input v-model="temp.forcedOutageRate" />
        </el-form-item>
        <el-form-item label="节点故障恢复时间" prop="faultRecoveryTime">
          <el-input v-model="temp.faultRecoveryTime" />
        </el-form-item>
        <el-form-item label="供电区域" prop="powerSupplyArea">
          <el-input v-model="temp.powerSupplyArea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 初始模拟数据
const initialMockList = [
  { 
    id: 'N001', 
    name: '变电站A', 
    region: '华中', 
    startDate: '20090101', 
    endDate: '21000101', 
    isTwoControlArea: '是', 
    nodeCutLoss: '2.5MW', 
    nodeLoadRatio: '12.5', 
    fixedLoad: '8MW', 
    forcedOutageRate: '0.05', 
    faultRecoveryTime: '4小时', 
    powerSupplyArea: '湖北'
  },
  { 
    id: 'N002', 
    name: '发电厂B', 
    region: '华北', 
    startDate: '20100215', 
    endDate: '21000101', 
    isTwoControlArea: '否', 
    nodeCutLoss: '0MW', 
    nodeLoadRatio: '0', 
    fixedLoad: '0MW', 
    forcedOutageRate: '0.03', 
    faultRecoveryTime: '2小时', 
    powerSupplyArea: '北京'
  },
  { 
    id: 'N003', 
    name: '变电站C', 
    region: '华中', 
    startDate: '20111125', 
    endDate: '21000101', 
    isTwoControlArea: '是', 
    nodeCutLoss: '1.8MW', 
    nodeLoadRatio: '8.2', 
    fixedLoad: '6.5MW', 
    forcedOutageRate: '0.04', 
    faultRecoveryTime: '3小时', 
    powerSupplyArea: '湖北'
  },
  { 
    id: 'N004', 
    name: '变电站D', 
    region: '华东', 
    startDate: '20150630', 
    endDate: '21000101', 
    isTwoControlArea: '否', 
    nodeCutLoss: '3.2MW', 
    nodeLoadRatio: '15.8', 
    fixedLoad: '10MW', 
    forcedOutageRate: '0.02', 
    faultRecoveryTime: '2.5小时', 
    powerSupplyArea: '上海'
  },
  { 
    id: 'N005', 
    name: '发电厂E', 
    region: '华南', 
    startDate: '20080520', 
    endDate: '21000101', 
    isTwoControlArea: '否', 
    nodeCutLoss: '0MW', 
    nodeLoadRatio: '0', 
    fixedLoad: '0MW', 
    forcedOutageRate: '0.03', 
    faultRecoveryTime: '3小时', 
    powerSupplyArea: '广东'
  }
];

// 从localStorage获取数据，如果没有则使用初始数据
let mockList = [];
try {
  const savedData = localStorage.getItem('nodeInfoData');
  mockList = savedData ? JSON.parse(savedData) : [...initialMockList];
} catch (e) {
  console.error('Error loading data from localStorage:', e);
  mockList = [...initialMockList];
}

export default {
  name: 'NodeInfo',
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        name: '',
        region: ''
      },
      regionOptions: ['华北', '华东', '华中', '华南', '西北', '西南', '东北'],
      multipleSelection: [],
      temp: {
        id: '',
        name: '',
        region: '',
        startDate: '',
        endDate: '',
        isTwoControlArea: '否',
        nodeCutLoss: '',
        nodeLoadRatio: '',
        fixedLoad: '',
        forcedOutageRate: '',
        faultRecoveryTime: '',
        powerSupplyArea: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑节点',
        create: '新增节点'
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      
      // 过滤数据
      let filteredData = [...mockList];
      
      if (this.listQuery.name) {
        filteredData = filteredData.filter(item => {
          return item.name.toLowerCase().includes(this.listQuery.name.toLowerCase()) || 
                 item.id.toLowerCase().includes(this.listQuery.name.toLowerCase());
        });
      }
      
      if (this.listQuery.region) {
        filteredData = filteredData.filter(item => item.region === this.listQuery.region);
      }
      
      // 更新总数
      this.total = filteredData.length;
      
      // 分页
      const start = (this.listQuery.page - 1) * this.listQuery.limit;
      const end = start + this.listQuery.limit;
      this.list = filteredData.slice(start, end);
      
      this.listLoading = false;
    },
    
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    
    resetFilter() {
      this.listQuery.name = '';
      this.listQuery.region = '';
      this.handleFilter();
    },
    
    handleSizeChange(val) {
      this.listQuery.limit = val;
      this.getList();
    },
    
    handleCurrentChange(val) {
      this.listQuery.page = val;
      this.getList();
    },
    
    resetTemp() {
      this.temp = {
        id: '',
        name: '',
        region: '',
        startDate: '',
        endDate: '',
        isTwoControlArea: '否',
        nodeCutLoss: '',
        nodeLoadRatio: '',
        fixedLoad: '',
        forcedOutageRate: '',
        faultRecoveryTime: '',
        powerSupplyArea: ''
      };
    },
    
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = 'create';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    
    createData() {
      // 简单验证
      if (!this.temp.id || !this.temp.name) {
        this.$message.error('节点ID和名称不能为空');
        return;
      }
      
      // 检查ID是否已存在
      const isExist = mockList.some(item => item.id === this.temp.id);
      if (isExist) {
        this.$message.error('节点ID已存在，请使用其他ID');
        return;
      }
      
      // 添加新节点
      mockList.push(Object.assign({}, this.temp));
      
      // 保存到localStorage
      localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
      
      this.dialogFormVisible = false;
      this.$message({
        message: '创建成功',
        type: 'success'
      });
      this.getList();
    },
    
    handleUpdate(row) {
      this.temp = Object.assign({}, row); // 复制当前行数据
      this.dialogStatus = 'update';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate();
      });
    },
    
    updateData() {
      // 简单验证
      if (!this.temp.name) {
        this.$message.error('节点名称不能为空');
        return;
      }
      
      // 更新节点数据
      const index = mockList.findIndex(item => item.id === this.temp.id);
      if (index !== -1) {
        mockList.splice(index, 1, Object.assign({}, this.temp));
        
        // 保存到localStorage
        localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
        
        this.dialogFormVisible = false;
        this.$message({
          message: '更新成功',
          type: 'success'
        });
        this.getList();
      }
    },
    
    handleDelete(row) {
      this.$confirm('确认删除该节点?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = mockList.findIndex(item => item.id === row.id);
        if (index !== -1) {
          mockList.splice(index, 1);
          
          // 保存到localStorage
          localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
          
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.getList();
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个节点');
        return;
      }
      
      this.$confirm(`确认删除选中的 ${this.multipleSelection.length} 个节点?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.id);
        mockList = mockList.filter(item => !ids.includes(item.id));
        
        // 保存到localStorage
        localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
        
        this.$message({
          message: '批量删除成功',
          type: 'success'
        });
        this.getList();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    
    handleCancel() {
      this.dialogFormVisible = false;
    },
    
    handleExport() {
      // 导出所有数据，不仅仅是当前页
      const exportData = mockList.map(item => {
        return {
          '节点ID': item.id,
          '节点名称': item.name,
          '起始日期': item.startDate,
          '退役日期': item.endDate,
          '节点所属区域': item.region,
          '是否两控区': item.isTwoControlArea,
          '节点切除损失': item.nodeCutLoss,
          '节点负荷占比(%)': item.nodeLoadRatio,
          '固定负荷': item.fixedLoad,
          '节点强迫停运率': item.forcedOutageRate,
          '节点故障恢复时间': item.faultRecoveryTime,
          '供电区域': item.powerSupplyArea
        };
      });
      
      // 创建工作表
      const worksheet = this.$XLSX.utils.json_to_sheet(exportData);
      const workbook = this.$XLSX.utils.book_new();
      this.$XLSX.utils.book_append_sheet(workbook, worksheet, '节点信息');
      
      // 设置列宽
      const colWidths = [
        { wch: 10 }, // 节点ID
        { wch: 12 }, // 节点名称
        { wch: 10 }, // 起始日期
        { wch: 10 }, // 退役日期
        { wch: 12 }, // 节点所属区域
        { wch: 10 }, // 是否两控区
        { wch: 12 }, // 节点切除损失
        { wch: 15 }, // 节点负荷占比(%)
        { wch: 10 }, // 固定负荷
        { wch: 15 }, // 节点强迫停运率
        { wch: 15 }, // 节点故障恢复时间
        { wch: 10 }  // 供电区域
      ];
      worksheet['!cols'] = colWidths;
      
      // 导出文件
      const excelBuffer = this.$XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
      this.$saveAs(blob, '节点信息.xlsx');
    },
    
    handleImportClick() {
      this.$refs.excelUpload.click();
    },
    
    handleImportFile(e) {
      const files = e.target.files;
      if (!files || !files.length) {
        return;
      }
      
      const file = files[0];
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target.result;
          const workbook = this.$XLSX.read(data, { type: 'array' });
          
          // 默认读取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 转换为JSON格式
          const jsonData = this.$XLSX.utils.sheet_to_json(worksheet);
          
          if (jsonData && jsonData.length > 0) {
            // 处理导入的数据
            this.processImportData(jsonData);
          } else {
            this.$message.error('导入的Excel文件未包含有效数据！');
          }
        } catch (error) {
          console.error('Excel导入错误:', error);
          this.$message.error('Excel导入失败，请检查文件格式！');
        }
        
        // 清空文件输入，以便于下次导入同一个文件
        this.$refs.excelUpload.value = '';
      };
      
      reader.readAsArrayBuffer(file);
    },
    
    processImportData(data) {
      try {
        // 映射字段名
        const importedNodes = data.map(item => {
          return {
            id: item['节点ID'] || '',
            name: item['节点名称'] || '',
            startDate: item['起始日期'] || '',
            endDate: item['退役日期'] || '',
            region: item['节点所属区域'] || '',
            isTwoControlArea: item['是否两控区'] || '否',
            nodeCutLoss: item['节点切除损失'] || '',
            nodeLoadRatio: item['节点负荷占比(%)'] || '',
            fixedLoad: item['固定负荷'] || '',
            forcedOutageRate: item['节点强迫停运率'] || '',
            faultRecoveryTime: item['节点故障恢复时间'] || '',
            powerSupplyArea: item['供电区域'] || ''
          };
        });
        
        // 验证必填字段
        const invalidNodes = importedNodes.filter(node => !node.id || !node.name);
        if (invalidNodes.length > 0) {
          this.$message.warning(`有 ${invalidNodes.length} 条数据缺少必填字段(节点ID或名称)，已忽略这些数据`);
        }
        
        // 过滤掉无效数据
        const validNodes = importedNodes.filter(node => node.id && node.name);
        
        // 检查ID是否重复
        const existingIds = mockList.map(node => node.id);
        const duplicateNodes = validNodes.filter(node => existingIds.includes(node.id));
        
        if (duplicateNodes.length > 0) {
          // 提示用户是否覆盖已有数据
          this.$confirm(`发现 ${duplicateNodes.length} 个节点ID已存在，是否覆盖这些节点数据?`, '警告', {
            confirmButtonText: '覆盖',
            cancelButtonText: '跳过',
            type: 'warning'
          }).then(() => {
            // 覆盖已有数据
            this.mergeImportedData(validNodes);
          }).catch(() => {
            // 跳过已有数据
            this.addNewImportedData(validNodes);
          });
        } else {
          // 直接添加新数据
          this.addNewImportedData(validNodes);
        }
      } catch (error) {
        console.error('处理导入数据错误:', error);
        this.$message.error('导入数据处理失败！');
      }
    },
    
    mergeImportedData(importedNodes) {
      // 遍历导入的节点
      importedNodes.forEach(importedNode => {
        const index = mockList.findIndex(node => node.id === importedNode.id);
        if (index !== -1) {
          // 覆盖已有节点
          mockList.splice(index, 1, importedNode);
        } else {
          // 添加新节点
          mockList.push(importedNode);
        }
      });
      
      // 保存到localStorage
      localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
      
      this.$message.success(`成功导入 ${importedNodes.length} 条节点数据`);
      this.getList();
    },
    
    addNewImportedData(importedNodes) {
      // 过滤掉ID已存在的节点
      const existingIds = mockList.map(node => node.id);
      const newNodes = importedNodes.filter(node => !existingIds.includes(node.id));
      
      // 添加新节点
      mockList = mockList.concat(newNodes);
      
      // 保存到localStorage
      localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
      
      this.$message.success(`成功导入 ${newNodes.length} 条新节点数据`);
      this.getList();
    },
    
    clearAllData() {
      this.$confirm('确认重置为默认数据? 当前所有修改将丢失!', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        mockList = [...initialMockList];
        localStorage.setItem('nodeInfoData', JSON.stringify(mockList));
        this.$message.success('已重置为默认数据');
        this.getList();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        });
      });
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
  