<template>
  <el-table
    :data="data"
    border
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center"></el-table-column>
    <el-table-column prop="name" label="节点名称" width="180"></el-table-column>
    <el-table-column prop="code" label="节点编码" width="180"></el-table-column>
    <el-table-column prop="type" label="节点类型" width="120"></el-table-column>
    <el-table-column prop="region" label="所属区域" width="120"></el-table-column>
    <el-table-column prop="voltageLevel" label="电压等级" width="120"></el-table-column>
    <el-table-column label="操作" width="100" align="center">
      <template slot-scope="scope">
        <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'NodeDataTable',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleEdit(row) {
      this.$emit('edit', row);
    },
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
  },
};
</script> 