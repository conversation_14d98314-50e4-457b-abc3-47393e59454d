<template>
  <el-dialog
    :title="isEdit ? '编辑节点' : '新增节点'"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleClose"
  >
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="节点名称">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="节点编码">
        <el-input v-model="form.code"></el-input>
      </el-form-item>
      <el-form-item label="节点类型">
        <el-input v-model="form.type"></el-input>
      </el-form-item>
      <el-form-item label="所属区域">
        <el-input v-model="form.region"></el-input>
      </el-form-item>
      <el-form-item label="电压等级">
        <el-input v-model="form.voltageLevel"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'NodeEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {},
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    isEdit() {
        return this.form && this.form.id;
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.form = { ...val };
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('save', this.form);
        }
      });
    },
  },
};
</script> 