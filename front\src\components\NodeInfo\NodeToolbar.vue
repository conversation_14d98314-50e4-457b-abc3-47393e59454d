<template>
  <div class="toolbar">
    <el-button type="primary" icon="el-icon-plus" @click="$emit('add')">新增</el-button>
    <el-button type="danger" icon="el-icon-delete" @click="$emit('delete')">删除</el-button>
    <el-button type="success" icon="el-icon-upload2" @click="$emit('import')">导入</el-button>
    <el-button type="warning" icon="el-icon-download" @click="$emit('export')">导出</el-button>
  </div>
</template>

<script>
export default {
  name: 'NodeToolbar',
};
</script>

<style scoped>
.toolbar {
  margin-bottom: 20px;
}
</style> 