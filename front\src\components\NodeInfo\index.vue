<template>
  <div class="node-info-container">
    <NodeToolbar @add="handleAdd" @delete="handleDelete" @import="handleImport" @export="handleExport" />
    <NodeDataTable ref="dataTable" :data="tableData" @edit="handleEdit" @selection-change="handleSelectionChange" />
    <NodeEditDialog :visible.sync="dialogVisible" :form-data="formData" @save="handleSave" />
  </div>
</template>

<script>
import NodeToolbar from './NodeToolbar.vue';
import NodeDataTable from './NodeDataTable.vue';
import NodeEditDialog from './NodeEditDialog.vue';
import * as nodeInfoService from '../../services/nodeInfoService';

export default {
  name: 'NodeInfo',
  components: {
    NodeToolbar,
    NodeDataTable,
    NodeEditDialog,
  },
  data() {
    return {
      tableData: [],
      dialogVisible: false,
      formData: {},
      selectedRows: [],
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.tableData = nodeInfoService.getNodeInfoList();
    },
    handleAdd() {
      this.formData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.formData = { ...row };
      this.dialogVisible = true;
    },
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据进行删除');
        return;
      }
      this.$confirm('此操作将永久删除所选节点, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        nodeInfoService.deleteNodeInfoByIds(ids);
        this.fetchData();
        this.$message.success('删除成功!');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSave(data) {
      nodeInfoService.saveNodeInfo(data);
      this.fetchData();
      this.dialogVisible = false;
      this.$message.success('保存成功!');
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleImport() {
        // TODO: Implement import logic
        this.$message.info('导入功能待实现');
    },
    handleExport() {
        // TODO: Implement export logic
        this.$message.info('导出功能待实现');
    }
  },
};
</script>

<style scoped>
.node-info-container {
  padding: 20px;
}
</style> 