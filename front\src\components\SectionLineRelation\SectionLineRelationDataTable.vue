<template>
  <el-table
    :data="data"
    border
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" fixed align="center"></el-table-column>
    
    <el-table-column label="断面名称" prop="sectionName" width="200">
      <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.sectionName" @input="handleInput(scope.$index, 'sectionName', $event)"></el-input>
        <span v-else>{{ scope.row.sectionName }}</span>
      </template>
    </el-table-column>

    <el-table-column label="起始日期" prop="startDate" width="150">
       <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.startDate" @input="handleInput(scope.$index, 'startDate', $event)"></el-input>
        <span v-else>{{ scope.row.startDate }}</span>
      </template>
    </el-table-column>

    <el-table-column label="结束日期" prop="endDate" width="150">
       <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.endDate" @input="handleInput(scope.$index, 'endDate', $event)"></el-input>
        <span v-else>{{ scope.row.endDate }}</span>
      </template>
    </el-table-column>
    
    <el-table-column label="包含线路名称" prop="lineName" width="200">
       <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.lineName" @input="handleInput(scope.$index, 'lineName', $event)"></el-input>
        <span v-else>{{ scope.row.lineName }}</span>
      </template>
    </el-table-column>

    <el-table-column label="包含线路方向" prop="lineDirection">
       <template slot-scope="scope">
        <el-select v-if="isEditing" :value="scope.row.lineDirection" @change="handleInput(scope.$index, 'lineDirection', $event)">
            <el-option label="正向" value="正向"></el-option>
            <el-option label="反向" value="反向"></el-option>
        </el-select>
        <span v-else>{{ scope.row.lineDirection }}</span>
      </template>
    </el-table-column>

  </el-table>
</template>

<script>
export default {
  name: 'SectionLineRelationDataTable',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    isEditing: {
        type: Boolean,
        default: false,
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    handleInput(index, key, value) {
        this.$emit('data-change', { index, key, value });
    }
  },
};
</script> 