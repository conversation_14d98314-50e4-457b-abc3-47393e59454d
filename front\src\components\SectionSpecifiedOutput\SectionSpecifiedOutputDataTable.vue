<template>
  <el-table
    ref="table"
    :data="tableData"
    border
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column prop="sectionName" label="断面名称" align="center">
      <template slot-scope="scope">
        <el-input v-if="isEditing" v-model="scope.row.sectionName" placeholder="请输入断面名称" />
        <span v-else>{{ scope.row.sectionName }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="constraintType" label="约束类型" align="center">
      <template slot-scope="scope">
        <el-input v-if="isEditing" v-model="scope.row.constraintType" placeholder="请输入约束类型" />
        <span v-else>{{ scope.row.constraintType }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="utilizationHours" label="利用小时数" align="center">
      <template slot-scope="scope">
        <el-input-number v-if="isEditing" v-model="scope.row.utilizationHours" :controls="false" style="width: 100%;" placeholder="请输入利用小时数" />
        <span v-else>{{ scope.row.utilizationHours }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="modelingCount" label="建模数" align="center">
      <template slot-scope="scope">
        <el-input-number v-if="isEditing" v-model="scope.row.modelingCount" :controls="false" style="width: 100%;" placeholder="请输入建模数" />
        <span v-else>{{ scope.row.modelingCount }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'SectionSpecifiedOutputDataTable',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    isEditing: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    clearSelection() {
      this.$refs.table.clearSelection();
    },
  },
};
</script>
<style scoped>
.el-input-number.is-without-controls .el-input__inner {
  text-align: center;
}
</style> 