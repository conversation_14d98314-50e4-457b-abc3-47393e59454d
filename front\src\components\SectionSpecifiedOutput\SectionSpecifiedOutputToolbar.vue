<template>
  <div class="toolbar-container">
    <div class="actions-container">
      <el-button
        v-if="!isEditing"
        type="primary"
        icon="el-icon-edit"
        @click="handleEdit"
      >
        批量维护
      </el-button>
      <el-button
        v-if="isEditing"
        type="success"
        icon="el-icon-check"
        @click="handleSave"
      >
        保存
      </el-button>
      <el-button
        v-if="isEditing"
        type="info"
        icon="el-icon-close"
        @click="handleCancel"
      >
        取消
      </el-button>
      <el-button
        v-if="!isEditing"
        type="danger"
        icon="el-icon-delete"
        :disabled="isDeleteDisabled"
        @click="handleDelete"
      >
        删除
      </el-button>
    </div>

    <el-form :inline="true" class="search-form">
      <el-form-item label="断面名称">
        <el-input v-model="queryParams.name" placeholder="请输入断面名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SectionSpecifiedOutputToolbar',
  props: {
    isEditing: {
      type: Boolean,
      default: false,
    },
    isDeleteDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      queryParams: {
        name: '',
      },
    };
  },
  methods: {
    handleEdit() {
      this.$emit('update:isEditing', true);
    },
    handleSave() {
      this.$emit('batch-save');
    },
    handleCancel() {
      this.$emit('update:isEditing', false);
      this.$emit('cancel-edit');
    },
    handleDelete() {
      this.$emit('batch-delete');
    },
    search() {
      this.$emit('query', this.queryParams);
    },
    reset() {
      this.queryParams.name = '';
      this.$emit('query', this.queryParams);
    },
  },
};
</script>

<style scoped>
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.search-form {
  margin-left: auto;
}
</style> 