<template>
  <div class="app-container">
    <SectionSpecifiedOutputToolbar
      :is-editing.sync="isEditing"
      :is-delete-disabled="isDeleteDisabled"
      @batch-save="handleBatchSave"
      @batch-delete="handleBatchDelete"
      @query="handleQuery"
      @cancel-edit="handleCancel"
    />
    <SectionSpecifiedOutputDataTable
      ref="dataTable"
      :is-editing="isEditing"
      :table-data.sync="tableData"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script>
import SectionSpecifiedOutputToolbar from './SectionSpecifiedOutputToolbar.vue';
import SectionSpecifiedOutputDataTable from './SectionSpecifiedOutputDataTable.vue';
import * as sectionSpecifiedOutputService from '@/services/sectionSpecifiedOutputService';
import { deepClone } from '@/utils/deepClone';

export default {
  name: 'SectionSpecifiedOutput',
  components: {
    SectionSpecifiedOutputToolbar,
    SectionSpecifiedOutputDataTable,
  },
  data() {
    return {
      isEditing: false,
      tableData: [],
      originalTableData: [],
      selectedRows: [],
    };
  },
  computed: {
    isDeleteDisabled() {
      return this.selectedRows.length === 0;
    },
  },
  created() {
    this.handleQuery();
  },
  watch: {
    isEditing(newVal) {
      if (newVal) {
        this.originalTableData = deepClone(this.tableData);
      } else {
        this.originalTableData = [];
      }
    },
  },
  methods: {
    handleQuery(queryParams) {
      this.tableData = sectionSpecifiedOutputService.query(queryParams);
    },
    handleBatchSave() {
      this.$refs.dataTable.$el.click(); // 使得所有输入框失去焦点，触发数据更新
      sectionSpecifiedOutputService.batchSave(this.tableData);
      this.$message.success('保存成功');
      this.isEditing = false;
      this.handleQuery();
    },
    handleBatchDelete() {
      this.$confirm('此操作将永久删除选中的数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        sectionSpecifiedOutputService.batchDelete(ids);
        this.$message.success('删除成功');
        this.handleQuery();
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleCancel() {
      this.tableData = this.originalTableData;
      if (this.$refs.dataTable) {
        this.$refs.dataTable.clearSelection();
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style> 