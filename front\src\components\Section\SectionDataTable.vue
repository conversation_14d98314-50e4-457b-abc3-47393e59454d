<template>
  <el-table
    :data="data"
    border
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" fixed align="center"></el-table-column>
    
    <el-table-column label="断面名称" prop="sectionName" width="200">
      <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.sectionName" @input="handleInput(scope.$index, 'sectionName', $event)"></el-input>
        <span v-else>{{ scope.row.sectionName }}</span>
      </template>
    </el-table-column>

    <el-table-column label="起始日期" prop="startDate" width="150">
       <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.startDate" @input="handleInput(scope.$index, 'startDate', $event)"></el-input>
        <span v-else>{{ scope.row.startDate }}</span>
      </template>
    </el-table-column>

    <el-table-column label="结束日期" prop="endDate" width="150">
       <template slot-scope="scope">
        <el-input v-if="isEditing" :value="scope.row.endDate" @input="handleInput(scope.$index, 'endDate', $event)"></el-input>
        <span v-else>{{ scope.row.endDate }}</span>
      </template>
    </el-table-column>
    
    <el-table-column label="最大正传输容量" prop="maxPositiveCapacity" width="200">
       <template slot-scope="scope">
        <el-input-number v-if="isEditing" controls-position="right" style="width: 100%" :value="scope.row.maxPositiveCapacity" @change="handleInput(scope.$index, 'maxPositiveCapacity', $event)"></el-input-number>
        <span v-else>{{ scope.row.maxPositiveCapacity }}</span>
      </template>
    </el-table-column>

    <el-table-column label="最大反传输容量" prop="maxNegativeCapacity">
       <template slot-scope="scope">
        <el-input-number v-if="isEditing" controls-position="right" style="width: 100%" :value="scope.row.maxNegativeCapacity" @change="handleInput(scope.$index, 'maxNegativeCapacity', $event)"></el-input-number>
        <span v-else>{{ scope.row.maxNegativeCapacity }}</span>
      </template>
    </el-table-column>

  </el-table>
</template>

<script>
export default {
  name: 'SectionDataTable',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    isEditing: {
        type: Boolean,
        default: false,
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    handleInput(index, key, value) {
        this.$emit('data-change', { index, key, value });
    }
  },
};
</script>
<style scoped>
.el-input-number {
    width: 100%;
}
</style> 