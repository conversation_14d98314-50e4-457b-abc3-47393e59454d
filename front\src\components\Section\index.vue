<template>
  <div class="section-container">
    <SectionToolbar 
      :is-editing="isEditing"
      @add="handleAdd" 
      @edit="handleEdit"
      @cancel="handleCancel"
      @save="handleSave"
      @delete="handleDelete" 
      @import="handleImport" 
      @export="handleExport"
      @search="handleSearch"
    />
    <SectionDataTable 
      ref="dataTable" 
      :data="filteredData" 
      :is-editing="isEditing"
      @selection-change="handleSelectionChange" 
      @data-change="handleDataChange"
    />
  </div>
</template>

<script>
import SectionToolbar from './SectionToolbar.vue';
import SectionDataTable from './SectionDataTable.vue';
import * as sectionService from '../../services/sectionService';
import { deepClone } from '../../utils/deepClone';

export default {
  name: 'Section',
  components: {
    SectionToolbar,
    SectionDataTable,
  },
  data() {
    return {
      tableData: [],
      filteredData: [],
      originalData: [],
      isEditing: false,
      selectedRows: [],
      searchQuery: '',
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.tableData = sectionService.getSectionList();
      this.handleSearch(this.searchQuery);
    },
    handleSearch(query) {
      this.searchQuery = query;
      if (!query) {
        this.filteredData = this.tableData;
      } else {
        this.filteredData = this.tableData.filter(item => 
          item.sectionName && item.sectionName.toLowerCase().includes(query.toLowerCase())
        );
      }
    },
    handleAdd() {
      if (!this.isEditing) {
        this.handleEdit();
      }
      const newRecord = sectionService.createEmptyRecord();
      this.filteredData.unshift(newRecord);
    },
    handleEdit() {
      this.isEditing = true;
      this.originalData = deepClone(this.filteredData);
    },
    handleCancel() {
        this.isEditing = false;
        const newItems = this.filteredData.filter(item => !this.originalData.some(orig => orig.id === item.id));
        this.filteredData = deepClone(this.originalData);
        if (newItems.length > 0) {
            this.tableData = this.tableData.filter(item => !newItems.some(newItem => newItem.id === item.id));
        }
        this.$message.info('已取消修改');
    },
    handleSave() {
        sectionService.batchSaveSection(this.filteredData);
        this.tableData = sectionService.getSectionList();
        this.handleSearch(this.searchQuery);
        this.isEditing = false;
        this.$message.success('保存成功!');
    },
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据进行删除');
        return;
      }
      this.$confirm('此操作将永久删除所选断面, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        sectionService.deleteSectionByIds(ids);
        this.fetchData();
        this.$message.success('删除成功!');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleDataChange({index, key, value}) {
        if (this.filteredData[index]) {
            this.filteredData[index][key] = value;
        }
    },
    handleImport() {
        this.$message.info('导入功能待实现');
    },
    handleExport() {
        this.$message.info('导出功能待实现');
    },
  },
};
</script>

<style scoped>
.section-container {
  padding: 20px;
}
</style> 