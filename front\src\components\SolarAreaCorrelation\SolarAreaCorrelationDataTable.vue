<!-- eslint-disable -->
<template>
  <div class="data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      
      <el-table-column prop="area1Name" label="光区1名称" min-width="200"></el-table-column>
      <el-table-column prop="area2Name" label="光区2名称" min-width="200"></el-table-column>
      
      <el-table-column prop="correlation" label="光区之间相关系数" min-width="200">
        <template slot-scope="scope">
          <span v-if="!isEditing">{{ scope.row.correlation }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.correlation" 
            :min="0"
            :max="1"
            :precision="6" 
            :step="0.01"
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { solarAreaCorrelationService } from '@/services/solarAreaCorrelationService';

export default {
  name: 'SolarAreaCorrelationDataTable',
  props: {
    isEditing: {
      type: Boolean,
      default: false
    },
    searchText: {
        type: String,
        default: ''
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editingBackup: null,
      currentPage: 1,
      pageSize: 20
    };
  },
  computed: {
    filteredData() {
        if (!this.searchText) {
            return this.tableData;
        }
        return this.tableData.filter(item => 
            item.area1Name.toLowerCase().includes(this.searchText.toLowerCase())
        );
    },
    totalRecords() {
      return this.filteredData.length;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredData.slice(start, end);
    }
  },
  watch: {
      searchText() {
          this.currentPage = 1;
      }
  },
  methods: {
    handleSelectionChange(rows) {
      this.$emit('selection-change', rows);
    },
    async loadData() {
      try {
        this.loading = true;
        this.tableData = await solarAreaCorrelationService.getData();
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    startEditing() {
      this.editingBackup = JSON.parse(JSON.stringify(this.tableData));
    },
    cancelEditing() {
      if (this.editingBackup) {
        this.tableData = this.editingBackup;
        this.editingBackup = null;
      }
    },
    async saveChanges() {
      try {
        await solarAreaCorrelationService.saveData(this.tableData);
        this.editingBackup = null;
      } catch(error) {
        this.$message.error('保存失败');
        console.error(error);
        throw error;
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; 
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  },
  mounted() {
    this.loadData();
  }
};
</script>

<style scoped>
.data-table {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.total-info {
  font-size: 14px;
  color: #606266;
}
</style> 