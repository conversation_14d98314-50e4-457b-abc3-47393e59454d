<!-- eslint-disable -->
<template>
  <div class="data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
      <el-table-column prop="areaName" label="光区名称" min-width="150" fixed="left"></el-table-column>
      <el-table-column prop="startTime" label="起始时间" min-width="120"></el-table-column>
      
      <el-table-column label="地理位置" min-width="270">
        <el-table-column prop="longitude" label="经度" min-width="90"></el-table-column>
        <el-table-column prop="latitude" label="纬度" min-width="90"></el-table-column>
        <el-table-column prop="altitude" label="海拔高度" min-width="90"></el-table-column>
      </el-table-column>

      <el-table-column v-if="showHiddenColumns" prop="endTime" label="结束日期" min-width="120"></el-table-column>
      
      <el-table-column v-if="showHiddenColumns" label="晴空指数概率分布参数">
        <el-table-column prop="clearIndexC" label="C" min-width="90"></el-table-column>
        <el-table-column prop="clearIndexLambda" label="lamda" min-width="90"></el-table-column>
        <el-table-column prop="clearIndexKtu" label="ktu" min-width="90"></el-table-column>
      </el-table-column>
      
      <el-table-column v-if="showHiddenColumns" prop="groundReflectivity" label="地面反射率" min-width="120"></el-table-column>
      
      <el-table-column v-if="showHiddenColumns" label="大气散射系数">
        <el-table-column prop="scatterP" label="p" min-width="90"></el-table-column>
        <el-table-column prop="scatterQ" label="q" min-width="90"></el-table-column>
      </el-table-column>

      <el-table-column v-if="showHiddenColumns" label="天气类型概率">
        <el-table-column v-for="i in 8" :key="'weather' + i" :prop="`weatherProb${i}`" :label="`类型${i}`" min-width="90"></el-table-column>
      </el-table-column>

    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { solarAreaService } from '@/services/solarAreaService';

export default {
  name: 'SolarAreaDataTable',
  props: {
    isEditing: {
      type: Boolean,
      default: false
    },
    searchText: {
        type: String,
        default: ''
    },
    showHiddenColumns: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editingBackup: null,
      currentPage: 1,
      pageSize: 20
    };
  },
  computed: {
    filteredData() {
        if (!this.searchText) {
            return this.tableData;
        }
        return this.tableData.filter(item => 
            item.areaName.toLowerCase().includes(this.searchText.toLowerCase())
        );
    },
    totalRecords() {
      return this.filteredData.length;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredData.slice(start, end);
    }
  },
  watch: {
      searchText() {
          this.currentPage = 1;
      }
  },
  methods: {
    async loadData() {
      try {
        this.loading = true;
        this.tableData = await solarAreaService.getData();
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    startEditing() {
      this.editingBackup = JSON.parse(JSON.stringify(this.tableData));
    },
    cancelEditing() {
      if (this.editingBackup) {
        this.tableData = this.editingBackup;
        this.editingBackup = null;
      }
    },
    async saveChanges() {
      // For simplicity, inline editing is not implemented in this version.
      // This function is a placeholder for saving the whole table data.
      try {
        await solarAreaService.saveData(this.tableData);
        this.editingBackup = null;
      } catch(error) {
        this.$message.error('保存失败');
        console.error(error);
        throw error;
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; 
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  },
  mounted() {
    this.loadData();
  }
};
</script>

<style scoped>
.data-table {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.total-info {
  font-size: 14px;
  color: #606266;
}
</style> 