<!-- eslint-disable -->
<template>
  <div class="toolbar">
    <div class="left">
      <el-button-group>
        <el-button size="small" @click="handleModify" :disabled="isEditing">修改</el-button>
        <el-button size="small" @click="handleCancel" :disabled="!isEditing">取消修改</el-button>
        <el-button size="small" type="primary" @click="handleSave" :disabled="!isEditing">保存</el-button>
      </el-button-group>
      <el-upload
        class="upload-btn"
        action=""
        :show-file-list="false"
        :before-upload="handleImport"
      >
        <el-button size="small">
          <i class="el-icon-upload2"></i> 导入Excel
        </el-button>
      </el-upload>
      <el-button size="small" @click="handleExport">
        <i class="el-icon-download"></i> 导出Excel
      </el-button>
      <el-button size="small" @click="toggleColumns">
        <i class="el-icon-view"></i> 显示/隐藏列
      </el-button>
    </div>
    <div class="right">
        <el-input
            v-model="searchText"
            placeholder="光区名称"
            size="small"
            clearable
            @clear="handleSearch"
            @keyup.enter.native="handleSearch"
            style="width: 200px; margin-right: 10px;"
        ></el-input>
        <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
        <el-button size="small" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SolarAreaToolbar',
  props: {
    isEditing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchText: ''
    };
  },
  methods: {
    handleModify() {
      this.$emit('modify');
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleSave() {
      this.$emit('save');
    },
    handleImport(file) {
      if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && 
          file.type !== 'application/vnd.ms-excel') {
        this.$message.error('只能上传Excel文件!');
        return false;
      }
      this.$emit('import', file);
      return false;
    },
    handleExport() {
      this.$emit('export');
    },
    toggleColumns() {
      this.$emit('toggle-columns');
    },
    handleSearch() {
        this.$emit('search', this.searchText);
    },
    handleReset() {
        this.searchText = '';
        this.$emit('search', this.searchText);
    }
  }
};
</script>

<style scoped>
.toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left, .right {
  display: flex;
  gap: 10px;
  align-items: center;
}
.upload-btn {
  display: inline-block;
}
</style> 