<!-- eslint-disable -->
<template>
  <div class="solar-area">
    <solar-area-toolbar
      @modify="enableEditing"
      @cancel="cancelEditing"
      @save="saveChanges"
      @import="handleImport"
      @export="handleExport"
      :is-editing="isEditing"
      @search="handleSearch"
      @toggle-columns="toggleColumns"
    ></solar-area-toolbar>
    
    <solar-area-data-table
      ref="dataTable"
      :is-editing="isEditing"
      :search-text="searchText"
      :show-hidden-columns="showHiddenColumns"
    ></solar-area-data-table>
  </div>
</template>

<script>
import SolarAreaToolbar from './SolarAreaToolbar.vue';
import SolarAreaDataTable from './SolarAreaDataTable.vue';
import * as solarAreaService from '@/services/solarAreaService';

export default {
  name: 'SolarArea',
  components: {
    SolarAreaToolbar,
    SolarAreaDataTable
  },
  data() {
    return {
      isEditing: false,
      searchText: '',
      showHiddenColumns: false
    };
  },
  methods: {
    enableEditing() {
      this.isEditing = true;
      this.$refs.dataTable.startEditing();
    },
    cancelEditing() {
      this.isEditing = false;
      this.$refs.dataTable.cancelEditing();
    },
    async saveChanges() {
      try {
        await this.$refs.dataTable.saveChanges();
        this.isEditing = false;
        this.$message.success('保存成功');
      } catch (error) {
        this.$message.error('保存失败');
        console.error('保存失败:', error);
      }
    },
    handleSearch(text) {
        this.searchText = text;
    },
    toggleColumns() {
      this.showHiddenColumns = !this.showHiddenColumns;
    },
    handleImport(file) {
      solarAreaService.importData(file)
        .then(() => {
          this.$message.success('导入成功');
          this.$refs.dataTable.loadData();
        })
        .catch(error => {
          this.$message.error(error.message || '导入失败');
          console.error('导入失败:', error);
        });
    },
    handleExport() {
      solarAreaService.exportData()
        .then(() => {
          this.$message.success('导出成功');
        })
        .catch(error => {
          this.$message.error('导出失败');
          console.error('导出失败:', error);
        });
    }
  }
};
</script>

<style scoped>
.solar-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}
</style> 