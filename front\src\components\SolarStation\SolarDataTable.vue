<!-- eslint-disable -->
<template>
  <div class="solar-data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      
      <el-table-column
        prop="stationName"
        label="光伏名称"
        min-width="200"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.stationName }}</span>
          <el-input v-else v-model="scope.row.stationName" size="small"></el-input>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="region"
        label="所在光区"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.region }}</span>
          <el-select v-else v-model="scope.row.region" placeholder="请选择" size="small" style="width: 100%;">
            <el-option label="默认区域" value="默认区域"></el-option>
            <el-option label="珠江区域" value="珠江区域"></el-option>
            <el-option label="粤东区域" value="粤东区域"></el-option>
            <el-option label="粤西区域" value="粤西区域"></el-option>
            <el-option label="粤北区域" value="粤北区域"></el-option>
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="stationCode"
        label="所属节点"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.stationCode }}</span>
          <el-input v-else v-model="scope.row.stationCode" size="small"></el-input>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="stationType"
        label="光伏阵列类型"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.stationType }}</span>
          <el-select v-else v-model="scope.row.stationType" placeholder="请选择" size="small" style="width: 100%;">
            <el-option label="固定倾角" value="固定倾角"></el-option>
            <el-option label="跟踪式" value="跟踪式"></el-option>
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="panelTiltAngle"
        label="光伏阵列倾斜角"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.panelTiltAngle }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.panelTiltAngle" 
            :min="0" 
            :max="90" 
            :precision="0" 
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="panelAzimuthAngle"
        label="光伏阵列方向角"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.panelAzimuthAngle }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.panelAzimuthAngle" 
            :min="0" 
            :max="360" 
            :precision="0" 
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="unitCount"
        label="光伏组件单元个数"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.unitCount }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.unitCount" 
            :min="1" 
            :precision="0" 
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="availability"
        label="光伏板可用率"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.availability }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.availability" 
            :min="0" 
            :max="1" 
            :precision="2" 
            :step="0.01" 
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="errorRate"
        label="预测误差占比"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.errorRate }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.errorRate" 
            :min="0" 
            :max="1" 
            :precision="2" 
            :step="0.01" 
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="150"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.editing"
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <template v-else>
            <el-button
              type="text"
              size="small"
              @click="handleSave(scope.row)"
            >保存</el-button>
            <el-button
              type="text"
              size="small"
              @click="handleCancel(scope.row)"
            >取消</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import * as solarStationService from '@/services/solarStationService';

export default {
  name: 'SolarDataTable',
  props: {
    selectedRegion: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      selectedRows: [],
      loading: false,
      currentPage: 1,
      pageSize: 20,
      editingBackup: null
    };
  },
  computed: {
    totalRecords() {
      return this.tableData.length;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  watch: {
    selectedRegion: {
      handler(newVal) {
        this.loadData();
      },
      immediate: true
    }
  },
  methods: {
    async loadData() {
      this.loading = true;
      try {
        this.tableData = await solarStationService.getSolarStationData(this.selectedRegion);
      } catch (error) {
        this.$message.error('加载数据失败');
        console.error('加载数据失败:', error);
      }
      this.loading = false;
    },
    handleSelectionChange(val) {
      this.selectedRows = val;
      this.$emit('selection-change', val);
    },
    handleEdit(row) {
      this.editingBackup = { ...row };
      this.$set(row, 'editing', true);
    },
    async handleSave(row) {
      try {
        await solarStationService.updateSolarStation(row);
        this.$set(row, 'editing', false);
        this.editingBackup = null;
        this.$message.success('保存成功');
        await this.loadData();
      } catch (error) {
        this.$message.error('保存失败');
        console.error('保存失败:', error);
      }
    },
    handleCancel(row) {
      Object.assign(row, this.editingBackup);
      this.$set(row, 'editing', false);
      this.editingBackup = null;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  }
};
</script>

<style scoped>
.solar-data-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pagination-container {
  padding: 10px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.total-info {
  margin-right: 15px;
  color: #606266;
}
</style>

<style>
/* 全局样式，不使用scoped */
/* 表格编辑样式 */
.el-table .cell .el-input,
.el-table .cell .el-select,
.el-table .cell .el-input-number,
.el-table .cell .el-date-editor {
  margin: -5px 0;
}

/* 表格固定列样式 */
.el-table--border th.is-leaf {
  border-right: 1px solid #ebeef5;
}

.el-table--border td {
  border-right: 1px solid #ebeef5;
}

/* 数字输入框样式 */
.el-input-number.el-input-number--small {
  width: 100%;
}
</style> 