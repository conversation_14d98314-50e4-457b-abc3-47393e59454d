<!-- eslint-disable -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
    class="solar-edit-dialog"
  >
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="form" 
      label-width="120px"
      label-position="right"
      size="small"
    >
      <el-form-item label="光伏名称" prop="stationName">
        <el-input v-model="form.stationName" placeholder="请输入光伏电站名称"></el-input>
      </el-form-item>
      
      <el-form-item label="所在光区" prop="region">
        <el-select v-model="form.region" placeholder="请选择所在区域" style="width: 100%;">
          <el-option label="默认区域" value="默认区域"></el-option>
          <el-option label="珠江区域" value="珠江区域"></el-option>
          <el-option label="粤东区域" value="粤东区域"></el-option>
          <el-option label="粤西区域" value="粤西区域"></el-option>
          <el-option label="粤北区域" value="粤北区域"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="所属节点" prop="stationCode">
        <el-input v-model="form.stationCode" placeholder="请输入所属节点代码"></el-input>
      </el-form-item>
      
      <el-form-item label="光伏阵列类型" prop="stationType">
        <el-select v-model="form.stationType" placeholder="请选择阵列类型" style="width: 100%;">
          <el-option label="固定倾角" value="固定倾角"></el-option>
          <el-option label="跟踪式" value="跟踪式"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="光伏阵列倾角" prop="panelTiltAngle">
        <el-input-number 
          v-model="form.panelTiltAngle" 
          :min="0" 
          :max="90" 
          :precision="0" 
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="光伏阵列方向角" prop="panelAzimuthAngle">
        <el-input-number 
          v-model="form.panelAzimuthAngle" 
          :min="0" 
          :max="360" 
          :precision="0" 
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="光伏组件个数" prop="installedCapacity">
        <el-input-number 
          v-model="form.installedCapacity" 
          :min="1" 
          :precision="0" 
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="光伏容量率" prop="capacity">
        <el-input-number 
          v-model="form.capacity" 
          :min="0" 
          :precision="2" 
          :step="0.01" 
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="偏测减光系数" prop="powerFactor">
        <el-input-number 
          v-model="form.powerFactor" 
          :min="0" 
          :max="1" 
          :precision="2" 
          :step="0.01" 
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SolarEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'add' // 'add' 或 'edit'
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        id: '',
        stationName: '',
        region: '默认区域',
        stationCode: '',
        stationType: '固定倾角',
        panelTiltAngle: 28,
        panelAzimuthAngle: 0,
        installedCapacity: 36,
        capacity: 1,
        powerFactor: 0.15
      },
      rules: {
        stationName: [
          { required: true, message: '请输入光伏电站名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        region: [
          { required: true, message: '请选择所在区域', trigger: 'change' }
        ],
        stationCode: [
          { required: true, message: '请输入所属节点代码', trigger: 'blur' }
        ],
        stationType: [
          { required: true, message: '请选择光伏阵列类型', trigger: 'change' }
        ],
        panelTiltAngle: [
          { required: true, message: '请输入光伏阵列倾角', trigger: 'blur' }
        ],
        panelAzimuthAngle: [
          { required: true, message: '请输入光伏阵列方向角', trigger: 'blur' }
        ],
        installedCapacity: [
          { required: true, message: '请输入光伏组件个数', trigger: 'blur' }
        ],
        capacity: [
          { required: true, message: '请输入光伏容量率', trigger: 'blur' }
        ],
        powerFactor: [
          { required: true, message: '请输入偏测减光系数', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogTitle() {
      return this.mode === 'add' ? '新增光伏电站' : '编辑光伏电站';
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initForm();
      }
    },
    editData: {
      handler(val) {
        if (this.dialogVisible) {
          this.initForm();
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    initForm() {
      if (this.mode === 'edit' && this.editData) {
        // 编辑模式，使用传入的数据
        this.form = { ...this.editData };
      } else {
        // 添加模式，使用默认值
        this.form = {
          id: '',
          stationName: '',
          region: '默认区域',
          stationCode: '',
          stationType: '固定倾角',
          panelTiltAngle: 28,
          panelAzimuthAngle: 0,
          installedCapacity: 36,
          capacity: 1,
          powerFactor: 0.15
        };
      }
      
      // 重置表单验证状态
      if (this.$refs.form) {
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      }
    },
    
    // 处理关闭
    handleClose() {
      this.dialogVisible = false;
      this.$emit('update:visible', false);
    },
    
    // 处理提交
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 提交表单数据到父组件
          this.$emit('submit', { ...this.form });
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style scoped>
/* 基本样式 */
.solar-edit-dialog {
  margin-top: 2vh;
}
</style>

<style>
/* 全局样式，适配不同屏幕尺寸 */
@media screen and (max-width: 768px) {
  .solar-edit-dialog .el-dialog {
    width: 90% !important;
  }
  
  .solar-edit-dialog .el-form-item__label {
    width: 80px !important;
  }
}
</style> 