<!-- eslint-disable -->
<template>
  <div class="solar-station-container">
    <!-- 左侧树形导航 -->
    <solar-tree-menu 
      :treeData="treeData"
      @node-click="handleNodeClick"
    />
    
    <!-- 右侧内容区 -->
    <div class="content-area">
      <!-- 工具栏 -->
      <solar-toolbar 
        @add="handleAdd"
        @edit="handleEdit"
        @cancel-edit="handleCancelEdit"
        @delete="handleDelete"
        @save="handleSave"
        @export="handleExport"
        @import="handleImport"
        @search="handleSearch"
        :isEditing="isEditing"
        :selectedRows="selectedRows.length"
      />
      
      <!-- 表格区域 -->
      <solar-data-table
        :data="tableData"
        :loading="loading"
        :isEditing="isEditing"
        @selection-change="handleSelectionChange"
        @edit-row="handleEditRow"
        @page-change="handlePageChange"
        ref="dataTable"
      />
      
      <!-- 编辑对话框 -->
      <solar-edit-dialog
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        :editData="editData"
        :mode="dialogMode"
        @submit="handleDialogSubmit"
      />
    </div>
  </div>
</template>

<script>
import SolarTreeMenu from './SolarTreeMenu.vue';
import SolarToolbar from './SolarToolbar.vue';
import SolarDataTable from './SolarDataTable.vue';
import SolarEditDialog from './SolarEditDialog.vue';
import { solarStationService } from '@/services/solarStationService';

export default {
  name: 'SolarStation',
  components: {
    SolarTreeMenu,
    SolarToolbar,
    SolarDataTable,
    SolarEditDialog
  },
  data() {
    return {
      treeData: [],
      tableData: [],
      filteredData: [],
      loading: false,
      isEditing: false,
      selectedRows: [],
      currentNode: null,
      dialogVisible: false,
      dialogMode: 'add', // 'add' 或 'edit'
      editData: {},
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      searchText: ''
    };
  },
  created() {
    // 加载初始数据
    this.loadTreeData();
  },
  methods: {
    // 加载树形菜单数据
    loadTreeData() {
      this.loading = true;
      solarStationService.getMenuTree()
        .then(data => {
          this.treeData = data;
          this.loading = false;
        })
        .catch(error => {
          console.error('加载菜单数据失败:', error);
          this.$message.error('加载菜单数据失败');
          this.loading = false;
        });
    },
    
    // 处理节点点击
    handleNodeClick(node) {
      this.currentNode = node;
      this.loadTableData();
    },
    
    // 加载表格数据
    loadTableData() {
      if (!this.currentNode) return;
      
      this.loading = true;
      solarStationService.getSolarStationData(this.currentNode.id)
        .then(response => {
          this.tableData = response;
          this.applyFilters();
          this.loading = false;
        })
        .catch(error => {
          console.error('加载表格数据失败:', error);
          this.$message.error('加载表格数据失败');
          this.loading = false;
        });
    },
    
    // 应用过滤器并更新分页
    applyFilters() {
      let filteredData = [...this.tableData];
      
      // 应用搜索过滤
      if (this.searchText) {
        filteredData = filteredData.filter(item => 
          item.stationName.toLowerCase().includes(this.searchText.toLowerCase())
        );
      }
      
      this.filteredData = filteredData;
      this.pagination.total = filteredData.length;
      
      // 更新分页数据
      const { page, size } = this.pagination;
      const start = (page - 1) * size;
      const end = start + size;
      
      // 向表格组件传递分页后的数据
      if (this.$refs.dataTable) {
        this.$refs.dataTable.updateData(filteredData.slice(start, end), this.pagination);
      }
    },
    
    // 处理搜索
    handleSearch(text) {
      this.searchText = text;
      this.pagination.page = 1; // 重置到第一页
      this.applyFilters();
    },
    
    // 处理选择行变化
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    
    // 处理添加
    handleAdd() {
      this.dialogMode = 'add';
      this.editData = {
        stationName: '',
        region: '默认区域',
        stationCode: '',
        stationType: '固定倾角',
        panelTiltAngle: 28,
        panelAzimuthAngle: 0,
        installedCapacity: 36,
        capacity: 1,
        powerFactor: 0.15
      };
      this.dialogVisible = true;
    },
    
    // 处理编辑按钮点击
    handleEdit() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条记录进行编辑');
        return;
      }
      
      this.isEditing = true;
    },
    
    // 处理取消编辑
    handleCancelEdit() {
      this.isEditing = false;
      this.loadTableData(); // 重新加载数据，放弃修改
    },
    
    // 处理删除
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录');
        return;
      }
      
      this.$confirm(`确认删除选中的 ${this.selectedRows.length} 条记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        solarStationService.deleteSolarStations(ids)
          .then(() => {
            this.$message.success('删除成功');
            this.loadTableData();
          })
          .catch(error => {
            console.error('删除失败:', error);
            this.$message.error('删除失败');
          });
      }).catch(() => {
        // 用户取消删除操作
      });
    },
    
    // 处理保存
    handleSave() {
      const tableData = this.$refs.dataTable ? this.$refs.dataTable.getEditedData() : [];
      
      solarStationService.saveSolarStationData(tableData)
        .then(() => {
          this.$message.success('保存成功');
          this.isEditing = false;
          this.loadTableData();
        })
        .catch(error => {
          console.error('保存失败:', error);
          this.$message.error('保存失败');
        });
    },
    
    // 处理导出
    handleExport() {
      solarStationService.exportSolarStationData(this.currentNode ? this.currentNode.id : null)
        .then(() => {
          this.$message.success('导出成功');
        })
        .catch(error => {
          console.error('导出失败:', error);
          this.$message.error('导出失败');
        });
    },
    
    // 处理导入
    handleImport(file) {
      solarStationService.importSolarStationData(file, this.currentNode ? this.currentNode.id : null)
        .then(() => {
          this.$message.success('导入成功');
          this.loadTableData();
        })
        .catch(error => {
          console.error('导入失败:', error);
          this.$message.error('导入失败: ' + (error.message || '未知错误'));
        });
    },
    
    // 处理行编辑
    handleEditRow(row) {
      this.dialogMode = 'edit';
      // 深拷贝以防止直接修改原始数据
      this.editData = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },
    
    // 处理对话框提交
    handleDialogSubmit(formData) {
      if (this.dialogMode === 'add') {
        solarStationService.addSolarStation(formData)
          .then(() => {
            this.$message.success('添加成功');
            this.dialogVisible = false;
            this.loadTableData();
          })
          .catch(error => {
            console.error('添加失败:', error);
            this.$message.error('添加失败');
          });
      } else {
        solarStationService.updateSolarStation(formData)
          .then(() => {
            this.$message.success('更新成功');
            this.dialogVisible = false;
            this.loadTableData();
          })
          .catch(error => {
            console.error('更新失败:', error);
            this.$message.error('更新失败');
          });
      }
    },
    
    // 处理页码变化
    handlePageChange(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
      this.applyFilters();
    }
  }
};
</script>

<style scoped>
.solar-station-container {
  display: flex;
  height: 100%;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow: hidden;
}
</style> 