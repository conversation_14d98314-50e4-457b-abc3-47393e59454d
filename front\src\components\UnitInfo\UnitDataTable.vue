<template>
  <el-table
    :data="data"
    border
    style="width: 100%"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" fixed align="center"></el-table-column>
    <el-table-column prop="unitName" label="机组名称" fixed width="250"></el-table-column>
    <el-table-column prop="node" label="节点" width="150"></el-table-column>
    <el-table-column prop="powerPlant" label="电厂" width="180"></el-table-column>
    <el-table-column prop="generationCompany" label="发电公司" width="150"></el-table-column>
    <el-table-column prop="startDate" label="起始日期" width="120"></el-table-column>
    <el-table-column prop="retirementDate" label="退役时间" width="120"></el-table-column>
    <el-table-column prop="type" label="类型" width="120"></el-table-column>
    <el-table-column prop="capacity" label="容量(MW)" width="120"></el-table-column>
    <el-table-column prop="minPower" label="最小出力(MW)" width="120"></el-table-column>

    <!-- Hidden Columns -->
    <el-table-column v-if="showExtraColumns" prop="hydroStorageEnergy_MWH" label="水电/储能电量(MWH)" width="180"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="pumpedStorageEfficiency" label="抽蓄转换效率" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="forcedOutageRate_percent" label="强迫停运率(％)" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="plantPowerConsumptionRate_percent" label="厂用电率(％)" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="equivalentMaintenancePeriod_days" label="等效检修期(天)" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="majorOverhaulPeriod_days" label="大修期(天)" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="minorOverhaulPeriod_days" label="小修期(天)" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="majorOverhaulLife_years" label="大修年限(年)" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="specifiedMaintenanceStart" label="指定检修起始" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="specifiedMaintenanceEnd" label="指定检修终止" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="startupCost_wan_yuan" label="启停费用(万元)" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="fixedOperatingCost_wan_yuan_per_year" label="固定运行成本(万元/年)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="varOpCostAtMaxPower_yuan_per_kWh" label="最高出力处可变运行成本(元/kWh)" width="280"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="varOpCostAtMinPower_yuan_per_kWh" label="最低出力处可变运行成本(元/kWh)" width="280"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="investment_wan_yuan" label="新建/扩建/技改投资(万元)" width="220"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="paybackPeriod_years" label="回收期(年)" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="planType" label="计划类型" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="periodsInPlanning" label="规划期内的期数" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="earliestCommissionYear" label="最早投产年份" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="samePeriodInterval" label="同期间隔" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="minIntervalWithPreviousPeriod" label="与上期最小间隔" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="planningType" label="规划类型" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="coalConsumptionRateAtMaxPower_g_per_kWh" label="最高出力处煤耗率(g/kWh)" width="250"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="coalConsumptionRateAtMinPower_g_per_kWh" label="最低出力处煤耗率(g/kWh)" width="250"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="co2EmissionRate_t_per_t" label="二氧化碳排放率(t/t)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="hydrocarbonEmissionRate_kg_per_t" label="碳氢化合物放率(kg/t)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="noxEmissionRate_kg_per_t" label="氮氧化物排放率(kg/t)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="so2EmissionRate_kg_per_t" label="二氧化硫排放率(kg/t)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="ashSlagEmissionRate_t_per_t" label="灰渣排放率(t/t)" width="180"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="particulateMatterEmissionRate_kg_per_t" label="烟尘排放率(kg/t)" width="180"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="isAgcUnit" label="是否为AGC机组" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="agcRegulationRate_MW_per_min" label="机组调频调节速率(MW/min)" width="250"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="loadFollowingRate_MW_per_15min" label="机组负荷跟踪调节速率(MW/15min)" width="300"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="rampUpRate_MW_per_h" label="机组爬坡up调节速率(MW/h)" width="250"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="rampDownRate_MW_per_h" label="机组爬坡down调节速率(MW/h)" width="250"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="maxDailyStarts" label="日内最大启停次数(次)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="minUpTime_h" label="机组最小开机时间(h)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="minDownTime_h" label="机组最小停机时间(h)" width="200"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="mttf_hours" label="MTTF（小时）" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="mttr_hours" label="MTTR（小时）" width="150"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="unitStatus" label="机组状态" width="120"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="failureRateGain_percent" label="机组故障率增益(％)" width="180"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="rotationalInertiaCoefficient" label="机组转动惯量系数" width="180"></el-table-column>
    <el-table-column v-if="showExtraColumns" prop="storageParticipatesInWeeklyOptimization" label="储能/抽蓄是否参与周优化" width="220"></el-table-column>

    <el-table-column label="操作" width="100" fixed="right" align="center">
      <template slot-scope="scope">
        <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'UnitDataTable',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    showExtraColumns: {
        type: Boolean,
        default: false,
    }
  },
  methods: {
    handleEdit(row) {
      this.$emit('edit', row);
    },
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
  },
};
</script> 