<template>
  <el-dialog
    :title="isEdit ? '编辑机组' : '新增机组'"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
    top="5vh"
  >
    <el-scrollbar style="height: 70vh;">
      <el-form ref="form" :model="form" label-width="220px" style="padding-right: 20px;">
        <el-row>
            <el-col :span="12">
                <el-form-item label="机组名称" prop="unitName"><el-input v-model="form.unitName"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="节点" prop="node"><el-input v-model="form.node"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="电厂" prop="powerPlant"><el-input v-model="form.powerPlant"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="发电公司" prop="generationCompany"><el-input v-model="form.generationCompany"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="起始日期" prop="startDate"><el-input v-model="form.startDate"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="退役时间" prop="retirementDate"><el-input v-model="form.retirementDate"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="类型" prop="type"><el-input v-model="form.type"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="容量(MW)" prop="capacity"><el-input v-model="form.capacity" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="最小出力(MW)" prop="minPower"><el-input v-model="form.minPower" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="水电/储能电量(MWH)" prop="hydroStorageEnergy_MWH"><el-input v-model="form.hydroStorageEnergy_MWH" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="抽蓄转换效率" prop="pumpedStorageEfficiency"><el-input v-model="form.pumpedStorageEfficiency" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="强迫停运率(％)" prop="forcedOutageRate_percent"><el-input v-model="form.forcedOutageRate_percent" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="厂用电率(％)" prop="plantPowerConsumptionRate_percent"><el-input v-model="form.plantPowerConsumptionRate_percent" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="等效检修期(天)" prop="equivalentMaintenancePeriod_days"><el-input v-model="form.equivalentMaintenancePeriod_days" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="大修期(天)" prop="majorOverhaulPeriod_days"><el-input v-model="form.majorOverhaulPeriod_days" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="小修期(天)" prop="minorOverhaulPeriod_days"><el-input v-model="form.minorOverhaulPeriod_days" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="大修年限(年)" prop="majorOverhaulLife_years"><el-input v-model="form.majorOverhaulLife_years" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="指定检修起始" prop="specifiedMaintenanceStart"><el-input v-model="form.specifiedMaintenanceStart"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="指定检修终止" prop="specifiedMaintenanceEnd"><el-input v-model="form.specifiedMaintenanceEnd"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="启停费用(万元)" prop="startupCost_wan_yuan"><el-input v-model="form.startupCost_wan_yuan" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="固定运行成本(万元/年)" prop="fixedOperatingCost_wan_yuan_per_year"><el-input v-model="form.fixedOperatingCost_wan_yuan_per_year" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="最高出力处可变运行成本(元/kWh)" prop="varOpCostAtMaxPower_yuan_per_kWh"><el-input v-model="form.varOpCostAtMaxPower_yuan_per_kWh" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="最低出力处可变运行成本(元/kWh)" prop="varOpCostAtMinPower_yuan_per_kWh"><el-input v-model="form.varOpCostAtMinPower_yuan_per_kWh" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="新建/扩建/技改投资(万元)" prop="investment_wan_yuan"><el-input v-model="form.investment_wan_yuan" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="回收期(年)" prop="paybackPeriod_years"><el-input v-model="form.paybackPeriod_years" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="计划类型" prop="planType"><el-input v-model="form.planType"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="规划期内的期数" prop="periodsInPlanning"><el-input v-model="form.periodsInPlanning" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="最早投产年份" prop="earliestCommissionYear"><el-input v-model="form.earliestCommissionYear" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="同期间隔" prop="samePeriodInterval"><el-input v-model="form.samePeriodInterval"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="与上期最小间隔" prop="minIntervalWithPreviousPeriod"><el-input v-model="form.minIntervalWithPreviousPeriod"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="规划类型" prop="planningType"><el-input v-model="form.planningType"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="最高出力处煤耗率(g/kWh)" prop="coalConsumptionRateAtMaxPower_g_per_kWh"><el-input v-model="form.coalConsumptionRateAtMaxPower_g_per_kWh" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="最低出力处煤耗率(g/kWh)" prop="coalConsumptionRateAtMinPower_g_per_kWh"><el-input v-model="form.coalConsumptionRateAtMinPower_g_per_kWh" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="二氧化碳排放率(t/t)" prop="co2EmissionRate_t_per_t"><el-input v-model="form.co2EmissionRate_t_per_t" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="碳氢化合物放率(kg/t)" prop="hydrocarbonEmissionRate_kg_per_t"><el-input v-model="form.hydrocarbonEmissionRate_kg_per_t" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="氮氧化物排放率(kg/t)" prop="noxEmissionRate_kg_per_t"><el-input v-model="form.noxEmissionRate_kg_per_t" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="二氧化硫排放率(kg/t)" prop="so2EmissionRate_kg_per_t"><el-input v-model="form.so2EmissionRate_kg_per_t" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="灰渣排放率(t/t)" prop="ashSlagEmissionRate_t_per_t"><el-input v-model="form.ashSlagEmissionRate_t_per_t" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="烟尘排放率(kg/t)" prop="particulateMatterEmissionRate_kg_per_t"><el-input v-model="form.particulateMatterEmissionRate_kg_per_t" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="是否为AGC机组" prop="isAgcUnit"><el-switch v-model="form.isAgcUnit"></el-switch></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="机组调频调节速率(MW/min)" prop="agcRegulationRate_MW_per_min"><el-input v-model="form.agcRegulationRate_MW_per_min" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="机组负荷跟踪调节速率(MW/15min)" prop="loadFollowingRate_MW_per_15min"><el-input v-model="form.loadFollowingRate_MW_per_15min" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="机组爬坡up调节速率(MW/h)" prop="rampUpRate_MW_per_h"><el-input v-model="form.rampUpRate_MW_per_h" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="机组爬坡down调节速率(MW/h)" prop="rampDownRate_MW_per_h"><el-input v-model="form.rampDownRate_MW_per_h" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="日内最大启停次数(次)" prop="maxDailyStarts"><el-input v-model="form.maxDailyStarts" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="机组最小开机时间(h)" prop="minUpTime_h"><el-input v-model="form.minUpTime_h" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="机组最小停机时间(h)" prop="minDownTime_h"><el-input v-model="form.minDownTime_h" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="MTTF（小时）" prop="mttf_hours"><el-input v-model="form.mttf_hours" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="MTTR（小时）" prop="mttr_hours"><el-input v-model="form.mttr_hours" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="机组状态" prop="unitStatus"><el-input v-model="form.unitStatus"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="机组故障率增益(％)" prop="failureRateGain_percent"><el-input v-model="form.failureRateGain_percent" type="number"></el-input></el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="机组转动惯量系数" prop="rotationalInertiaCoefficient"><el-input v-model="form.rotationalInertiaCoefficient" type="number"></el-input></el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item label="储能/抽蓄是否参与周优化" prop="storageParticipatesInWeeklyOptimization"><el-switch v-model="form.storageParticipatesInWeeklyOptimization"></el-switch></el-form-item>
            </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'UnitEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {},
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    isEdit() {
        return this.form && this.form.id;
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.form = { ...val };
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('save', this.form);
        }
      });
    },
  },
};
</script>
<style scoped>
.el-scrollbar__wrap {
    overflow-x: hidden;
}
</style> 