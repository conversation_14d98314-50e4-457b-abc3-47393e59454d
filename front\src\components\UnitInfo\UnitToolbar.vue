<template>
  <div class="toolbar-container">
    <div class="buttons-left">
      <el-button type="primary" icon="el-icon-plus" @click="$emit('add')">新增</el-button>
      <el-button type="primary" icon="el-icon-edit" @click="$emit('edit')">编辑</el-button>
      <el-button type="danger" icon="el-icon-delete" @click="$emit('delete')">删除</el-button>
      <el-button type="success" icon="el-icon-upload2" @click="$emit('import')">导入Excel</el-button>
      <el-button type="warning" icon="el-icon-download" @click="$emit('export')">导出Excel</el-button>
      <el-button icon="el-icon-view" @click="$emit('toggle-columns')">显示/隐藏列</el-button>
      <el-button icon="el-icon-document" @click="$emit('load-typical')">典型值载入</el-button>
    </div>
    <div class="search-right">
       <el-form :inline="true" :model="searchForm" @submit.native.prevent>
         <el-form-item label="机组名称">
           <el-input v-model="searchForm.unitName" placeholder="请输入机组名称" @keyup.enter.native="handleSearch"></el-input>
         </el-form-item>
         <el-form-item>
           <el-button type="primary" @click="handleSearch">查询</el-button>
           <el-button @click="handleReset">重置</el-button>
         </el-form-item>
       </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnitToolbar',
  data() {
    return {
      searchForm: {
        unitName: '',
      },
    };
  },
  methods: {
      handleSearch() {
          this.$emit('search', this.searchForm.unitName);
      },
      handleReset() {
          this.searchForm.unitName = '';
          this.$emit('search', '');
      }
  }
};
</script>

<style scoped>
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.buttons-left > .el-button {
    margin-right: 10px;
}
.search-right {
    display: flex;
}
</style> 