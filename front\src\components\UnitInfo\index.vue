<template>
  <div class="unit-info-container">
    <UnitToolbar 
      @add="handleAdd" 
      @edit="handleEdit"
      @delete="handleDelete" 
      @import="handleImport" 
      @export="handleExport"
      @toggle-columns="handleToggleColumns"
      @load-typical="handleLoadTypical"
      @search="handleSearch"
    />
    <UnitDataTable 
      ref="dataTable" 
      :data="filteredData" 
      :show-extra-columns="showExtraColumns"
      @edit="handleEdit" 
      @selection-change="handleSelectionChange" 
    />
    <UnitEditDialog 
      :visible.sync="dialogVisible" 
      :form-data="formData" 
      @save="handleSave" 
    />
  </div>
</template>

<script>
import UnitToolbar from './UnitToolbar.vue';
import UnitDataTable from './UnitDataTable.vue';
import UnitEditDialog from './UnitEditDialog.vue';
import * as unitInfoService from '../../services/unitInfoService';

export default {
  name: 'UnitInfo',
  components: {
    UnitToolbar,
    UnitDataTable,
    UnitEditDialog,
  },
  data() {
    return {
      tableData: [],
      filteredData: [],
      dialogVisible: false,
      formData: {},
      selectedRows: [],
      showExtraColumns: false,
      searchQuery: '',
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.tableData = unitInfoService.getUnitInfoList();
      this.handleSearch(this.searchQuery); // Apply current search query
    },
    handleSearch(query) {
      this.searchQuery = query;
      if (!query) {
        this.filteredData = this.tableData;
      } else {
        this.filteredData = this.tableData.filter(item => 
          item.unitName && item.unitName.toLowerCase().includes(query.toLowerCase())
        );
      }
    },
    handleAdd() {
      this.formData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
        // If edit is triggered from toolbar, row is undefined. Use selectedRows.
        const recordToEdit = row || (this.selectedRows.length === 1 ? this.selectedRows[0] : null);
        if (!recordToEdit) {
            this.$message.warning('请选择一条数据进行编辑');
            return;
        }
        this.formData = { ...recordToEdit };
        this.dialogVisible = true;
    },
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条数据进行删除');
        return;
      }
      this.$confirm('此操作将永久删除所选机组, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        unitInfoService.deleteUnitInfoByIds(ids);
        this.fetchData();
        this.$message.success('删除成功!');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleSave(data) {
      unitInfoService.saveUnitInfo(data);
      this.fetchData();
      this.dialogVisible = false;
      this.$message.success('保存成功!');
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleToggleColumns() {
        this.showExtraColumns = !this.showExtraColumns;
    },
    handleImport() {
        this.$message.info('导入功能待实现');
    },
    handleExport() {
        this.$message.info('导出功能待实现');
    },
    handleLoadTypical() {
        this.$message.info('典型值载入功能待实现');
    }
  },
};
</script>

<style scoped>
.unit-info-container {
  padding: 20px;
}
</style> 