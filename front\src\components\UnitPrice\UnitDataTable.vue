<!-- eslint-disable -->
<template>
  <div class="unit-data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
      ></el-table-column>
      
      <el-table-column
        prop="unitName"
        label="机组名称"
        min-width="180"
        fixed="left"
      ></el-table-column>
      
      <el-table-column
        prop="priceDate"
        label="报价日期"
        width="100"
        align="center"
        fixed="left"
      ></el-table-column>
      
      <!-- 24小时分时报价 -->
      <el-table-column
        v-for="hour in 24"
        :key="hour"
        :prop="`prices[${hour-1}]`"
        :label="`时段${hour}`"
        width="80"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!isEditing">{{ scope.row.prices[hour-1] }}</span>
          <el-input 
            v-else 
            v-model.number="scope.row.prices[hour-1]" 
            size="small" 
            style="width: 70px;" 
            @input="validateNumber($event, scope.row, hour-1)"
          ></el-input>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="80"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页器 -->
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
      <span class="page-info">
        {{ currentPage }}/{{ totalPages }} 页
      </span>
      <el-select v-model="pageSize" placeholder="每页显示" size="small" style="width: 120px;">
        <el-option
          v-for="item in [10, 20, 50, 100]"
          :key="item"
          :label="`${item}条/页`"
          :value="item">
        </el-option>
      </el-select>
      <el-button size="small" type="primary" @click="goToPage">GO</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnitDataTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    isEditing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      paginatedData: [],
      currentPage: 1,
      pageSize: 20,
      totalRecords: 0,
      editedData: []
    };
  },
  computed: {
    totalPages() {
      return Math.ceil(this.totalRecords / this.pageSize) || 1;
    }
  },
  watch: {
    data: {
      handler(newVal) {
        this.editedData = JSON.parse(JSON.stringify(newVal));
        this.updatePaginatedData();
      },
      immediate: true,
      deep: true
    },
    isEditing(val) {
      if (val) {
        // 进入编辑模式，复制当前数据
        this.editedData = JSON.parse(JSON.stringify(this.data));
      } else {
        // 退出编辑模式，重置数据
        this.editedData = JSON.parse(JSON.stringify(this.data));
      }
      this.updatePaginatedData();
    }
  },
  methods: {
    // 更新分页数据
    updatePaginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.paginatedData = this.isEditing 
        ? this.editedData.slice(start, end) 
        : this.data.slice(start, end);
      this.totalRecords = this.isEditing ? this.editedData.length : this.data.length;
    },
    
    // 更新表格数据（由父组件调用）
    updateData(data, pagination) {
      this.paginatedData = data;
      if (pagination) {
        this.currentPage = pagination.page;
        this.pageSize = pagination.size;
        this.totalRecords = pagination.total;
      } else {
        this.totalRecords = data.length;
      }
    },
    
    // 获取编辑后的数据（由父组件调用）
    getEditedData() {
      return this.editedData;
    },
    
    // 验证输入的数字
    validateNumber(value, row, index) {
      // 确保是数字且在合理范围内
      let num = parseFloat(value);
      if (isNaN(num)) {
        num = 0;
      } else if (num < 0) {
        num = 0;
      } else if (num > 1000) {
        num = 1000;
      }
      
      // 保留两位小数
      num = parseFloat(num.toFixed(2));
      
      // 更新数据
      row.prices[index] = num;
    },
    
    // 处理选择行变化
    handleSelectionChange(val) {
      this.$emit('selection-change', val);
    },
    
    // 处理编辑按钮点击
    handleEdit(row) {
      this.$emit('edit-row', row);
    },
    
    // 处理页码大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      // 切换每页条数时，保持在第一页
      this.currentPage = 1;
      this.$emit('page-change', { page: this.currentPage, size: val });
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.$emit('page-change', { page: val, size: this.pageSize });
    },
    
    // 跳转到指定页
    goToPage() {
      if (this.currentPage > this.totalPages) {
        this.currentPage = this.totalPages;
      } else if (this.currentPage < 1) {
        this.currentPage = 1;
      }
      this.$emit('page-change', { page: this.currentPage, size: this.pageSize });
    }
  }
};
</script>

<style scoped>
.unit-data-table {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.total-info {
  margin-right: 15px;
  color: #606266;
  font-size: 13px;
}

.page-info {
  margin: 0 15px;
  color: #606266;
  font-size: 13px;
}
</style>

<style>
/* 全局样式，不使用scoped */
/* 表格编辑样式 */
.el-table .cell .el-input,
.el-table .cell .el-select,
.el-table .cell .el-date-editor {
  margin: -5px 0;
}

/* 表格固定列样式 */
.el-table--border th.is-leaf {
  border-right: 1px solid #ebeef5;
}

.el-table--border td {
  border-right: 1px solid #ebeef5;
}
</style> 