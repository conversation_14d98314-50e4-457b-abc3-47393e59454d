<!-- eslint-disable -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="form" 
      label-width="100px"
      :label-position="'right'"
      size="small"
    >
      <el-form-item label="机组名称" prop="unitName">
        <el-input v-model="form.unitName" placeholder="请输入机组名称"></el-input>
      </el-form-item>
      
      <el-form-item label="报价日期" prop="priceDate">
        <el-date-picker
          v-model="form.priceDate"
          type="date"
          placeholder="选择报价日期"
          format="yyyyMMdd"
          value-format="yyyyMMdd"
          style="width: 100%;"
        ></el-date-picker>
      </el-form-item>
      
      <el-divider content-position="left">24小时分时报价</el-divider>
      
      <div class="price-grid">
        <div v-for="hour in 24" :key="hour" class="price-item">
          <el-form-item :label="`时段${hour}`" :prop="`prices.${hour-1}`">
            <el-input 
              v-model.number="form.prices[hour-1]" 
              placeholder="报价" 
              @input="validateNumber($event, hour-1)"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      
      <div class="price-actions">
        <el-button type="primary" size="small" @click="setAllPrices">批量设置</el-button>
        <el-button type="info" size="small" @click="resetPrices">重置</el-button>
      </div>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
    
    <!-- 批量设置对话框 -->
    <el-dialog
      title="批量设置报价"
      :visible.sync="batchDialogVisible"
      width="400px"
      append-to-body
    >
      <el-form :model="batchForm" :rules="batchRules" ref="batchForm" label-width="100px">
        <el-form-item label="起始时段" prop="startHour">
          <el-select v-model="batchForm.startHour" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="h in 24"
              :key="h"
              :label="`时段${h}`"
              :value="h"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="结束时段" prop="endHour">
          <el-select v-model="batchForm.endHour" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="h in 24"
              :key="h"
              :label="`时段${h}`"
              :value="h"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="报价值" prop="priceValue">
          <el-input v-model.number="batchForm.priceValue" placeholder="请输入报价值"></el-input>
        </el-form-item>
      </el-form>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="applyBatchPrices">确 定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: 'UnitEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'add'
    }
  },
  data() {
    // 验证报价值
    const validatePrice = (rule, value, callback) => {
      if (typeof value !== 'number') {
        callback(new Error('请输入数字'));
      } else if (value < 0) {
        callback(new Error('报价不能小于0'));
      } else {
        callback();
      }
    };
    
    return {
      dialogVisible: this.visible,
      form: {
        id: '',
        unitName: '',
        priceDate: new Date().toISOString().substr(0, 10).replace(/-/g, ''),
        prices: Array(24).fill(0.5)
      },
      rules: {
        unitName: [
          { required: true, message: '请输入机组名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        priceDate: [
          { required: true, message: '请选择报价日期', trigger: 'change' }
        ],
        'prices.0': [
          { required: true, type: 'number', message: '请输入报价', trigger: 'blur' },
          { validator: validatePrice, trigger: 'blur' }
        ]
      },
      batchDialogVisible: false,
      batchForm: {
        startHour: 1,
        endHour: 24,
        priceValue: 0.5
      },
      batchRules: {
        startHour: [
          { required: true, message: '请选择起始时段', trigger: 'change' }
        ],
        endHour: [
          { required: true, message: '请选择结束时段', trigger: 'change' }
        ],
        priceValue: [
          { required: true, type: 'number', message: '请输入报价值', trigger: 'blur' },
          { validator: validatePrice, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogTitle() {
      return this.mode === 'add' ? '添加机组报价' : '编辑机组报价';
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    editData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          // 确保prices数组包含24个值
          const prices = Array.isArray(val.prices) && val.prices.length === 24
            ? [...val.prices]
            : Array(24).fill(0.5);
          
          this.form = {
            ...val,
            prices
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleClose() {
      this.resetForm();
      this.dialogVisible = false;
    },
    
    resetForm() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
      
      // 重置表单
      this.form = {
        id: '',
        unitName: '',
        priceDate: new Date().toISOString().substr(0, 10).replace(/-/g, ''),
        prices: Array(24).fill(0.5)
      };
    },
    
    // 验证输入的数字
    validateNumber(value, index) {
      // 确保是数字且在合理范围内
      let num = parseFloat(value);
      if (isNaN(num)) {
        num = 0;
      } else if (num < 0) {
        num = 0;
      } else if (num > 1000) {
        num = 1000;
      }
      
      // 保留两位小数
      num = parseFloat(num.toFixed(2));
      
      // 更新数据
      this.form.prices.splice(index, 1, num);
    },
    
    // 打开批量设置对话框
    setAllPrices() {
      this.batchDialogVisible = true;
    },
    
    // 应用批量设置
    applyBatchPrices() {
      this.$refs.batchForm.validate(valid => {
        if (valid) {
          const { startHour, endHour, priceValue } = this.batchForm;
          
          // 验证开始和结束时段
          if (startHour > endHour) {
            this.$message.error('开始时段不能大于结束时段');
            return;
          }
          
          // 应用批量设置
          for (let i = startHour - 1; i < endHour; i++) {
            this.form.prices[i] = parseFloat(priceValue);
          }
          
          this.batchDialogVisible = false;
        }
      });
    },
    
    // 重置所有报价
    resetPrices() {
      this.form.prices = Array(24).fill(0.5);
    },
    
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 添加一个临时ID，如果是新增的话
          if (this.mode === 'add' && !this.form.id) {
            this.form.id = `temp_${Date.now()}`;
          }
          
          this.$emit('submit', this.form);
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style scoped>
.price-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
}

@media (max-width: 1200px) {
  .price-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .price-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.price-item {
  width: 100%;
}

.price-actions {
  margin-top: 20px;
  text-align: right;
}

.el-divider {
  margin: 24px 0;
}
</style> 