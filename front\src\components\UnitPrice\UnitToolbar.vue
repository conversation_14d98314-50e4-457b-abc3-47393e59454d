<!-- eslint-disable -->
<template>
  <div class="unit-toolbar">
    <div class="tabs-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="模型库" name="model"></el-tab-pane>
        <el-tab-pane label="资源库" name="resource"></el-tab-pane>
      </el-tabs>
    </div>
    
    <div class="button-container">
      <el-button 
        type="primary" 
        size="small" 
        icon="el-icon-plus" 
        @click="$emit('add')"
      >新增</el-button>
      
      <el-button 
        type="primary" 
        size="small" 
        icon="el-icon-edit" 
        :disabled="isEditing || selectedRows === 0" 
        @click="$emit('edit')"
      >修改</el-button>
      
      <el-button 
        type="info" 
        size="small" 
        icon="el-icon-close" 
        :disabled="!isEditing" 
        @click="$emit('cancel-edit')"
      >取消修改</el-button>
      
      <el-button 
        type="danger" 
        size="small" 
        icon="el-icon-delete" 
        :disabled="isEditing || selectedRows === 0" 
        @click="$emit('delete')"
      >删除</el-button>
      
      <el-button 
        type="success" 
        size="small" 
        icon="el-icon-check" 
        :disabled="!isEditing" 
        @click="$emit('save')"
      >保存</el-button>
      
      <el-button 
        type="success" 
        size="small" 
        icon="el-icon-download" 
        @click="$emit('export')"
      >导出Excel</el-button>
      
      <el-button 
        type="warning" 
        size="small" 
        icon="el-icon-upload2" 
        @click="handleImportClick"
      >导入Excel</el-button>
      
      <input
        ref="fileInput"
        type="file"
        accept=".xlsx, .xls"
        style="display: none;"
        @change="handleFileChange"
      />
      
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchText"
          placeholder="机组名称"
          clearable
          size="small"
          style="width: 200px;"
          @keyup.enter.native="handleSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
        </el-input>
        
        <el-button 
          type="primary" 
          size="small" 
          plain
          @click="handleReset"
        >重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnitToolbar',
  props: {
    isEditing: {
      type: Boolean,
      default: false
    },
    selectedRows: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeTab: 'model',
      searchText: ''
    };
  },
  methods: {
    handleImportClick() {
      this.$refs.fileInput.click();
    },
    
    handleFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;
      
      this.$emit('import', file);
      
      // 重置文件输入，以便于下次导入同一个文件
      this.$refs.fileInput.value = '';
    },
    
    handleSearch() {
      this.$emit('search', this.searchText);
    },
    
    handleReset() {
      this.searchText = '';
      this.$emit('search', '');
    }
  }
};
</script>

<style scoped>
.unit-toolbar {
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
}

.tabs-container {
  padding: 0 10px;
  border-bottom: 1px solid #e6e6e6;
}

.button-container {
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.button-container .el-button {
  margin-right: 10px;
  margin-bottom: 5px;
}

.search-box {
  display: flex;
  margin-left: auto;
}

.search-box .el-button {
  margin-left: 10px;
}
</style> 