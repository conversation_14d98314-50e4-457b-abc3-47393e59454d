<!-- eslint-disable -->
<template>
  <div class="unit-tree-menu">
    <div class="menu-header">
      <span>机组报价</span>
    </div>
    <el-tree
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      :highlight-current="true"
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
      default-expand-all
      ref="tree"
    >
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <i :class="getNodeIcon(data.type)" class="node-icon"></i>
        <span>{{ node.label }}</span>
      </span>
    </el-tree>
  </div>
</template>

<script>
export default {
  name: 'UnitTreeMenu',
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    };
  },
  methods: {
    handleNodeClick(data) {
      // 避免点击非叶子节点触发数据加载
      if (!data.children || data.children.length === 0) {
        this.$emit('node-click', data);
      }
    },
    
    getNodeIcon(type) {
      // 根据节点类型返回不同的图标
      const iconMap = {
        'category': 'el-icon-folder',
        'subcategory': 'el-icon-folder-opened',
        'unit': 'el-icon-cpu',
        'station': 'el-icon-office-building',
        'price': 'el-icon-price-tag',
        'default': 'el-icon-document'
      };
      
      return iconMap[type] || iconMap.default;
    }
  }
};
</script>

<style scoped>
.unit-tree-menu {
  width: 250px;
  border-right: 1px solid #e6e6e6;
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}

.menu-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
}

.node-icon {
  margin-right: 5px;
}
</style> 