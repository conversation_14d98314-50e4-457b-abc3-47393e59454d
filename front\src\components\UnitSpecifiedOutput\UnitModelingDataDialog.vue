<!-- eslint-disable -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="80%"
    @close="handleClose"
  >
    <div class="modeling-dialog-content">
      <div class="toolbar">
        <el-button-group>
            <el-button size="small" @click="handleModify" :disabled="isEditing">修改</el-button>
            <el-button size="small" @click="handleCancel" :disabled="!isEditing">取消修改</el-button>
            <el-button size="small" type="primary" @click="handleSave" :disabled="!isEditing">保存</el-button>
        </el-button-group>
        <el-button size="small" @click="exportData">导出Excel</el-button>
        <el-upload
          class="upload-btn"
          action=""
          :show-file-list="false"
          :before-upload="handleImport"
        >
          <el-button size="small">导入Excel</el-button>
        </el-upload>
        <el-button size="small" @click="clearData">清空</el-button>
      </div>
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        height="50vh"
      >
        <el-table-column prop="unitName" label="机组名称" min-width="250"></el-table-column>
        <el-table-column prop="startDate" label="起始日期" min-width="120"></el-table-column>
        <el-table-column prop="endDate" label="结束日期" min-width="120"></el-table-column>
        <el-table-column v-for="i in 24" :key="i" :label="`时段${i}(MW)`" :prop="`p${i}`" min-width="100">
            <template slot-scope="scope">
                <span v-if="!isEditing">{{ scope.row[`p${i}`] }}</span>
                <el-input-number 
                    v-else 
                    v-model="scope.row[`p${i}`]" 
                    :precision="2" 
                    :step="1"
                    size="small" 
                    style="width: 100%;"
                ></el-input-number>
            </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import * as unitModelingDataService from '@/services/unitModelingDataService';

export default {
  name: 'UnitModelingDataDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unitInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      isEditing: false,
      editingBackup: null,
    };
  },
  computed: {
      dialogTitle() {
          return this.unitInfo ? `机组建模数据 - ${this.unitInfo.unitName}` : '机组建模数据';
      }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val && this.unitInfo) {
        this.loadData();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    async loadData() {
      if (!this.unitInfo) return;
      this.loading = true;
      try {
        this.tableData = await unitModelingDataService.getModelingData(this.unitInfo.id, this.unitInfo.unitName);
      } catch (error) {
        this.$message.error('加载建模数据失败');
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    exportData() {
        if (!this.unitInfo) return;
        unitModelingDataService.exportModelingData(this.unitInfo.id, this.unitInfo.unitName);
    },
    async handleImport(file) {
      if (!this.unitInfo) return;
       try {
        await unitModelingDataService.importModelingData(this.unitInfo.id, file);
        this.$message.success('导入成功');
        this.loadData();
      } catch (error) {
        this.$message.error(error.message || '导入失败');
      }
    },
    async clearData() {
        if (!this.unitInfo) return;
         this.$confirm('确认清空该机组的所有建模数据吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(async () => {
            try {
                await unitModelingDataService.clearModelingData(this.unitInfo.id);
                this.$message.success('数据已清空');
                this.loadData();
            } catch (error) {
                this.$message.error('清空失败');
            }
        }).catch(() => {});
    },
    handleClose() {
      this.tableData = [];
      this.isEditing = false;
      this.editingBackup = null;
    },
    handleModify() {
      this.editingBackup = JSON.parse(JSON.stringify(this.tableData));
      this.isEditing = true;
    },
    handleCancel() {
      if (this.editingBackup) {
        this.tableData = this.editingBackup;
        this.editingBackup = null;
      }
      this.isEditing = false;
    },
    async handleSave() {
      if (!this.unitInfo) return;
      this.loading = true;
      try {
        await unitModelingDataService.saveModelingData(this.unitInfo.id, this.tableData);
        this.$message.success('保存成功');
        this.isEditing = false;
        this.editingBackup = null;
      } catch (error) {
        this.$message.error('保存失败');
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.modeling-dialog-content .toolbar {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}
.upload-btn {
  display: inline-block;
}
.dialog-footer {
  text-align: right;
}
</style> 