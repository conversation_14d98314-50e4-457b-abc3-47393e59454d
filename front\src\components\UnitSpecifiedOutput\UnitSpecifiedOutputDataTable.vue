<!-- eslint-disable -->
<template>
  <div class="data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="unitName" label="机组名称" min-width="300"></el-table-column>
      <el-table-column prop="constraintType" label="约束类型" min-width="150"></el-table-column>
      <el-table-column prop="hours" label="利用小时数" min-width="150"></el-table-column>
      <el-table-column prop="energy" label="累计电量" min-width="150"></el-table-column>
      <el-table-column prop="modelingCount" label="建模数" min-width="120" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="openModelingDialog(scope.row)">{{ scope.row.modelingCount }}</el-link>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { unitSpecifiedOutputService } from '@/services/unitSpecifiedOutputService';

export default {
  name: 'UnitSpecifiedOutputDataTable',
  props: {
    searchText: {
        type: String,
        default: ''
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 20
    };
  },
  computed: {
    filteredData() {
        if (!this.searchText) {
            return this.tableData;
        }
        return this.tableData.filter(item => 
            item.unitName.toLowerCase().includes(this.searchText.toLowerCase())
        );
    },
    totalRecords() {
      return this.filteredData.length;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredData.slice(start, end);
    }
  },
  watch: {
      searchText() {
          this.currentPage = 1;
      }
  },
  methods: {
    handleSelectionChange(rows) {
      this.$emit('selection-change', rows);
    },
    async loadData() {
      try {
        this.loading = true;
        this.tableData = await unitSpecifiedOutputService.getData();
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    openModelingDialog(row) {
      this.$emit('open-modeling-dialog', row);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; 
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  },
  mounted() {
    this.loadData();
  }
};
</script>

<style scoped>
.data-table {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.total-info {
  font-size: 14px;
  color: #606266;
}
</style> 