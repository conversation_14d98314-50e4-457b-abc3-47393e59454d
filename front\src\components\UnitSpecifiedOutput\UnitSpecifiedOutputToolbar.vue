<!-- eslint-disable -->
<template>
  <div class="toolbar">
    <div class="left">
      <el-dropdown @command="handleBatchCommand" trigger="click">
        <el-button size="small" type="primary">
          批量维护<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="import">导入</el-dropdown-item>
          <el-dropdown-item command="export">导出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      
      <el-upload
        ref="upload"
        class="upload-btn"
        action=""
        :show-file-list="false"
        :before-upload="handleBeforeUpload"
        style="display: none;"
      ></el-upload>
      
      <el-button size="small" type="danger" @click="handleDelete" :disabled="!canDelete">删除</el-button>
    </div>
    <div class="right">
        <el-input
            v-model="searchText"
            placeholder="机组名称"
            size="small"
            clearable
            @clear="handleSearch"
            @keyup.enter.native="handleSearch"
            style="width: 200px; margin-right: 10px;"
        ></el-input>
        <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
        <el-button size="small" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnitSpecifiedOutputToolbar',
  props: {
    canDelete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchText: ''
    };
  },
  methods: {
    handleBatchCommand(command) {
      if (command === 'import') {
        this.$refs.upload.$children[0].$el.click();
      } else if (command === 'export') {
        this.$emit('batch-export');
      }
    },
    handleBeforeUpload(file) {
      this.$emit('batch-import', file);
      return false; // Prevent auto-upload
    },
    handleDelete() {
      this.$confirm('确认删除选中的记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete');
      }).catch(() => {});
    },
    handleSearch() {
        this.$emit('search', this.searchText);
    },
    handleReset() {
        this.searchText = '';
        this.$emit('search', this.searchText);
    }
  }
};
</script>

<style scoped>
.toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left, .right {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style> 