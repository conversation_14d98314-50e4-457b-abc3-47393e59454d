<!-- eslint-disable -->
<template>
  <div class="unit-specified-output">
    <unit-specified-output-toolbar
      @batch-import="handleBatchImport"
      @batch-export="handleBatchExport"
      @delete="handleDelete"
      @search="handleSearch"
      :can-delete="!!selectedRows.length"
    ></unit-specified-output-toolbar>
    
    <unit-specified-output-data-table
      ref="dataTable"
      @selection-change="handleSelectionChange"
      @open-modeling-dialog="openModelingDialog"
      :search-text="searchText"
    ></unit-specified-output-data-table>

    <unit-modeling-data-dialog
      :visible.sync="modelingDialogVisible"
      :unit-info="selectedUnit"
    ></unit-modeling-data-dialog>
  </div>
</template>

<script>
import UnitSpecifiedOutputToolbar from './UnitSpecifiedOutputToolbar.vue';
import UnitSpecifiedOutputDataTable from './UnitSpecifiedOutputDataTable.vue';
import UnitModelingDataDialog from './UnitModelingDataDialog.vue'; // Will be created
import { unitSpecifiedOutputService } from '@/services/unitSpecifiedOutputService';

export default {
  name: 'UnitSpecifiedOutput',
  components: {
    UnitSpecifiedOutputToolbar,
    UnitSpecifiedOutputDataTable,
    UnitModelingDataDialog
  },
  data() {
    return {
      selectedRows: [],
      searchText: '',
      modelingDialogVisible: false,
      selectedUnit: null,
    };
  },
  methods: {
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    handleBatchImport(file) {
      unitSpecifiedOutputService.batchImport(file)
        .then(() => {
          this.$message.success('批量导入成功');
          this.$refs.dataTable.loadData();
        })
        .catch(err => {
          this.$message.error(err.message || '批量导入失败');
        });
    },
    handleBatchExport() {
      unitSpecifiedOutputService.batchExport()
        .then(() => {
          this.$message.success('批量导出成功');
        })
        .catch(err => {
          this.$message.error(err.message || '批量导出失败');
        });
    },
    async handleDelete() {
      try {
        const ids = this.selectedRows.map(row => row.id);
        await unitSpecifiedOutputService.deleteData(ids);
        this.$message.success('删除成功');
        this.$refs.dataTable.loadData();
        this.selectedRows = [];
      } catch (error) {
        this.$message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    handleSearch(text) {
      this.searchText = text;
    },
    openModelingDialog(unit) {
      this.selectedUnit = unit;
      this.modelingDialogVisible = true;
    }
  }
};
</script>

<style scoped>
.unit-specified-output {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}
</style> 