<!-- eslint-disable -->
<template>
  <div class="unit-data-table">
    <el-table
      :data="data"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :row-class-name="rowClassName"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      
      <el-table-column
        prop="unitName"
        label="机组名称"
        min-width="180"
      ></el-table-column>
      
      <el-table-column
        prop="startDate"
        label="起始日期"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!isEditing">{{ scope.row.startDate }}</span>
          <el-date-picker
            v-else
            v-model="scope.row.startDate"
            type="date"
            placeholder="选择日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd"
            size="small"
            style="width: 120px;"
          ></el-date-picker>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="endDate"
        label="结束日期"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!isEditing">{{ scope.row.endDate }}</span>
          <el-date-picker
            v-else
            v-model="scope.row.endDate"
            type="date"
            placeholder="选择日期"
            format="yyyyMMdd"
            value-format="yyyyMMdd"
            size="small"
            style="width: 120px;"
          ></el-date-picker>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="status"
        label="指定状态"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!isEditing">{{ scope.row.status }}</span>
          <el-select
            v-else
            v-model="scope.row.status"
            placeholder="请选择"
            size="small"
            style="width: 120px;"
          >
            <el-option label="指定关机" value="指定关机"></el-option>
            <el-option label="指定开机" value="指定开机"></el-option>
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="80"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnitDataTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        size: 10,
        total: 0
      })
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
  },
  watch: {
    'pagination': {
      handler(val) {
        this.currentPage = val.page;
        this.pageSize = val.size;
        this.total = val.total;
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.$emit('selection-change', val);
    },
    
    handleEdit(row) {
      this.$emit('edit-row', row);
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.$emit('page-change', { page: this.currentPage, size: val });
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.$emit('page-change', { page: val, size: this.pageSize });
    },
    
    rowClassName({ row }) {
      // 根据不同的状态设置不同的样式
      if (row.status === '指定关机') {
        return 'status-shutdown';
      } else if (row.status === '指定开机') {
        return 'status-running';
      }
      return '';
    }
  }
};
</script>

<style scoped>
.unit-data-table {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style>

<style>
/* 全局样式，不使用scoped */
.status-shutdown {
  background-color: #fef0f0;
}

.status-running {
  background-color: #f0f9eb;
}

/* 表格编辑样式 */
.el-table .cell .el-input,
.el-table .cell .el-select,
.el-table .cell .el-date-editor {
  margin: -5px 0;
}
</style> 