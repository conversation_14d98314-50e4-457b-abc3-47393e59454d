<!-- eslint-disable -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="form" 
      label-width="100px"
      :label-position="'right'"
      size="small"
    >
      <el-form-item label="机组名称" prop="unitName">
        <el-input v-model="form.unitName" placeholder="请输入机组名称"></el-input>
      </el-form-item>
      
      <el-form-item label="起始日期" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          placeholder="选择起始日期"
          format="yyyyMMdd"
          value-format="yyyyMMdd"
          style="width: 100%;"
        ></el-date-picker>
      </el-form-item>
      
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          placeholder="选择结束日期"
          format="yyyyMMdd"
          value-format="yyyyMMdd"
          style="width: 100%;"
        ></el-date-picker>
      </el-form-item>
      
      <el-form-item label="指定状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
          <el-option label="指定关机" value="指定关机"></el-option>
          <el-option label="指定开机" value="指定开机"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'UnitEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'add'
    }
  },
  data() {
    // 验证日期格式
    const validateDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择日期'));
      } else {
        if (this.form.startDate && this.form.endDate) {
          if (this.form.startDate > this.form.endDate) {
            callback(new Error('结束日期不能早于开始日期'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    };
    
    return {
      dialogVisible: this.visible,
      form: {
        id: '',
        unitName: '',
        startDate: '',
        endDate: '',
        status: '指定关机'
      },
      rules: {
        unitName: [
          { required: true, message: '请输入机组名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择起始日期', trigger: 'change' },
          { validator: validateDate, trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
          { validator: validateDate, trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择指定状态', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    dialogTitle() {
      return this.mode === 'add' ? '添加机组状态' : '编辑机组状态';
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    editData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.form = { ...val };
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleClose() {
      this.resetForm();
      this.dialogVisible = false;
    },
    
    resetForm() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
      
      // 重置表单
      this.form = {
        id: '',
        unitName: '',
        startDate: '',
        endDate: '',
        status: '指定关机'
      };
    },
    
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 添加一个临时ID，如果是新增的话
          if (this.mode === 'add' && !this.form.id) {
            this.form.id = `temp_${Date.now()}`;
          }
          
          this.$emit('submit', this.form);
        } else {
          return false;
        }
      });
    }
  }
};
</script> 