<!-- eslint-disable -->
<template>
  <div class="unit-status-container">
    <!-- 左侧树形导航 -->
    <unit-tree-menu 
      :treeData="treeData"
      @node-click="handleNodeClick"
    />
    
    <!-- 右侧内容区 -->
    <div class="content-area">
      <!-- 工具栏 -->
      <unit-toolbar 
        @add="handleAdd"
        @edit="handleEdit"
        @cancel-edit="handleCancelEdit"
        @delete="handleDelete"
        @save="handleSave"
        @export="handleExport"
        @import="handleImport"
        :isEditing="isEditing"
        :selectedRows="selectedRows.length"
      />
      
      <!-- 表格区域 -->
      <unit-data-table
        :data="tableData"
        :loading="loading"
        :isEditing="isEditing"
        @selection-change="handleSelectionChange"
        @edit-row="handleEditRow"
        @page-change="handlePageChange"
      />
      
      <!-- 编辑对话框 -->
      <unit-edit-dialog
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        :editData="editData"
        :mode="dialogMode"
        @submit="handleDialogSubmit"
      />
    </div>
  </div>
</template>

<script>
import UnitTreeMenu from './UnitTreeMenu.vue';
import UnitToolbar from './UnitToolbar.vue';
import UnitDataTable from './UnitDataTable.vue';
import UnitEditDialog from './UnitEditDialog.vue';
import { unitDataService } from '@/services/unitDataService';

export default {
  name: 'UnitStatus',
  components: {
    UnitTreeMenu,
    UnitToolbar,
    UnitDataTable,
    UnitEditDialog
  },
  data() {
    return {
      treeData: [],
      tableData: [],
      loading: false,
      isEditing: false,
      selectedRows: [],
      currentNode: null,
      dialogVisible: false,
      dialogMode: 'add', // 'add' 或 'edit'
      editData: {},
      pagination: {
        page: 1,
        size: 10,
        total: 0
      }
    };
  },
  created() {
    // 加载初始数据
    this.loadTreeData();
  },
  methods: {
    // 加载树形菜单数据
    loadTreeData() {
      this.loading = true;
      unitDataService.getMenuTree()
        .then(data => {
          this.treeData = data;
          this.loading = false;
        })
        .catch(error => {
          console.error('加载菜单数据失败:', error);
          this.$message.error('加载菜单数据失败');
          this.loading = false;
        });
    },
    
    // 处理节点点击
    handleNodeClick(node) {
      this.currentNode = node;
      this.loadTableData();
    },
    
    // 加载表格数据
    loadTableData() {
      if (!this.currentNode) return;
      
      this.loading = true;
      unitDataService.getUnitData(this.currentNode.id, this.pagination)
        .then(response => {
          this.tableData = response.data;
          this.pagination.total = response.total;
          this.loading = false;
        })
        .catch(error => {
          console.error('加载表格数据失败:', error);
          this.$message.error('加载表格数据失败');
          this.loading = false;
        });
    },
    
    // 处理选择行变化
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    
    // 处理添加
    handleAdd() {
      this.dialogMode = 'add';
      this.editData = {
        unitName: '',
        startDate: '',
        endDate: '',
        status: '指定关机'
      };
      this.dialogVisible = true;
    },
    
    // 处理编辑按钮点击
    handleEdit() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条记录进行编辑');
        return;
      }
      
      this.isEditing = true;
    },
    
    // 处理取消编辑
    handleCancelEdit() {
      this.isEditing = false;
      this.loadTableData(); // 重新加载数据，放弃修改
    },
    
    // 处理删除
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录');
        return;
      }
      
      this.$confirm(`确认删除选中的 ${this.selectedRows.length} 条记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        unitDataService.deleteUnits(ids)
          .then(() => {
            this.$message.success('删除成功');
            this.loadTableData();
          })
          .catch(error => {
            console.error('删除失败:', error);
            this.$message.error('删除失败');
          });
      }).catch(() => {
        // 用户取消删除操作
      });
    },
    
    // 处理保存
    handleSave() {
      unitDataService.saveUnitData(this.tableData)
        .then(() => {
          this.$message.success('保存成功');
          this.isEditing = false;
          this.loadTableData();
        })
        .catch(error => {
          console.error('保存失败:', error);
          this.$message.error('保存失败');
        });
    },
    
    // 处理导出
    handleExport() {
      unitDataService.exportUnitData(this.currentNode.id)
        .then(data => {
          // 导出逻辑
        })
        .catch(error => {
          console.error('导出失败:', error);
          this.$message.error('导出失败');
        });
    },
    
    // 处理导入
    handleImport(file) {
      unitDataService.importUnitData(file, this.currentNode.id)
        .then(() => {
          this.$message.success('导入成功');
          this.loadTableData();
        })
        .catch(error => {
          console.error('导入失败:', error);
          this.$message.error('导入失败');
        });
    },
    
    // 处理行编辑
    handleEditRow(row) {
      this.dialogMode = 'edit';
      this.editData = { ...row };
      this.dialogVisible = true;
    },
    
    // 处理对话框提交
    handleDialogSubmit(formData) {
      if (this.dialogMode === 'add') {
        unitDataService.addUnit(formData)
          .then(() => {
            this.$message.success('添加成功');
            this.dialogVisible = false;
            this.loadTableData();
          })
          .catch(error => {
            console.error('添加失败:', error);
            this.$message.error('添加失败');
          });
      } else {
        unitDataService.updateUnit(formData)
          .then(() => {
            this.$message.success('更新成功');
            this.dialogVisible = false;
            this.loadTableData();
          })
          .catch(error => {
            console.error('更新失败:', error);
            this.$message.error('更新失败');
          });
      }
    },
    
    // 处理页码变化
    handlePageChange(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
      this.loadTableData();
    }
  }
};
</script>

<style scoped>
.unit-status-container {
  display: flex;
  height: 100%;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow: hidden;
}
</style> 