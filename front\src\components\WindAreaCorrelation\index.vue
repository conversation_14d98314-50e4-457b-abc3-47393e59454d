<!-- eslint-disable -->
<template>
  <div class="wind-area-correlation">
    <wind-area-correlation-toolbar
      @modify="enableEditing"
      @cancel="cancelEditing"
      @save="saveChanges"
      @import="handleImport"
      @export="handleExport"
      :is-editing="isEditing"
      @search="handleSearch"
      @delete="handleDelete"
      :can-delete="!!selectedRows.length"
    ></wind-area-correlation-toolbar>
    
    <wind-area-correlation-data-table
      ref="dataTable"
      :is-editing="isEditing"
      :search-text="searchText"
      @selection-change="handleSelectionChange"
    ></wind-area-correlation-data-table>
  </div>
</template>

<script>
import WindAreaCorrelationToolbar from './WindAreaCorrelationToolbar.vue';
import WindAreaCorrelationDataTable from './WindAreaCorrelationDataTable.vue';
import * as windAreaCorrelationService from '@/services/windAreaCorrelationService';

export default {
  name: 'WindAreaCorrelation',
  components: {
    WindAreaCorrelationToolbar,
    WindAreaCorrelationDataTable
  },
  data() {
    return {
      isEditing: false,
      searchText: '',
      selectedRows: []
    };
  },
  methods: {
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    enableEditing() {
      this.isEditing = true;
      this.$refs.dataTable.startEditing();
    },
    cancelEditing() {
      this.isEditing = false;
      this.$refs.dataTable.cancelEditing();
    },
    async saveChanges() {
      try {
        await this.$refs.dataTable.saveChanges();
        this.isEditing = false;
        this.$message.success('保存成功');
      } catch (error) {
        this.$message.error('保存失败');
        console.error('保存失败:', error);
      }
    },
    async handleDelete() {
      try {
        const ids = this.selectedRows.map(row => row.id);
        await windAreaCorrelationService.deleteData(ids);
        this.$message.success('删除成功');
        this.$refs.dataTable.loadData();
        this.selectedRows = [];
      } catch (error) {
        this.$message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    handleSearch(text) {
        this.searchText = text;
    },
    handleImport(file) {
      windAreaCorrelationService.importData(file)
        .then(() => {
          this.$message.success('导入成功');
          this.$refs.dataTable.loadData();
        })
        .catch(error => {
          this.$message.error(error.message || '导入失败');
          console.error('导入失败:', error);
        });
    },
    handleExport() {
      windAreaCorrelationService.exportData()
        .then(() => {
          this.$message.success('导出成功');
        })
        .catch(error => {
          this.$message.error('导出失败');
          console.error('导出失败:', error);
        });
    }
  }
};
</script>

<style scoped>
.wind-area-correlation {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}
</style> 