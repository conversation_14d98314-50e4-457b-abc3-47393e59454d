<!-- eslint-disable -->
<template>
  <div class="wind-area-data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
      <el-table-column prop="areaName" label="风区名称" min-width="150" fixed="left"></el-table-column>
      <el-table-column prop="startDate" label="起始日期" min-width="120"></el-table-column>
      <el-table-column prop="weibullC" label="风速Weibull分布参数c" min-width="180"></el-table-column>
      <el-table-column prop="weibullK" label="风速Weibull分布参数k" min-width="180"></el-table-column>
      <el-table-column prop="autoCorrelationDecay" label="风速自相关函数衰减系数" min-width="200"></el-table-column>
      
      <el-table-column label="月平均风速(标幺值)">
        <el-table-column v-for="i in 12" :key="'month' + i" :prop="`avgWindSpeedM${i}`" :label="`${i}月`" min-width="100"></el-table-column>
      </el-table-column>
      
      <el-table-column label="时段平均风速(标幺值)">
        <el-table-column v-for="i in 24" :key="'period' + i" :prop="`avgWindSpeedP${i}`" :label="`时段${i}`" min-width="100"></el-table-column>
      </el-table-column>

      <el-table-column
        label="操作"
        width="150"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { windAreaService } from '@/services/windAreaService';

export default {
  name: 'WindAreaDataTable',
  props: {
    selectedRegion: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 20
    };
  },
  computed: {
    totalRecords() {
      return this.tableData.length;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    async loadData() {
      try {
        this.loading = true;
        // The service and method need to be created
        const data = await windAreaService.getWindAreaData(this.selectedRegion);
        this.tableData = data;
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    handleSelectionChange(rows) {
      this.$emit('selection-change', rows);
    },
    handleEdit(row) {
      // In parent component, this will trigger the dialog
      this.$parent.editData = row;
      this.$parent.editDialogVisible = true;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; 
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  },
  mounted() {
    this.loadData();
  }
};
</script>

<style scoped>
.wind-area-data-table {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.total-info {
  font-size: 14px;
  color: #606266;
}
</style> 