<!-- eslint-disable -->
<template>
  <el-dialog
    :title="editData ? '编辑风区' : '新增风区'"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="200px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="风区名称" prop="areaName">
            <el-input v-model="form.areaName" placeholder="请输入风区名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="起始日期" prop="startDate">
            <el-date-picker v-model="form.startDate" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="风速Weibull分布参数c" prop="weibullC">
            <el-input-number v-model="form.weibullC" :precision="6" :step="0.1" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风速Weibull分布参数k" prop="weibullK">
            <el-input-number v-model="form.weibullK" :precision="6" :step="0.1" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="风速自相关函数衰减系数" prop="autoCorrelationDecay">
        <el-input-number v-model="form.autoCorrelationDecay" :precision="6" :step="0.01" style="width: 100%;"></el-input-number>
      </el-form-item>

      <el-divider>月平均风速(标幺值)</el-divider>
      <el-row>
        <el-col :span="8" v-for="i in 12" :key="'month' + i">
          <el-form-item :label="`${i}月`" :prop="`avgWindSpeedM${i}`">
            <el-input-number v-model="form[`avgWindSpeedM${i}`]" :precision="6" :step="0.01" style="width: 90%;"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider>时段平均风速(标幺值)</el-divider>
      <el-row>
        <el-col :span="6" v-for="i in 24" :key="'period' + i">
          <el-form-item :label="`时段${i}`" :prop="`avgWindSpeedP${i}`">
            <el-input-number v-model="form[`avgWindSpeedP${i}`]" :precision="6" :step="0.01" style="width: 90%;"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'WindAreaEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    const generateRules = (prefix, count, message) => {
      const rules = {};
      for (let i = 1; i <= count; i++) {
        rules[`${prefix}${i}`] = [{ required: true, message: `请输入${message}${i}`, trigger: 'blur' }];
      }
      return rules;
    };

    return {
      dialogVisible: false,
      form: this.getInitialForm(),
      rules: {
        areaName: [{ required: true, message: '请输入风区名称', trigger: 'blur' }],
        startDate: [{ required: true, message: '请选择起始日期', trigger: 'change' }],
        weibullC: [{ required: true, message: '请输入风速Weibull分布参数c', trigger: 'blur' }],
        weibullK: [{ required: true, message: '请输入风速Weibull分布参数k', trigger: 'blur' }],
        autoCorrelationDecay: [{ required: true, message: '请输入风速自相关函数衰减系数', trigger: 'blur' }],
        ...generateRules('avgWindSpeedM', 12, '月平均风速'),
        ...generateRules('avgWindSpeedP', 24, '时段平均风速')
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    editData: {
      handler(val) {
        if (val) {
          this.form = { ...this.getInitialForm(), ...val };
        } else {
          this.resetForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    getInitialForm() {
        const form = {
            areaName: '',
            startDate: null,
            weibullC: 0,
            weibullK: 0,
            autoCorrelationDecay: 0,
        };
        for (let i = 1; i <= 12; i++) {
            form[`avgWindSpeedM${i}`] = 0;
        }
        for (let i = 1; i <= 24; i++) {
            form[`avgWindSpeedP${i}`] = 0;
        }
        return form;
    },
    resetForm() {
      this.form = this.getInitialForm();
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const submitData = { ...this.form };
          if (this.editData) {
            submitData.id = this.editData.id;
          }
          this.$emit('submit', submitData);
        }
      });
    }
  }
};
</script>

<style scoped>
.el-input-number {
  width: 100%;
}
</style> 