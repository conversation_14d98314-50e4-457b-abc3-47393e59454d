<!-- eslint-disable -->
<template>
  <div class="wind-area">
    <wind-area-toolbar
      @add="handleAdd"
      @delete="handleDelete"
      @import="handleImport"
      @export="handleExport"
      :can-delete="!!selectedRows.length"
    ></wind-area-toolbar>
    
    <wind-area-data-table
      ref="dataTable"
      :selected-region="selectedRegion"
      @selection-change="handleSelectionChange"
    ></wind-area-data-table>
    
    <wind-area-edit-dialog
      :visible.sync="editDialogVisible"
      :edit-data="editData"
      @submit="handleSubmit"
    ></wind-area-edit-dialog>
  </div>
</template>

<script>
import WindAreaToolbar from './WindAreaToolbar.vue';
import WindAreaDataTable from './WindAreaDataTable.vue';
import WindAreaEditDialog from './WindAreaEditDialog.vue';
import { windAreaService } from '@/services/windAreaService';

export default {
  name: 'WindArea',
  components: {
    WindAreaToolbar,
    WindAreaDataTable,
    WindAreaEditDialog
  },
  data() {
    return {
      selectedRows: [],
      editDialogVisible: false,
      editData: null,
      selectedRegion: ''
    };
  },
  methods: {
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    handleAdd() {
      this.editData = null;
      this.editDialogVisible = true;
    },
    async handleDelete() {
      try {
        const ids = this.selectedRows.map(row => row.id);
        await windAreaService.deleteWindAreas(ids);
        this.$message.success('删除成功');
        this.$refs.dataTable.loadData();
        this.selectedRows = [];
      } catch (error) {
        this.$message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    handleImport(file) {
      windAreaService.importWindAreaData(file)
        .then(() => {
          this.$message.success('导入成功');
          this.$refs.dataTable.loadData();
        })
        .catch(error => {
          this.$message.error(error.message || '导入失败');
          console.error('导入失败:', error);
        });
    },
    handleExport() {
      windAreaService.exportWindAreaData(this.selectedRegion)
        .then(() => {
          this.$message.success('导出成功');
        })
        .catch(error => {
          this.$message.error('导出失败');
          console.error('导出失败:', error);
        });
    },
    async handleSubmit(data) {
      try {
        if (data.id) {
          await windAreaService.updateWindArea(data);
          this.$message.success('更新成功');
        } else {
          await windAreaService.addWindArea(data);
          this.$message.success('添加成功');
        }
        this.editDialogVisible = false;
        this.$refs.dataTable.loadData();
      } catch (error) {
        this.$message.error(data.id ? '更新失败' : '添加失败');
        console.error(data.id ? '更新失败:' : '添加失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.wind-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}
</style> 