<!-- eslint-disable -->
<template>
  <div class="wind-data-table">
    <el-table
      :data="paginatedData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      height="calc(100vh - 240px)"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      
      <el-table-column
        prop="stationName"
        label="风电场名称"
        min-width="200"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.stationName }}</span>
          <el-input v-else v-model="scope.row.stationName" size="small"></el-input>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="region"
        label="所在风区"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.region }}</span>
          <el-select v-else v-model="scope.row.region" placeholder="请选择" size="small" style="width: 100%;">
            <el-option label="默认区域" value="默认区域"></el-option>
            <el-option label="珠江区域" value="珠江区域"></el-option>
            <el-option label="粤东区域" value="粤东区域"></el-option>
            <el-option label="粤西区域" value="粤西区域"></el-option>
            <el-option label="粤北区域" value="粤北区域"></el-option>
          </el-select>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="stationCode"
        label="所属节点"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.stationCode }}</span>
          <el-input v-else v-model="scope.row.stationCode" size="small"></el-input>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="cutInSpeed"
        label="风机切入风速"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.cutInSpeed }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.cutInSpeed" 
            :min="0" 
            :precision="1" 
            :step="0.1"
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="ratedSpeed"
        label="风机额定风速"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.ratedSpeed }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.ratedSpeed" 
            :min="0" 
            :precision="1" 
            :step="0.1"
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="cutOutSpeed"
        label="风机切出风速"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.cutOutSpeed }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.cutOutSpeed" 
            :min="0" 
            :precision="1" 
            :step="0.1"
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="randomFactor"
        label="风电场尾流系数"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.randomFactor }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.randomFactor" 
            :min="0" 
            :max="1" 
            :precision="2" 
            :step="0.01"
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="availability"
        label="风机可用率"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.editing">{{ scope.row.availability }}</span>
          <el-input-number 
            v-else 
            v-model="scope.row.availability" 
            :min="0" 
            :max="1" 
            :precision="2" 
            :step="0.01"
            size="small" 
            style="width: 100%;"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="150"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.editing"
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <template v-else>
            <el-button
              type="text"
              size="small"
              @click="handleSave(scope.row)"
            >保存</el-button>
            <el-button
              type="text"
              size="small"
              @click="handleCancel(scope.row)"
            >取消</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ totalRecords }} 条数据</span>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="sizes, prev, pager, next, jumper"
        :total="totalRecords"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { windStationService } from '@/services/windStationService';

export default {
  name: 'WindDataTable',
  props: {
    selectedRegion: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      editingBackup: null
    };
  },
  computed: {
    totalRecords() {
      return this.tableData.length;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    async loadData() {
      try {
        this.loading = true;
        const data = await windStationService.getWindStationData(this.selectedRegion);
        this.tableData = data;
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    handleSelectionChange(rows) {
      this.$emit('selection-change', rows);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    handleEdit(row) {
      this.editingBackup = { ...row };
      this.$set(row, 'editing', true);
    },
    async handleSave(row) {
      try {
        await windStationService.updateWindStation(row);
        this.$set(row, 'editing', false);
        this.editingBackup = null;
        this.$message.success('更新成功');
        await this.loadData();
      } catch (error) {
        console.error('更新失败:', error);
        this.$message.error('更新失败');
      }
    },
    handleCancel(row) {
      Object.assign(row, this.editingBackup);
      this.$set(row, 'editing', false);
      this.editingBackup = null;
    }
  },
  created() {
    this.loadData();
  },
  watch: {
    selectedRegion: {
      handler() {
        this.loadData();
      }
    }
  }
};
</script>

<style scoped>
.wind-data-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pagination-container {
  padding: 10px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.total-info {
  margin-right: 15px;
  color: #606266;
}
</style> 