<!-- eslint-disable -->
<template>
  <el-dialog
    :title="editData ? '编辑风电场' : '新增风电场'"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="风电场名称" prop="stationName">
        <el-input v-model="form.stationName" placeholder="请输入风电场名称"></el-input>
      </el-form-item>
      
      <el-form-item label="所在风区" prop="region">
        <el-select v-model="form.region" placeholder="请选择所在风区" style="width: 100%;">
          <el-option label="默认区域" value="默认区域"></el-option>
          <el-option label="珠江区域" value="珠江区域"></el-option>
          <el-option label="粤东区域" value="粤东区域"></el-option>
          <el-option label="粤西区域" value="粤西区域"></el-option>
          <el-option label="粤北区域" value="粤北区域"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="所属节点" prop="stationCode">
        <el-input v-model="form.stationCode" placeholder="请输入所属节点"></el-input>
      </el-form-item>
      
      <el-form-item label="切入风速" prop="cutInSpeed">
        <el-input-number 
          v-model="form.cutInSpeed" 
          :min="0" 
          :precision="1" 
          :step="0.1"
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="额定风速" prop="ratedSpeed">
        <el-input-number 
          v-model="form.ratedSpeed" 
          :min="0" 
          :precision="1" 
          :step="0.1"
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="切出风速" prop="cutOutSpeed">
        <el-input-number 
          v-model="form.cutOutSpeed" 
          :min="0" 
          :precision="1" 
          :step="0.1"
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="随机系数" prop="randomFactor">
        <el-input-number 
          v-model="form.randomFactor" 
          :min="0" 
          :max="1" 
          :precision="2" 
          :step="0.01"
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="可用率" prop="availability">
        <el-input-number 
          v-model="form.availability" 
          :min="0" 
          :max="1" 
          :precision="2" 
          :step="0.01"
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'WindEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        stationName: '',
        region: '',
        stationCode: '',
        cutInSpeed: 3.5,
        ratedSpeed: 12,
        cutOutSpeed: 25,
        randomFactor: 0.98,
        availability: 0.95
      },
      rules: {
        stationName: [
          { required: true, message: '请输入风电场名称', trigger: 'blur' }
        ],
        region: [
          { required: true, message: '请选择所在风区', trigger: 'change' }
        ],
        stationCode: [
          { required: true, message: '请输入所属节点', trigger: 'blur' }
        ],
        cutInSpeed: [
          { required: true, message: '请输入切入风速', trigger: 'blur' }
        ],
        ratedSpeed: [
          { required: true, message: '请输入额定风速', trigger: 'blur' }
        ],
        cutOutSpeed: [
          { required: true, message: '请输入切出风速', trigger: 'blur' }
        ],
        randomFactor: [
          { required: true, message: '请输入随机系数', trigger: 'blur' }
        ],
        availability: [
          { required: true, message: '请输入可用率', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    editData: {
      handler(val) {
        if (val) {
          this.form = { ...val };
        } else {
          this.resetForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    resetForm() {
      this.form = {
        stationName: '',
        region: '',
        stationCode: '',
        cutInSpeed: 3.5,
        ratedSpeed: 12,
        cutOutSpeed: 25,
        randomFactor: 0.98,
        availability: 0.95
      };
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const submitData = { ...this.form };
          if (this.editData) {
            submitData.id = this.editData.id;
          }
          this.$emit('submit', submitData);
        }
      });
    }
  }
};
</script>

<style scoped>
.el-input-number {
  width: 100%;
}
</style> 