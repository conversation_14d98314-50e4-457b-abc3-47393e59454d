<!-- eslint-disable -->
<template>
  <div class="wind-toolbar">
    <div class="left">
      <el-button type="primary" size="small" @click="handleAdd">
        <i class="el-icon-plus"></i> 新增
      </el-button>
      <el-button type="danger" size="small" :disabled="!canDelete" @click="handleDelete">
        <i class="el-icon-delete"></i> 删除
      </el-button>
      <el-upload
        class="upload-btn"
        action=""
        :show-file-list="false"
        :before-upload="handleImport"
      >
        <el-button size="small">
          <i class="el-icon-upload2"></i> 导入Excel
        </el-button>
      </el-upload>
      <el-button size="small" @click="handleExport">
        <i class="el-icon-download"></i> 导出Excel
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WindToolbar',
  props: {
    canDelete: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleAdd() {
      this.$emit('add');
    },
    handleDelete() {
      this.$confirm('确认删除选中的记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete');
      }).catch(() => {});
    },
    handleImport(file) {
      if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && 
          file.type !== 'application/vnd.ms-excel') {
        this.$message.error('只能上传Excel文件!');
        return false;
      }
      this.$emit('import', file);
      return false;
    },
    handleExport() {
      this.$emit('export');
    }
  }
};
</script>

<style scoped>
.wind-toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  display: flex;
  gap: 10px;
  align-items: center;
}

.upload-btn {
  display: inline-block;
}
</style> 