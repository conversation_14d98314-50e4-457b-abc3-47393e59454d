<!-- eslint-disable -->
<template>
  <div class="wind-station">
    <wind-toolbar
      @add="handleAdd"
      @delete="handleDelete"
      @import="handleImport"
      @export="handleExport"
      :can-delete="!!selectedRows.length"
    ></wind-toolbar>
    
    <wind-data-table
      ref="dataTable"
      :selected-region="selectedRegion"
      @selection-change="handleSelectionChange"
    ></wind-data-table>
    
    <wind-edit-dialog
      :visible.sync="editDialogVisible"
      :edit-data="editData"
      @submit="handleSubmit"
    ></wind-edit-dialog>
  </div>
</template>

<script>
import WindToolbar from './WindToolbar.vue';
import WindDataTable from './WindDataTable.vue';
import WindEditDialog from './WindEditDialog.vue';
import { windStationService } from '@/services/windStationService';

export default {
  name: 'WindStation',
  components: {
    WindToolbar,
    WindDataTable,
    WindEditDialog
  },
  data() {
    return {
      selectedRows: [],
      editDialogVisible: false,
      editData: null,
      selectedRegion: ''
    };
  },
  methods: {
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    handleAdd() {
      this.editData = null;
      this.editDialogVisible = true;
    },
    async handleDelete() {
      try {
        const ids = this.selectedRows.map(row => row.id);
        await windStationService.deleteWindStations(ids);
        this.$message.success('删除成功');
        this.$refs.dataTable.loadData();
        this.selectedRows = [];
      } catch (error) {
        this.$message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    handleImport(file) {
      windStationService.importWindStationData(file)
        .then(() => {
          this.$message.success('导入成功');
          this.$refs.dataTable.loadData();
        })
        .catch(error => {
          this.$message.error(error.message || '导入失败');
          console.error('导入失败:', error);
        });
    },
    handleExport() {
      windStationService.exportWindStationData(this.selectedRegion)
        .then(() => {
          this.$message.success('导出成功');
        })
        .catch(error => {
          this.$message.error('导出失败');
          console.error('导出失败:', error);
        });
    },
    async handleSubmit(data) {
      try {
        if (data.id) {
          await windStationService.updateWindStation(data);
          this.$message.success('更新成功');
        } else {
          await windStationService.addWindStation(data);
          this.$message.success('添加成功');
        }
        this.editDialogVisible = false;
        this.$refs.dataTable.loadData();
      } catch (error) {
        this.$message.error(data.id ? '更新失败' : '添加失败');
        console.error(data.id ? '更新失败:' : '添加失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.wind-station {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}
</style> 