/* eslint-disable */
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

Vue.config.productionTip = false
Vue.use(ElementUI)

// 全局注册Excel导入导出工具
Vue.prototype.$XLSX = XLSX;
Vue.prototype.$saveAs = saveAs;

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')