import Vue from 'vue'
import VueRouter from 'vue-router'

// 使用懒加载方式导入组件
const UnitStatus = () => import('@/components/UnitStatus')
const UnitPrice = () => import('@/components/UnitPrice')
const UnitSpecifiedOutput = () => import('@/components/UnitSpecifiedOutput')
const SolarStation = () => import('@/components/SolarStation')
const SolarArea = () => import('@/components/SolarArea')
const SolarAreaCorrelation = () => import('@/components/SolarAreaCorrelation')
const WindStation = () => import('@/components/WindStation')
const WindArea = () => import('@/components/WindArea')
const WindAreaCorrelation = () => import('@/components/WindAreaCorrelation')
const LineStation = () => import('@/components/LineStation')
const SectionSpecifiedOutput = () => import('@/components/SectionSpecifiedOutput')

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/unit-status'
  },
  {
    path: '/unit-status',
    name: 'UnitStatus',
    component: UnitStatus
  },
  {
    path: '/unit-price',
    name: 'UnitPrice',
    component: UnitPrice
  },
  {
    path: '/unit-specified-output',
    name: 'UnitSpecifiedOutput',
    component: () => import('@/components/UnitSpecifiedOutput'),
  },
  {
    path: '/load-curv',
    name: 'LoadCurv',
    component: () => import('@/components/LoadCurv'),
  },
  {
    path: '/solar-area',
    name: 'SolarArea',
    component: () => import('../components/SolarArea'),
  },
  {
    path: '/solar-area-correlation',
    name: 'SolarAreaCorrelation',
    component: () => import('../components/SolarAreaCorrelation'),
  },
  {
    path: '/solar-station',
    name: 'SolarStation',
    component: SolarStation
  },
  {
    path: '/wind-station',
    name: 'WindStation',
    component: WindStation
  },
  {
    path: '/wind-area',
    name: 'WindArea',
    component: WindArea
  },
  {
    path: '/wind-area-correlation',
    name: 'WindAreaCorrelation',
    component: () => import('../components/WindAreaCorrelation'),
  },
  {
    path: '/line-station',
    name: 'LineStation',
    component: LineStation
  },
  {
    path: '/node-info',
    name: 'NodeInfo',
    component: () => import('../components/NodeInfo'),
  },
  {
    path: '/unit-info',
    name: 'UnitInfo',
    component: () => import('../components/UnitInfo'),
  },
  {
    path: '/hydropower-output',
    name: 'HydropowerOutput',
    component: () => import('../components/HydropowerOutput'),
  },
  {
    path: '/section',
    name: 'Section',
    component: () => import('../components/Section'),
  },
  {
    path: '/section-line-relation',
    name: 'SectionLineRelation',
    component: () => import('../components/SectionLineRelation'),
  },
  {
    path: '/section-specified-output',
    name: 'SectionSpecifiedOutput',
    component: SectionSpecifiedOutput
  }
]

const router = new VueRouter({
  routes
})

export default router 