import * as XLSX from 'xlsx';
import nodeInfoService from './nodeInfoService';
import unitInfoService from './unitInfoService';
import hydropowerOutputService from './hydropowerOutputService';
import unitSpecifiedOutputService from './unitSpecifiedOutputService';
import unitStatusService from './unitStatusService';
import unitPriceService from './unitPriceService';
import windAreaService from './windAreaService';
import windStationService from './windStationService';
import windAreaCorrelationService from './windAreaCorrelationService';
import solarAreaService from './solarAreaService';
import solarStationService from './solarStationService';
import solarAreaCorrelationService from './solarAreaCorrelationService';
import lineStationService from './lineStationService';
import sectionService from './sectionService';
import sectionLineRelationService from './sectionLineRelationService';
import sectionSpecifiedOutputService from './sectionSpecifiedOutputService';
import loadCurvService from './loadCurvService';
import loadModelingDataService from './loadModelingDataService';
import unitModelingDataService from './unitModelingDataService';

const sheetServiceMap = {
  '节点': nodeInfoService,
  '机组': unitInfoService,
  '水电三段式出力': hydropowerOutputService,
  '机组指定出力': unitSpecifiedOutputService,
  '机组指定状态': unitStatusService,
  '机组报价': unitPriceService,
  '风区信息': windAreaService,
  '风电场': windStationService,
  '风区之间相关系数': windAreaCorrelationService,
  '光区信息': solarAreaService,
  '光伏电站': solarStationService,
  '光区之间相关系数': solarAreaCorrelationService,
  '线路': lineStationService,
  '断面': sectionService,
  '断面线路包含关系': sectionLineRelationService,
  '断面指定出力': sectionSpecifiedOutputService,
  '负荷曲线': loadCurvService,
  '负荷曲线-建模': loadModelingDataService,
  '机组指定出力-建模': unitModelingDataService,
};

// 调试日志收集器
const debugLogs = [];
const addDebugLog = (level, message, data = null) => {
  const log = {
    timestamp: new Date().toISOString(),
    level,
    message,
    data: data ? JSON.stringify(data, null, 2) : null
  };
  debugLogs.push(log);
  console.log(`[${level}] ${message}`, data || '');
};

// 数据验证函数
const validateServiceData = (serviceName, data) => {
  if (!Array.isArray(data)) {
    throw new Error(`${serviceName}: 数据必须是数组格式`);
  }

  if (data.length === 0) {
    addDebugLog('WARN', `${serviceName}: 工作表为空`);
    return true;
  }

  // 检查数据结构
  const firstRow = data[0];
  if (typeof firstRow !== 'object' || firstRow === null) {
    throw new Error(`${serviceName}: 数据行格式错误，应为对象格式`);
  }

  addDebugLog('INFO', `${serviceName}: 数据验证通过，共 ${data.length} 行`);
  return true;
};

// 测试单个服务的方法
const testService = async (serviceName, service) => {
  const results = {
    serviceName,
    hasClearAll: false,
    hasBatchImport: false,
    clearAllWorks: false,
    batchImportWorks: false,
    errors: []
  };

  try {
    // 检查方法存在性
    results.hasClearAll = typeof service.clearAll === 'function';
    results.hasBatchImport = typeof service.batchImport === 'function';

    if (!results.hasClearAll) {
      results.errors.push('缺少 clearAll 方法');
    }

    if (!results.hasBatchImport) {
      results.errors.push('缺少 batchImport 方法');
    }

    // 测试 clearAll
    if (results.hasClearAll) {
      try {
        await service.clearAll();
        results.clearAllWorks = true;
        addDebugLog('SUCCESS', `${serviceName}: clearAll 方法测试通过`);
      } catch (error) {
        results.errors.push(`clearAll 方法错误: ${error.message}`);
        addDebugLog('ERROR', `${serviceName}: clearAll 方法测试失败`, error);
      }
    }

    // 测试 batchImport（使用空数组）
    if (results.hasBatchImport) {
      try {
        await service.batchImport([]);
        results.batchImportWorks = true;
        addDebugLog('SUCCESS', `${serviceName}: batchImport 方法测试通过`);
      } catch (error) {
        results.errors.push(`batchImport 方法错误: ${error.message}`);
        addDebugLog('ERROR', `${serviceName}: batchImport 方法测试失败`, error);
      }
    }

  } catch (error) {
    results.errors.push(`服务测试异常: ${error.message}`);
    addDebugLog('ERROR', `${serviceName}: 服务测试异常`, error);
  }

  return results;
};

const importAllDataFromFile = (file, progressCallback) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    // 清空调试日志
    debugLogs.length = 0;
    addDebugLog('INFO', '开始全量导入流程');

    reader.onload = async (e) => {
      const importResults = {
        success: false,
        processedSheets: 0,
        totalSheets: 0,
        missingSheets: [],
        serviceTests: [],
        errors: [],
        debugLogs: debugLogs
      };

      try {
        progressCallback('正在解析Excel文件...');
        addDebugLog('INFO', '开始解析Excel文件');

        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetNames = workbook.SheetNames;

        addDebugLog('INFO', `Excel文件解析成功，发现工作表: ${sheetNames.join(', ')}`);

        importResults.totalSheets = Object.keys(sheetServiceMap).length;
        importResults.missingSheets = Object.keys(sheetServiceMap).filter(name => !sheetNames.includes(name));

        // 第一步：测试所有服务
        progressCallback('正在测试服务方法...');
        addDebugLog('INFO', '开始测试所有服务方法');

        for (const [sheetName, service] of Object.entries(sheetServiceMap)) {
          const testResult = await testService(sheetName, service);
          importResults.serviceTests.push(testResult);

          if (testResult.errors.length > 0) {
            addDebugLog('ERROR', `${sheetName} 服务测试失败`, testResult.errors);
          }
        }

        // 第二步：清空旧数据
        progressCallback('正在清空旧数据...');
        addDebugLog('INFO', '开始清空旧数据');

        for (const [sheetName, service] of Object.entries(sheetServiceMap)) {
          try {
            if (service && typeof service.clearAll === 'function') {
              await service.clearAll();
              addDebugLog('SUCCESS', `${sheetName}: 旧数据清空成功`);
            } else {
              addDebugLog('ERROR', `${sheetName}: 缺少 clearAll 方法`);
              importResults.errors.push(`${sheetName}: 缺少 clearAll 方法`);
            }
          } catch (error) {
            addDebugLog('ERROR', `${sheetName}: 清空数据失败`, error);
            importResults.errors.push(`${sheetName}: 清空数据失败 - ${error.message}`);
          }
        }

        // 第三步：导入新数据
        addDebugLog('INFO', '开始导入新数据');

        for (const sheetName of sheetNames) {
          const service = sheetServiceMap[sheetName];
          if (service) {
            try {
              importResults.processedSheets++;
              progressCallback(`正在导入 '${sheetName}' 数据... (${importResults.processedSheets}/${importResults.totalSheets})`);

              const worksheet = workbook.Sheets[sheetName];
              const jsonData = XLSX.utils.sheet_to_json(worksheet);

              addDebugLog('INFO', `${sheetName}: 解析出 ${jsonData.length} 行数据`);

              // 验证数据
              validateServiceData(sheetName, jsonData);

              if (typeof service.batchImport === 'function') {
                await service.batchImport(jsonData);
                addDebugLog('SUCCESS', `${sheetName}: 数据导入成功，共 ${jsonData.length} 行`);
              } else {
                const error = `${sheetName}: 缺少 batchImport 方法`;
                addDebugLog('ERROR', error);
                importResults.errors.push(error);
              }
            } catch (error) {
              const errorMsg = `${sheetName}: 导入失败 - ${error.message}`;
              addDebugLog('ERROR', errorMsg, error);
              importResults.errors.push(errorMsg);
            }
          } else {
            addDebugLog('WARN', `${sheetName}: 未找到对应的服务`);
          }
        }

        importResults.success = importResults.errors.length === 0;

        if (importResults.success) {
          addDebugLog('SUCCESS', '全量导入完成');
          resolve({
            message: `导入成功！共处理 ${importResults.processedSheets} 个工作表。`,
            details: importResults.missingSheets.length > 0
              ? `以下工作表在文件中缺失，已跳过：${importResults.missingSheets.join(', ')}`
              : '所有工作表都已成功处理。',
            debugInfo: importResults
          });
        } else {
          addDebugLog('ERROR', '全量导入存在错误');
          reject(new Error(`导入过程中发生 ${importResults.errors.length} 个错误：\n${importResults.errors.join('\n')}`));
        }

      } catch (error) {
        addDebugLog('FATAL', '全量导入致命错误', error);
        importResults.errors.push(`致命错误: ${error.message}`);
        reject(error);
      }
    };

    reader.onerror = (error) => {
      addDebugLog('FATAL', '文件读取失败', error);
      reject(new Error('文件读取失败'));
    };

    reader.readAsArrayBuffer(file);
  });
};

// 导出调试工具
const getDebugLogs = () => debugLogs;
const clearDebugLogs = () => debugLogs.length = 0;

// 单独测试服务的工具函数
const testSingleService = async (serviceName) => {
  const service = sheetServiceMap[serviceName];
  if (!service) {
    throw new Error(`未找到服务: ${serviceName}`);
  }
  return await testService(serviceName, service);
};

// 测试所有服务
const testAllServices = async () => {
  const results = [];
  for (const [serviceName, service] of Object.entries(sheetServiceMap)) {
    const result = await testService(serviceName, service);
    results.push(result);
  }
  return results;
};

// 获取服务状态报告
const getServiceStatusReport = async () => {
  const results = await testAllServices();
  const report = {
    totalServices: results.length,
    workingServices: results.filter(r => r.clearAllWorks && r.batchImportWorks).length,
    problemServices: results.filter(r => r.errors.length > 0),
    summary: results.map(r => ({
      name: r.serviceName,
      status: r.errors.length === 0 ? 'OK' : 'ERROR',
      errors: r.errors
    }))
  };
  return report;
};

export default {
  importAllDataFromFile,
  sheetServiceMap,
  // 调试工具
  getDebugLogs,
  clearDebugLogs,
  testSingleService,
  testAllServices,
  getServiceStatusReport
};