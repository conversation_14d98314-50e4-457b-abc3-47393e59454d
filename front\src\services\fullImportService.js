import * as XLSX from 'xlsx';
import nodeInfoService from './nodeInfoService';
import unitInfoService from './unitInfoService';
import hydropowerOutputService from './hydropowerOutputService';
import unitSpecifiedOutputService from './unitSpecifiedOutputService';
import unitStatusService from './unitStatusService';
import unitPriceService from './unitPriceService';
import windAreaService from './windAreaService';
import windStationService from './windStationService';
import windAreaCorrelationService from './windAreaCorrelationService';
import solarAreaService from './solarAreaService';
import solarStationService from './solarStationService';
import solarAreaCorrelationService from './solarAreaCorrelationService';
import lineStationService from './lineStationService';
import sectionService from './sectionService';
import sectionLineRelationService from './sectionLineRelationService';
import sectionSpecifiedOutputService from './sectionSpecifiedOutputService';
import loadCurvService from './loadCurvService';
import loadModelingDataService from './loadModelingDataService';
import unitModelingDataService from './unitModelingDataService';

const sheetServiceMap = {
  '节点': nodeInfoService,
  '机组': unitInfoService,
  '水电三段式出力': hydropowerOutputService,
  '机组指定出力': unitSpecifiedOutputService,
  '机组指定状态': unitStatusService,
  '机组报价': unitPriceService,
  '风区信息': windAreaService,
  '风电场': windStationService,
  '风区之间相关系数': windAreaCorrelationService,
  '光区信息': solarAreaService,
  '光伏电站': solarStationService,
  '光区之间相关系数': solarAreaCorrelationService,
  '线路': lineStationService,
  '断面': sectionService,
  '断面线路包含关系': sectionLineRelationService,
  '断面指定出力': sectionSpecifiedOutputService,
  '负荷曲线': loadCurvService,
  '负荷曲线-建模': loadModelingDataService,
  '机组指定出力-建模': unitModelingDataService,
};

const importAllDataFromFile = (file, progressCallback) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        progressCallback('正在解析Excel文件...');
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        const sheetNames = workbook.SheetNames;
        const totalSheets = Object.keys(sheetServiceMap).length;
        let processedSheets = 0;

        progressCallback('正在清空旧数据...');
        for (const service of Object.values(sheetServiceMap)) {
          if (service && typeof service.clearAll === 'function') {
            await service.clearAll();
          }
        }

        for (const sheetName of sheetNames) {
          const service = sheetServiceMap[sheetName];
          if (service) {
            processedSheets++;
            progressCallback(`正在导入 '${sheetName}' 数据... (${processedSheets}/${totalSheets})`);
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (typeof service.batchImport === 'function') {
              await service.batchImport(jsonData);
            } else {
              console.warn(`Service for sheet '${sheetName}' does not have a batchImport method.`);
            }
          }
        }

        const missingSheets = Object.keys(sheetServiceMap).filter(name => !sheetNames.includes(name));
        if (missingSheets.length > 0) {
          console.warn('以下工作表在文件中缺失，已跳过：', missingSheets.join(', '));
        }
        
        resolve({
          message: `导入成功！共处理 ${processedSheets} 个工作表。`,
          details: `以下工作表在文件中缺失，已跳过：${missingSheets.join(', ')}`
        });

      } catch (error) {
        console.error('全量导入失败:', error);
        reject(error);
      }
    };

    reader.onerror = (error) => {
      console.error('文件读取失败:', error);
      reject(new Error('文件读取失败'));
    };

    reader.readAsArrayBuffer(file);
  });
};

export default {
  importAllDataFromFile,
  sheetServiceMap,
}; 