const STORAGE_KEY = 'hydropower_output_data';

function createEmptyRecord() {
    const record = {
        id: new Date().getTime(), // Temporary unique ID for new records
        powerPlantName: '',
        startTime: '',
        inputTypeA: '平水年',
        inputTypeB: '平均出力',
    };
    for (let i = 1; i <= 12; i++) {
        record['month' + i] = 0;
    }
    return record;
}

function getInitialData() {
  const initialData = [
    { id: 1, powerPlantName: '枫树坝', startTime: '20300101', inputTypeA: '平水年', inputTypeB: '平均出力', month1: 50, month2: 53, month3: 44, month4: 83, month5: 54, month6: 75, month7: 78, month8: 77, month9: 36, month10: 52, month11: 45, month12: 51 },
    { id: 2, powerPlantName: '枫树坝', startTime: '20300101', inputTypeA: '平水年', inputTypeB: '预想出力', month1: 150, month2: 136, month3: 118, month4: 130, month5: 126, month6: 129, month7: 136, month8: 149, month9: 129, month10: 139, month11: 139, month12: 139 },
    { id: 3, powerPlantName: '枫树坝', startTime: '20300101', inputTypeA: '平水年', inputTypeB: '强迫出力', month1: 10, month2: 34, month3: 13, month4: 36, month5: 46, month6: 69, month7: 68, month8: 59, month9: 30, month10: 47, month11: 33, month12: 32 },
    { id: 4, powerPlantName: '枫树坝', startTime: '20300101', inputTypeA: '枯水年', inputTypeB: '强迫出力', month1: 0, month2: 0, month3: 0, month4: 11, month5: 27, month6: 24, month7: 39, month8: 30, month9: 11, month10: 23, month11: 16, month12: 13 },
    { id: 5, powerPlantName: '青溪电站', startTime: '20300101', inputTypeA: '枯水年', inputTypeB: '预想出力', month1: 85, month2: 74, month3: 84, month4: 84, month5: 103, month6: 92, month7: 103, month8: 103, month9: 81, month10: 92, month11: 97, month12: 96 },
  ];
  // Ensure all month properties exist
  return initialData.map(d => ({...createEmptyRecord(), ...d}));
}

function getHydropowerOutputList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function batchSaveHydropowerOutput(updatedData) {
    // In a real app, this would be a single API call.
    // Here, we just replace the entire dataset.
    // A more sophisticated approach would be to find changed/new/deleted items.
    let allData = getHydropowerOutputList();

    updatedData.forEach(item => {
        const index = allData.findIndex(d => d.id === item.id);
        if (index !== -1) {
            allData[index] = item; // Update
        } else {
            // It's a new item, ensure it has a permanent ID if it doesn't
             if (typeof item.id !== 'number' || item.id > new Date().getTime() - 10000) {
                 item.id = allData.length > 0 ? Math.max(...allData.map(d => d.id)) + 1 : 1;
             }
            allData.push(item); // Add
        }
    });

    localStorage.setItem(STORAGE_KEY, JSON.stringify(allData));
}

function deleteHydropowerOutputByIds(ids) {
  let data = getHydropowerOutputList();
  const updatedData = data.filter(d => !ids.includes(d.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedData));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  createEmptyRecord,
  getHydropowerOutputList,
  batchSaveHydropowerOutput,
  deleteHydropowerOutputByIds,
  clearAll,
  batchImport,
}; 