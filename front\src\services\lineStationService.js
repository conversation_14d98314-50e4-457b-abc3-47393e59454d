import axios from 'axios'
import { saveAs } from 'file-saver'

// const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000'
const STORAGE_KEY = 'line_station_data';

const defaultData = [
  { id: 1, lineName: '线路1', fromNode: '节点A', toNode: '节点B', length: 100, type: 'AC' },
  { id: 2, lineName: '线路2', fromNode: '节点B', toNode: '节点C', length: 150, type: 'DC' },
];

function getList() {
    const data = localStorage.getItem(STORAGE_KEY);
    if (!data) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultData));
        return defaultData;
    }
    return JSON.parse(data);
}

function saveData(data) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 