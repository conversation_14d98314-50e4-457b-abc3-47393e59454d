import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

const LOAD_CURV_KEY = 'load_curv_data';

// Mock initial data
const setupInitialData = () => {
  if (!localStorage.getItem(LOAD_CURV_KEY)) {
    const initialLoadCurvs = [
      { id: 'area_1', name: '华中', modelingCount: 365 },
      { id: 'area_2', name: '华北', modelingCount: 0 },
      { id: 'area_3', name: '系统', modelingCount: 120 },
    ];
    localStorage.setItem(LOAD_CURV_KEY, JSON.stringify(initialLoadCurvs));
  }
};

setupInitialData();

function getList(params = {}) {
  let data = JSON.parse(localStorage.getItem(LOAD_CURV_KEY) || '[]');
  if (params.name) {
    data = data.filter(item => item.name.includes(params.name));
  }
  return data;
}

function deleteByIds(ids) {
  let data = getList();
  data = data.filter(item => !ids.includes(item.id));
  localStorage.setItem(LOAD_CURV_KEY, JSON.stringify(data));
  // Note: This service is no longer responsible for deleting associated modeling data.
  // That logic should be handled by the component or a higher-level service if needed.
}

function clearAll() {
  localStorage.removeItem(LOAD_CURV_KEY);
  // This will only clear the main curve list, not the modeling data,
  // which is managed by loadModelingDataService.
  return Promise.resolve();
}

function batchImport(data) {
  localStorage.setItem(LOAD_CURV_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  getList,
  deleteByIds,
  clearAll,
  batchImport,
}; 