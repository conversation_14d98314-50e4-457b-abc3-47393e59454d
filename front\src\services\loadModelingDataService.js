import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

const ALL_MODELING_DATA_KEY = 'load_modeling_data_all';
const OLD_KEY_PREFIX = 'load_modeling_data_';

/**
 * Gets the modeling data. Can be filtered by a parent ID.
 * @param {string} [loadCurvId] - If provided, filters data for that specific curve ID.
 * @returns {Array} - The modeling data.
 */
function getList(loadCurvId) {
  const allData = JSON.parse(localStorage.getItem(ALL_MODELING_DATA_KEY) || '[]');
  if (loadCurvId) {
    // This assumes the data rows have a property like 'parentId' or 'loadCurvId'
    // This needs to be consistent with how the data is shaped in the Excel sheet.
    // We'll assume a property 'areaId' for now.
    return allData.filter(d => d.areaId === loadCurvId);
  }
  return allData;
}

/**
 * Saves modeling data for a *specific* load curve, updating the main array.
 * @param {string} loadCurvId - The ID of the parent load curve.
 * @param {Array} data - The modeling data for that specific curve.
 */
function saveDataForId(loadCurvId, data) {
    const allData = getList();
    // Remove old data for this ID
    const otherData = allData.filter(d => d.areaId !== loadCurvId);
    // Add new data
    const newData = [...otherData, ...data];
    localStorage.setItem(ALL_MODELING_DATA_KEY, JSON.stringify(newData));
}


function clearAll() {
  // Clears all load modeling data, including the new single key and any old per-ID keys.
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith(OLD_KEY_PREFIX) || key === ALL_MODELING_DATA_KEY) {
      localStorage.removeItem(key);
    }
  });
  return Promise.resolve();
}

function batchImport(data) {
  // The data from the '负荷曲线-建模' sheet is a single array containing all modeling points.
  // It completely replaces any existing modeling data.
  localStorage.setItem(ALL_MODELING_DATA_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  getList,
  saveDataForId,
  clearAll,
  batchImport,
}; 