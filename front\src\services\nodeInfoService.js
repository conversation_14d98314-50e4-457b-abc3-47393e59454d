const STORAGE_KEY = 'node_info_data';

function getInitialData() {
  return [
    { id: 1, name: '节点A', code: 'NODE_A', type: '类型1', region: '区域1', voltageLevel: '110kV' },
    { id: 2, name: '节点B', code: 'NODE_B', type: '类型2', region: '区域2', voltageLevel: '220kV' },
    { id: 3, name: '节点C', code: 'NODE_C', type: '类型1', region: '区域1', voltageLevel: '500kV' },
  ];
}

function getNodeInfoList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function saveNodeInfo(node) {
  let nodes = getNodeInfoList();
  if (node.id) {
    // Update existing node
    const index = nodes.findIndex(n => n.id === node.id);
    if (index !== -1) {
      nodes[index] = { ...nodes[index], ...node };
    }
  } else {
    // Add new node
    node.id = nodes.length > 0 ? Math.max(...nodes.map(n => n.id)) + 1 : 1;
    nodes.push(node);
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(nodes));
}

function deleteNodeInfoByIds(ids) {
  let nodes = getNodeInfoList();
  const updatedNodes = nodes.filter(node => !ids.includes(node.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedNodes));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    const converted = {
      id: index + 1,
      name: item['注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称'] || '',
      code: item['注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称'] || '', // 使用节点名称作为代码
      type: '节点', // 默认类型
      region: item['[5]节点所属大区'] || '',
      voltageLevel: '500kV' // 默认电压等级
    };

    return converted;
  });

  localStorage.setItem(STORAGE_KEY, JSON.stringify(convertedData));
  return Promise.resolve();
}

export {
  getNodeInfoList,
  saveNodeInfo,
  deleteNodeInfoByIds,
  clearAll,
  batchImport,
}; 