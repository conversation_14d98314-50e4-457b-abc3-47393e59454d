const STORAGE_KEY = 'node_info_data';

function getInitialData() {
  return [
    { id: 1, name: '节点A', code: 'NODE_A', type: '类型1', region: '区域1', voltageLevel: '110kV' },
    { id: 2, name: '节点B', code: 'NODE_B', type: '类型2', region: '区域2', voltageLevel: '220kV' },
    { id: 3, name: '节点C', code: 'NODE_C', type: '类型1', region: '区域1', voltageLevel: '500kV' },
  ];
}

function getNodeInfoList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function saveNodeInfo(node) {
  let nodes = getNodeInfoList();
  if (node.id) {
    // Update existing node
    const index = nodes.findIndex(n => n.id === node.id);
    if (index !== -1) {
      nodes[index] = { ...nodes[index], ...node };
    }
  } else {
    // Add new node
    node.id = nodes.length > 0 ? Math.max(...nodes.map(n => n.id)) + 1 : 1;
    nodes.push(node);
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(nodes));
}

function deleteNodeInfoByIds(ids) {
  let nodes = getNodeInfoList();
  const updatedNodes = nodes.filter(node => !ids.includes(node.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedNodes));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // The full import will directly overwrite the data, so we need to ensure the format is consistent.
  // For node information, we can add a simple check or transformation if necessary.
  // Here, we trust the data from the Excel file.
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  getNodeInfoList,
  saveNodeInfo,
  deleteNodeInfoByIds,
  clearAll,
  batchImport,
}; 