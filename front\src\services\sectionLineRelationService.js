const STORAGE_KEY = 'section_line_relation_data';

function createEmptyRecord() {
    return {
        id: new Date().getTime(),
        sectionName: '',
        startDate: '',
        endDate: '',
        lineName: '',
        lineDirection: '正向',
    };
}

function getInitialData() {
  return [
    { id: 1, sectionName: '断面A', startDate: '20230101', endDate: '20231231', lineName: '线路1', lineDirection: '正向' },
    { id: 2, sectionName: '断面A', startDate: '20230101', endDate: '20231231', lineName: '线路2', lineDirection: '反向' },
    { id: 3, sectionName: '断面B', startDate: '20230101', endDate: '20231231', lineName: '线路3', lineDirection: '正向' },
  ];
}

function getSectionLineRelationList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data || JSON.parse(data).length === 0) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function batchSaveSectionLineRelation(updatedData) {
    let allData = getSectionLineRelationList();
    updatedData.forEach(item => {
        const index = allData.findIndex(d => d.id === item.id);
        if (index !== -1) {
            allData[index] = item;
        } else {
             if (typeof item.id !== 'number' || item.id > new Date().getTime() - 10000) {
                 item.id = allData.length > 0 ? Math.max(...allData.map(d => d.id)) + 1 : 1;
             }
            allData.push(item);
        }
    });
    localStorage.setItem(STORAGE_KEY, JSON.stringify(allData));
}

function deleteSectionLineRelationByIds(ids) {
  let data = getSectionLineRelationList();
  const updatedData = data.filter(d => !ids.includes(d.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedData));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  createEmptyRecord,
  getSectionLineRelationList,
  batchSaveSectionLineRelation,
  deleteSectionLineRelationByIds,
  clearAll,
  batchImport,
}; 