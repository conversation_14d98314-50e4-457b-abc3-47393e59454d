const STORAGE_KEY = 'section_data';

function createEmptyRecord() {
    return {
        id: new Date().getTime(),
        sectionName: '',
        startDate: '',
        endDate: '',
        maxPositiveCapacity: 0,
        maxNegativeCapacity: 0,
    };
}

function getInitialData() {
  return [
    { id: 1, sectionName: '断面A', startDate: '20230101', endDate: '20231231', maxPositiveCapacity: 1000, maxNegativeCapacity: 800 },
    { id: 2, sectionName: '断面B', startDate: '20230101', endDate: '20231231', maxPositiveCapacity: 1200, maxNegativeCapacity: 950 },
    { id: 3, sectionName: '断面C', startDate: '20240101', endDate: '20241231', maxPositiveCapacity: 1500, maxNegativeCapacity: 1100 },
  ];
}

function getSectionList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data || JSON.parse(data).length === 0) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function batchSaveSection(updatedData) {
    let allData = getSectionList();
    updatedData.forEach(item => {
        const index = allData.findIndex(d => d.id === item.id);
        if (index !== -1) {
            allData[index] = item;
        } else {
             if (typeof item.id !== 'number' || item.id > new Date().getTime() - 10000) {
                 item.id = allData.length > 0 ? Math.max(...allData.map(d => d.id)) + 1 : 1;
             }
            allData.push(item);
        }
    });
    localStorage.setItem(STORAGE_KEY, JSON.stringify(allData));
}

function deleteSectionByIds(ids) {
  let data = getSectionList();
  const updatedData = data.filter(d => !ids.includes(d.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedData));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  createEmptyRecord,
  getSectionList,
  batchSaveSection,
  deleteSectionByIds,
  clearAll,
  batchImport,
}; 