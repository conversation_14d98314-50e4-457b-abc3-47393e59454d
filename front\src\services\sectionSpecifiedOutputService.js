import { v4 as uuidv4 } from 'uuid';

const STORAGE_KEY = 'section_specified_output_data';

const createDefaultData = () => ({
  id: uuidv4(),
  sectionName: '', // 断面名称
  constraintType: '', // 约束类型
  utilizationHours: 0, // 利用小时数
  modelingCount: 0, // 建模数
});

function getList(params) {
  let data = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
  if (data.length === 0) {
    data = [
      { id: uuidv4(), sectionName: '华中-华东', constraintType: '直流', utilizationHours: 4500, modelingCount: 1 },
      { id: uuidv4(), sectionName: '华中-华南', constraintType: '交流', utilizationHours: 5000, modelingCount: 2 },
    ];
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  }
  
  if (params && params.name) {
    return data.filter(item => item.sectionName && item.sectionName.includes(params.name));
  }
  return data;
};

function saveData(dataToSave) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
};

function deleteByIds(ids) {
  let data = getList();
  data = data.filter(item => !ids.includes(item.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
};

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  deleteByIds,
  createDefaultData,
  clearAll,
  batchImport,
}; 