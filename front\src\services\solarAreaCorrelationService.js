// 光区之间相关系数数据服务
const DATA_KEY = 'solarAreaCorrelation_data';

const defaultData = [
  { id: 1, area1Name: '默认光区', area2Name: '默认光区', correlation: 1 },
  { id: 2, area1Name: '默认光区', area2Name: '珠江光区', correlation: 0.8 },
  { id: 3, area1Name: '珠江光区', area2Name: '珠江光区', correlation: 1 }
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

// 为了兼容组件调用，添加 getData 方法
const getData = getList;

export {
  getList,
  getData,  // 添加这个方法
  saveData,
  clearAll,
  batchImport,
};