// 光区数据服务
const DATA_KEY = 'solarArea_data';

const defaultData = [
  { 
    id: 1, 
    areaName: '默认光区', 
    startTime: '20300101',
    longitude: 119.95,
    latitude: 31.79,
    altitude: 5.3,
    endTime: '20301231',
    clearIndexC: 0.1,
    clearIndexLambda: 0.2,
    clearIndexKtu: 0.3,
    groundReflectivity: 0.2,
    scatterP: 0.4,
    scatterQ: 0.5,
    weatherProb1: 0.125,
    weatherProb2: 0.125,
    weatherProb3: 0.125,
    weatherProb4: 0.125,
    weatherProb5: 0.125,
    weatherProb6: 0.125,
    weatherProb7: 0.125,
    weatherProb8: 0.125,
  }
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    const converted = {
      id: index + 1,
      areaName: item['注：该表格中数据仅用于系统运行模拟[0]光区名称'] || '',
      startDate: item['起始时间'] || '',
      solarIrradiance: parseFloat(item['[1]晴空指数概率分布参数C']) || 0,
      longitude: parseFloat(item['[4]地理位置--经度']) || 0,
      latitude: parseFloat(item['[5]地理位置--纬度']) || 0,
      altitude: parseFloat(item['[6]地理位置--海拔高度']) || 0
    };

    return converted;
  });

  saveData(convertedData);
  return Promise.resolve();
}

// 为了兼容组件调用，添加 getData 方法
const getData = getList;

export {
  getList,
  getData,  // 添加这个方法
  saveData,
  clearAll,
  batchImport,
};