// 光区数据服务
const DATA_KEY = 'solarArea_data';

const defaultData = [
  { 
    id: 1, 
    areaName: '默认光区', 
    startTime: '20300101',
    longitude: 119.95,
    latitude: 31.79,
    altitude: 5.3,
    endTime: '20301231',
    clearIndexC: 0.1,
    clearIndexLambda: 0.2,
    clearIndexKtu: 0.3,
    groundReflectivity: 0.2,
    scatterP: 0.4,
    scatterQ: 0.5,
    weatherProb1: 0.125,
    weatherProb2: 0.125,
    weatherProb3: 0.125,
    weatherProb4: 0.125,
    weatherProb5: 0.125,
    weatherProb6: 0.125,
    weatherProb7: 0.125,
    weatherProb8: 0.125,
  }
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 