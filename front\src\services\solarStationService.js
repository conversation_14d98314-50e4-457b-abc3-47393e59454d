// 光伏电站数据服务

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 光伏电站数据的本地存储键
const STATION_DATA_KEY = 'solarStation_data';

// 示例光伏电站数据
const defaultStationData = [
  {
    id: generateId(),
    stationName: 'GF#珠江光伏发电#1',
    region: '珠江区域',
    stationCode: 'GD_ZDN',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤东光伏发电#1',
    region: '粤东区域',
    stationCode: 'GD_YD',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤西光伏发电#1',
    region: '粤西区域',
    stationCode: 'GD_ZXN',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤北光伏发电#1',
    region: '粤北区域',
    stationCode: 'GD_YB',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#特殊光伏发电#1',
    region: '珠江区域',
    stationCode: 'GD_ZXB',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤东光伏发电#2',
    region: '粤东区域',
    stationCode: 'GD_YD',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#东北光伏发电#1',
    region: '默认区域',
    stationCode: 'GD_ZDB',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  }
];

// 初始化本地存储
const initLocalStorage = () => {
  if (!localStorage.getItem(STATION_DATA_KEY)) {
    localStorage.setItem(STATION_DATA_KEY, JSON.stringify(defaultStationData));
  }
};

initLocalStorage();


function getList() {
  const allData = JSON.parse(localStorage.getItem(STATION_DATA_KEY) || '[]');
  return allData;
}

function saveData(data) {
  localStorage.setItem(STATION_DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STATION_DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    const converted = {
      id: index + 1,
      stationName: item['注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）'] || '',
      region: item['[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）'] || '',
      stationCode: '', // Excel中没有这个字段
      stationType: '固定倾角', // 默认类型
      panelTiltAngle: parseFloat(item['[2]光伏板倾斜角度']) || 0,
      panelAzimuthAngle: parseFloat(item['[3]光伏板方位角']) || 0,
      unitCount: parseInt(item['[4]光伏板数量']) || 0,
      availability: parseFloat(item['[5]光伏板可用率']) || 1,
      errorRate: parseFloat(item['[6]光伏板故障率']) || 0
    };

    return converted;
  });

  saveData(convertedData);
  return Promise.resolve();
}

// 为了兼容组件调用，添加缺少的方法
const getData = getList;

// 生成树形菜单数据
function getMenuTree() {
  const data = getList();
  // 按区域分组生成树形结构
  const groupedData = {};
  data.forEach(item => {
    if (!groupedData[item.region]) {
      groupedData[item.region] = {
        id: item.region,
        label: item.region,
        children: []
      };
    }
  });
  return Promise.resolve(Object.values(groupedData));
}

// 获取指定区域的光伏电站数据
function getSolarStationData(region) {
  const data = getList();
  const regionData = data.filter(item => item.region === region);
  return Promise.resolve(regionData);
}

export {
  getList,
  getData,  // 添加这个方法
  getMenuTree,  // 添加这个方法
  getSolarStationData,  // 添加这个方法
  saveData,
  clearAll,
  batchImport,
};