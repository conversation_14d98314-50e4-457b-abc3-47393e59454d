// 光伏电站数据服务

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 光伏电站数据的本地存储键
const STATION_DATA_KEY = 'solarStation_data';

// 示例光伏电站数据
const defaultStationData = [
  {
    id: generateId(),
    stationName: 'GF#珠江光伏发电#1',
    region: '珠江区域',
    stationCode: 'GD_ZDN',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤东光伏发电#1',
    region: '粤东区域',
    stationCode: 'GD_YD',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤西光伏发电#1',
    region: '粤西区域',
    stationCode: 'GD_ZXN',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤北光伏发电#1',
    region: '粤北区域',
    stationCode: 'GD_YB',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#特殊光伏发电#1',
    region: '珠江区域',
    stationCode: 'GD_ZXB',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#粤东光伏发电#2',
    region: '粤东区域',
    stationCode: 'GD_YD',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  },
  {
    id: generateId(),
    stationName: 'GF#东北光伏发电#1',
    region: '默认区域',
    stationCode: 'GD_ZDB',
    stationType: '固定倾角',
    panelTiltAngle: 28,
    panelAzimuthAngle: 0,
    unitCount: 36,
    availability: 1,
    errorRate: 0.15
  }
];

// 初始化本地存储
const initLocalStorage = () => {
  if (!localStorage.getItem(STATION_DATA_KEY)) {
    localStorage.setItem(STATION_DATA_KEY, JSON.stringify(defaultStationData));
  }
};

initLocalStorage();


function getList() {
  const allData = JSON.parse(localStorage.getItem(STATION_DATA_KEY) || '[]');
  return allData;
}

function saveData(data) {
  localStorage.setItem(STATION_DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STATION_DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 