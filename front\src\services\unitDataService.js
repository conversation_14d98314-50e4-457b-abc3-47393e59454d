import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

// 本地存储键名
const STORAGE_KEY = 'unit_status_data';
const TREE_KEY = 'unit_status_tree';

// 默认树形菜单数据
const defaultTreeData = [
  {
    id: 'batch',
    name: '批量处理',
    type: 'category',
    children: [
      { id: 'batch_import', name: '全量导入', type: 'subcategory' },
      { id: 'batch_export', name: '全量导出', type: 'subcategory' }
    ]
  },
  {
    id: 'data',
    name: '数据维护',
    type: 'category',
    children: [
      {
        id: 'node',
        name: '节点信息',
        type: 'subcategory',
        children: [
          { id: 'node_info', name: '节点', type: 'subcategory' }
        ]
      },
      {
        id: 'unit',
        name: '机组信息',
        type: 'subcategory',
        children: [
          { id: 'unit_info', name: '机组', type: 'subcategory' },
          { id: 'unit_status', name: '机组指定状态', type: 'subcategory' },
          { id: 'unit_output', name: '机组固定出力', type: 'subcategory' },
          { id: 'unit_price', name: '机组报价', type: 'subcategory' }
        ]
      },
      {
        id: 'wind',
        name: '风电信息',
        type: 'subcategory',
        children: [
          { id: 'wind_info', name: '风电信息', type: 'subcategory' },
          { id: 'wind_farm', name: '风电场', type: 'subcategory' },
          { id: 'wind_correlation', name: '风区之间相关系数', type: 'subcategory' }
        ]
      },
      {
        id: 'solar',
        name: '光伏信息',
        type: 'subcategory',
        children: [
          { id: 'solar_info', name: '光伏信息', type: 'subcategory' },
          { id: 'solar_station', name: '光伏电站', type: 'subcategory' },
          { id: 'solar_correlation', name: '光区之间相关系数', type: 'subcategory' }
        ]
      },
      {
        id: 'line',
        name: '线路断面信息',
        type: 'subcategory',
        children: [
          { id: 'line_info', name: '线路', type: 'subcategory' },
          { id: 'section_info', name: '断面', type: 'subcategory' }
        ]
      }
    ]
  }
];

// 默认机组状态数据
const defaultUnitData = [
  { id: '1', unitName: 'HD#肥东工业#6', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '2', unitName: 'HD#肥东工业#5', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '3', unitName: 'HD#肥东工业#4', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '4', unitName: 'HD#肥东工业#3', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '5', unitName: 'HD#肥东工业#2', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '6', unitName: 'HD#肥东工业#1', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '7', unitName: 'HD#太平粉碎电二期#2', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '8', unitName: 'HD#太平粉碎电二期#1', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '9', unitName: 'HD#太平粉碎电二期#4', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '10', unitName: 'HD#太平粉碎电二期#3', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '11', unitName: 'HD#陆丰核电5、6号机组#6', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '12', unitName: 'HD#陆丰核电5、6号机组#5', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '13', unitName: 'HD#陆丰核电1、2号机组#2', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '14', unitName: 'HD#陆丰核电1、2号机组#1', startDate: '20300101', endDate: '20300228', status: '指定关机' },
  { id: '15', unitName: 'HD#榕城核电二期B（岭东）#4', startDate: '20300101', endDate: '20300228', status: '指定关机' }
];

// 初始化本地存储
const initStorage = () => {
  // 初始化树形菜单数据
  if (!localStorage.getItem(TREE_KEY)) {
    localStorage.setItem(TREE_KEY, JSON.stringify(defaultTreeData));
  }
  
  // 初始化机组状态数据
  if (!localStorage.getItem(STORAGE_KEY)) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultUnitData));
  }
};

// 获取树形菜单数据
const getMenuTree = () => {
  return new Promise((resolve) => {
    initStorage();
    const treeData = JSON.parse(localStorage.getItem(TREE_KEY));
    resolve(treeData);
  });
};

// 根据节点ID获取机组数据
const getUnitData = (nodeId, pagination) => {
  return new Promise((resolve) => {
    initStorage();
    let data = JSON.parse(localStorage.getItem(STORAGE_KEY));
    
    // 这里可以根据不同的节点ID进行筛选
    // 目前为简化，所有节点都返回全部数据
    
    // 分页处理
    const { page, size } = pagination;
    const total = data.length;
    const start = (page - 1) * size;
    const end = start + size;
    const paginatedData = data.slice(start, end);
    
    resolve({
      data: paginatedData,
      total
    });
  });
};

// 添加机组状态
const addUnit = (unit) => {
  return new Promise((resolve) => {
    initStorage();
    const data = JSON.parse(localStorage.getItem(STORAGE_KEY));
    data.push(unit);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    resolve(unit);
  });
};

// 更新机组状态
const updateUnit = (unit) => {
  return new Promise((resolve) => {
    initStorage();
    let data = JSON.parse(localStorage.getItem(STORAGE_KEY));
    const index = data.findIndex(item => item.id === unit.id);
    
    if (index !== -1) {
      data[index] = unit;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      resolve(unit);
    } else {
      resolve(null);
    }
  });
};

// 删除机组状态
const deleteUnits = (ids) => {
  return new Promise((resolve) => {
    initStorage();
    let data = JSON.parse(localStorage.getItem(STORAGE_KEY));
    data = data.filter(item => !ids.includes(item.id));
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    resolve(true);
  });
};

// 保存全部机组状态数据
const saveUnitData = (data) => {
  return new Promise((resolve) => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    resolve(true);
  });
};

// 导出Excel
const exportUnitData = () => {
  return new Promise((resolve) => {
    initStorage();
    const data = JSON.parse(localStorage.getItem(STORAGE_KEY));
    
    // 转换为Excel格式
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "机组指定状态");
    
    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, `机组指定状态_${new Date().toISOString().split('T')[0]}.xlsx`);
    
    resolve(true);
  });
};

// 导入Excel
const importUnitData = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        // 验证导入数据格式
        const isValid = jsonData.every(item => {
          return item.unitName && item.startDate && item.endDate && item.status;
        });
        
        if (!isValid) {
          reject(new Error('导入数据格式不正确，请检查Excel文件'));
          return;
        }
        
        // 处理导入数据，添加id字段（如果没有）
        const processedData = jsonData.map(item => {
          if (!item.id) {
            item.id = `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          }
          return item;
        });
        
        // 保存到本地存储
        localStorage.setItem(STORAGE_KEY, JSON.stringify(processedData));
        resolve(processedData);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = (error) => {
      reject(error);
    };
    
    reader.readAsArrayBuffer(file);
  });
};

export const unitDataService = {
  getMenuTree,
  getUnitData,
  addUnit,
  updateUnit,
  deleteUnits,
  saveUnitData,
  exportUnitData,
  importUnitData
}; 