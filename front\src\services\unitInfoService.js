const STORAGE_KEY = 'unit_info_data';

function createEmptyUnit() {
    return {
        id: null,
        unitName: '',
        node: '',
        powerPlant: '',
        generationCompany: '',
        startDate: '',
        retirementDate: '',
        type: '',
        capacity: 0,
        minPower: 0,
        hydroStorageEnergy_MWH: 0,
        pumpedStorageEfficiency: 0,
        forcedOutageRate_percent: 0,
        plantPowerConsumptionRate_percent: 0,
        equivalentMaintenancePeriod_days: 0,
        majorOverhaulPeriod_days: 0,
        minorOverhaulPeriod_days: 0,
        majorOverhaulLife_years: 0,
        specifiedMaintenanceStart: '',
        specifiedMaintenanceEnd: '',
        startupCost_wan_yuan: 0,
        fixedOperatingCost_wan_yuan_per_year: 0,
        varOpCostAtMaxPower_yuan_per_kWh: 0,
        varOpCostAtMinPower_yuan_per_kWh: 0,
        investment_wan_yuan: 0,
        paybackPeriod_years: 0,
        planType: '',
        periodsInPlanning: 0,
        earliestCommissionYear: 0,
        samePeriodInterval: '',
        minIntervalWithPreviousPeriod: '',
        planningType: '',
        coalConsumptionRateAtMaxPower_g_per_kWh: 0,
        coalConsumptionRateAtMinPower_g_per_kWh: 0,
        co2EmissionRate_t_per_t: 0,
        hydrocarbonEmissionRate_kg_per_t: 0,
        noxEmissionRate_kg_per_t: 0,
        so2EmissionRate_kg_per_t: 0,
        ashSlagEmissionRate_t_per_t: 0,
        particulateMatterEmissionRate_kg_per_t: 0,
        isAgcUnit: false,
        agcRegulationRate_MW_per_min: 0,
        loadFollowingRate_MW_per_15min: 0,
        rampUpRate_MW_per_h: 0,
        rampDownRate_MW_per_h: 0,
        maxDailyStarts: 0,
        minUpTime_h: 0,
        minDownTime_h: 0,
        mttf_hours: 0,
        mttr_hours: 0,
        unitStatus: '',
        failureRateGain_percent: 0,
        rotationalInertiaCoefficient: 0,
        storageParticipatesInWeeklyOptimization: false,
    };
}


function getInitialData() {
  return [
    {
        ...createEmptyUnit(),
        id: 1, 
        unitName: 'QD_DIS#开平翠山湖热电(鑫灿电厂)#1', 
        node: 'GD_ZXN', 
        powerPlant: '开平翠山湖热电(鑫灿电厂)',
        generationCompany: 'QD_DIS', 
        startDate: '20230930', 
        retirementDate: '21001231', 
        type: '供热燃机',
        capacity: 120,
        minPower: 72,
    },
    {
        ...createEmptyUnit(),
        id: 2, 
        unitName: 'XD#云电#粤西',
        node: 'GD_YX', 
        powerPlant: '云电',
        generationCompany: 'XD',
        startDate: '20200101',
        retirementDate: '21001230',
        type: '区外',
        capacity: 1520,
        minPower: 0,
    },
    {
        ...createEmptyUnit(),
        id: 3, 
        unitName: '珠东南风电近海', 
        node: 'GD_ZDN',
        powerPlant: '珠东南风电近海',
        generationCompany: 'FD_JH',
        startDate: '20271201',
        retirementDate: '21001231',
        type: '风电',
        capacity: 3000,
        minPower: 0,
    },
  ];
}

function getUnitInfoList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function saveUnitInfo(unit) {
  let units = getUnitInfoList();
  if (unit.id) {
    const index = units.findIndex(u => u.id === unit.id);
    if (index !== -1) {
      units[index] = { ...units[index], ...unit };
    }
  } else {
    unit.id = units.length > 0 ? Math.max(...units.map(u => u.id)) + 1 : 1;
    units.push({ ...createEmptyUnit(), ...unit});
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(units));
}

function deleteUnitInfoByIds(ids) {
  let units = getUnitInfoList();
  const updatedUnits = units.filter(unit => !ids.includes(unit.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedUnits));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  createEmptyUnit,
  getUnitInfoList,
  saveUnitInfo,
  deleteUnitInfoByIds,
  clearAll,
  batchImport,
}; 