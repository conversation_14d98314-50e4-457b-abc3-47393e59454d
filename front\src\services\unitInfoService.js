const STORAGE_KEY = 'unit_info_data';

function createEmptyUnit() {
    return {
        id: null,
        unitName: '',
        node: '',
        powerPlant: '',
        generationCompany: '',
        startDate: '',
        retirementDate: '',
        type: '',
        capacity: 0,
        minPower: 0,
        hydroStorageEnergy_MWH: 0,
        pumpedStorageEfficiency: 0,
        forcedOutageRate_percent: 0,
        plantPowerConsumptionRate_percent: 0,
        equivalentMaintenancePeriod_days: 0,
        majorOverhaulPeriod_days: 0,
        minorOverhaulPeriod_days: 0,
        majorOverhaulLife_years: 0,
        specifiedMaintenanceStart: '',
        specifiedMaintenanceEnd: '',
        startupCost_wan_yuan: 0,
        fixedOperatingCost_wan_yuan_per_year: 0,
        varOpCostAtMaxPower_yuan_per_kWh: 0,
        varOpCostAtMinPower_yuan_per_kWh: 0,
        investment_wan_yuan: 0,
        paybackPeriod_years: 0,
        planType: '',
        periodsInPlanning: 0,
        earliestCommissionYear: 0,
        samePeriodInterval: '',
        minIntervalWithPreviousPeriod: '',
        planningType: '',
        coalConsumptionRateAtMaxPower_g_per_kWh: 0,
        coalConsumptionRateAtMinPower_g_per_kWh: 0,
        co2EmissionRate_t_per_t: 0,
        hydrocarbonEmissionRate_kg_per_t: 0,
        noxEmissionRate_kg_per_t: 0,
        so2EmissionRate_kg_per_t: 0,
        ashSlagEmissionRate_t_per_t: 0,
        particulateMatterEmissionRate_kg_per_t: 0,
        isAgcUnit: false,
        agcRegulationRate_MW_per_min: 0,
        loadFollowingRate_MW_per_15min: 0,
        rampUpRate_MW_per_h: 0,
        rampDownRate_MW_per_h: 0,
        maxDailyStarts: 0,
        minUpTime_h: 0,
        minDownTime_h: 0,
        mttf_hours: 0,
        mttr_hours: 0,
        unitStatus: '',
        failureRateGain_percent: 0,
        rotationalInertiaCoefficient: 0,
        storageParticipatesInWeeklyOptimization: false,
    };
}


function getInitialData() {
  return [
    {
        ...createEmptyUnit(),
        id: 1, 
        unitName: 'QD_DIS#开平翠山湖热电(鑫灿电厂)#1', 
        node: 'GD_ZXN', 
        powerPlant: '开平翠山湖热电(鑫灿电厂)',
        generationCompany: 'QD_DIS', 
        startDate: '20230930', 
        retirementDate: '21001231', 
        type: '供热燃机',
        capacity: 120,
        minPower: 72,
    },
    {
        ...createEmptyUnit(),
        id: 2, 
        unitName: 'XD#云电#粤西',
        node: 'GD_YX', 
        powerPlant: '云电',
        generationCompany: 'XD',
        startDate: '20200101',
        retirementDate: '21001230',
        type: '区外',
        capacity: 1520,
        minPower: 0,
    },
    {
        ...createEmptyUnit(),
        id: 3, 
        unitName: '珠东南风电近海', 
        node: 'GD_ZDN',
        powerPlant: '珠东南风电近海',
        generationCompany: 'FD_JH',
        startDate: '20271201',
        retirementDate: '21001231',
        type: '风电',
        capacity: 3000,
        minPower: 0,
    },
  ];
}

function getUnitInfoList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    const initialData = getInitialData();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
    return initialData;
  }
  return JSON.parse(data);
}

function saveUnitInfo(unit) {
  let units = getUnitInfoList();
  if (unit.id) {
    const index = units.findIndex(u => u.id === unit.id);
    if (index !== -1) {
      units[index] = { ...units[index], ...unit };
    }
  } else {
    unit.id = units.length > 0 ? Math.max(...units.map(u => u.id)) + 1 : 1;
    units.push({ ...createEmptyUnit(), ...unit});
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(units));
}

function deleteUnitInfoByIds(ids) {
  let units = getUnitInfoList();
  const updatedUnits = units.filter(unit => !ids.includes(unit.id));
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedUnits));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    // 机组类型映射
    const typeMap = {
      '1': '煤电',
      '2': '可启停火电',
      '3': '供热燃机',
      '4': '核电',
      '5': '水电',
      '6': '抽蓄',
      '7': '区外',
      '8': '热电',
      '9': '调峰燃机',
      '10': '燃油机组',
      '11': '风电',
      '12': '光伏',
      '13': '光热',
      '14': '储能',
      '15': '分布式光伏',
      '20': '生物质',
      '21': '110kV统调'
    };

    const converted = {
      ...createEmptyUnit(),
      id: index + 1,
      unitName: item['注：该表格中数据用于系统运行模拟与电源规划优化计算[0]机组名称'] || '',
      node: item['[1]所属节点'] || '',
      powerPlant: item['[2]电厂'] || '',
      generationCompany: item['[3]所属发电公司'] || '',
      startDate: item['[4]实际投产日（按"年月"形式输入）'] || '',
      retirementDate: item['[5]退役时间（按"年月"形式输入）'] || '',
      type: typeMap[item['[6]机组类型(1:煤电;2:可启停火电;3:供热燃机;4:核电;5:水电;6:抽蓄;7-区外;8:热电;9:调峰燃机;10:燃油机组;11:风电;12:光伏;13:光热;14:储能;15:分布式光伏;20:生物质;21:110kV统调)']] || '其他',
      capacity: parseFloat(item['[7]机组容量(MW)']) || 0,
      minPower: parseFloat(item['[8]最低出力(MW)']) || 0,
      hydroStorageEnergy_MWH: parseFloat(item['[9]最大抽水量/发电量(MWh)（只对水电以及抽水蓄能及储能有效）']) || 0,
      pumpedStorageEfficiency: parseFloat(item['[10]抽蓄转换效率（只对抽蓄和储能有效）(％)']) || 0,
      forcedOutageRate_percent: parseFloat(item['[11]强迫停运率(％)']) || 0,
      plantPowerConsumptionRate_percent: parseFloat(item['[12]厂用电率(％)']) || 0
    };

    return converted;
  });

  localStorage.setItem(STORAGE_KEY, JSON.stringify(convertedData));
  return Promise.resolve();
}

export {
  createEmptyUnit,
  getUnitInfoList,
  saveUnitInfo,
  deleteUnitInfoByIds,
  clearAll,
  batchImport,
}; 