// 机组建模数据服务
import * as XLSX from 'xlsx';

const ALL_MODELING_DATA_KEY = 'unit_modeling_data_all';
const OLD_KEY_PREFIX = 'unitModelingData_';

const generateDefaultModelingData = (unitId, unitName) => {
    const data = [];
    const count = unitId === 2 ? 365 : 1;
    for(let i=1; i<=count; i++) {
        const row = {
            id: `${unitId}_${i}`,
            unitName: unitName,
            startDate: `2024-01-${i < 10 ? '0'+i : i}`,
            endDate: `2024-01-${i < 10 ? '0'+i : i}`,
        };
        for(let j=1; j<=24; j++) {
            row[`p${j}`] = (Math.random() * 100).toFixed(2);
        }
        data.push(row);
    }
    return data;
}

/**
 * Gets the modeling data. Can be filtered by a parent unit ID.
 * @param {string} [unitId] - If provided, filters data for that specific unit.
 * @returns {Array} - The modeling data.
 */
function getList(unitId) {
  const allData = JSON.parse(localStorage.getItem(ALL_MODELING_DATA_KEY) || '[]');
  if (unitId) {
    // This assumes the data rows have a property like 'parentId' or 'unitId'
    // to associate them with the main unit entry.
    return allData.filter(d => d.unitId === unitId);
  }
  return allData;
}

/**
 * Saves modeling data for a *specific* unit, updating the main array.
 * @param {string} unitId - The ID of the parent unit.
 * @param {Array} data - The modeling data for that specific unit.
 */
function saveDataForId(unitId, data) {
    const allData = getList();
    const otherData = allData.filter(d => d.unitId !== unitId);
    const newData = [...otherData, ...data];
    localStorage.setItem(ALL_MODELING_DATA_KEY, JSON.stringify(newData));
}


function clearAll() {
  // Clears all unit modeling data, including the new single key and any old per-ID keys.
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith(OLD_KEY_PREFIX) || key === ALL_MODELING_DATA_KEY) {
      localStorage.removeItem(key);
    }
  });
  return Promise.resolve();
}

function batchImport(data) {
  // The data from the '机组指定出力-建模' sheet is a single array containing all modeling points.
  // It completely replaces any existing modeling data.
  localStorage.setItem(ALL_MODELING_DATA_KEY, JSON.stringify(data));
  return Promise.resolve();
}

export {
  getList,
  saveDataForId,
  clearAll,
  batchImport,
}; 