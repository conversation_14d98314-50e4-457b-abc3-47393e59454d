// 本地存储键名
const STORAGE_KEY = 'unit_price_data';

// 默认机组报价数据
const defaultUnitPriceData = [
  {
    id: '1',
    unitName: '珠西南风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '2',
    unitName: '珠西南风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '3',
    unitName: '珠西风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '4',
    unitName: '珠西北风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '5',
    unitName: '珠西北风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '6',
    unitName: '珠东南风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '7',
    unitName: '珠东南风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '8',
    unitName: '珠东风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '9',
    unitName: '粤西风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '10',
    unitName: '粤西风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '11',
    unitName: '粤西风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '12',
    unitName: '粤东风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '13',
    unitName: '粤东风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '14',
    unitName: '粤东风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '15',
    unitName: '粤乐风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  }
];

function getList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultUnitPriceData));
    return defaultUnitPriceData;
  }
  return JSON.parse(data);
}

function saveData(data) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    const converted = {
      id: index + 1,
      unitName: item['注：该表格中数据仅用于系统运行模拟[0]机组名称'] || '',
      startDate: item['[1]起始日期'] || '',
      prices: []
    };

    // 转换24个时段的价格数据
    for (let i = 1; i <= 24; i++) {
      const priceKey = `[${i + 1}]时段${i}`;
      converted.prices.push(parseFloat(item[priceKey]) || 0);
    }

    return converted;
  });

  saveData(convertedData);
  return Promise.resolve();
}

// 为了兼容组件调用，添加缺少的方法
const getData = getList;

// 生成树形菜单数据
function getMenuTree() {
  const data = getList();
  // 按机组名称分组生成树形结构
  const groupedData = {};
  data.forEach(item => {
    if (!groupedData[item.unitName]) {
      groupedData[item.unitName] = {
        id: item.unitName,
        label: item.unitName,
        children: []
      };
    }
  });
  return Promise.resolve(Object.values(groupedData));
}

// 获取指定机组的价格数据
function getUnitPriceData(unitName) {
  const data = getList();
  const unitData = data.filter(item => item.unitName === unitName);
  return Promise.resolve(unitData);
}

export {
  getList,
  getData,  // 添加这个方法
  getMenuTree,  // 添加这个方法
  getUnitPriceData,  // 添加这个方法
  saveData,
  clearAll,
  batchImport,
};