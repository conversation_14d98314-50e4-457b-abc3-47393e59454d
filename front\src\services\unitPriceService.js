// 本地存储键名
const STORAGE_KEY = 'unit_price_data';

// 默认机组报价数据
const defaultUnitPriceData = [
  {
    id: '1',
    unitName: '珠西南风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '2',
    unitName: '珠西南风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '3',
    unitName: '珠西风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '4',
    unitName: '珠西北风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '5',
    unitName: '珠西北风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '6',
    unitName: '珠东南风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '7',
    unitName: '珠东南风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '8',
    unitName: '珠东风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '9',
    unitName: '粤西风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '10',
    unitName: '粤西风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '11',
    unitName: '粤西风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '12',
    unitName: '粤东风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '13',
    unitName: '粤东风电场',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '14',
    unitName: '粤东风电',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  },
  {
    id: '15',
    unitName: '粤乐风电场海',
    priceDate: '20300101',
    prices: Array(24).fill(0.5)
  }
];

function getList() {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultUnitPriceData));
    return defaultUnitPriceData;
  }
  return JSON.parse(data);
}

function saveData(data) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 