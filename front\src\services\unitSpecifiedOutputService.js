// 机组指定出力数据服务
const DATA_KEY = 'unitSpecifiedOutput_data';

const defaultData = [
  { id: 1, unitName: 'QD_RD#桔乡电厂(四会热电)#4', constraintType: '出力下限', hours: 4800, energy: 13.1, modelingCount: 1 },
  { id: 2, unitName: 'XD#岭东#1', constraintType: '指定出力', hours: 6243.81, energy: 187.31, modelingCount: 365 },
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function deleteByIds(ids) {
  const data = getList();
  const filteredData = data.filter(item => !ids.includes(item.id));
  saveData(filteredData);
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // This service is only responsible for the '机组指定出力' sheet.
  // The modeling data will be handled by 'unitModelingDataService'.
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  deleteByIds,
  clearAll,
  batchImport,
}; 