// 机组指定出力数据服务
const DATA_KEY = 'unitSpecifiedOutput_data';

const defaultData = [
  { id: 1, unitName: 'QD_RD#桔乡电厂(四会热电)#4', constraintType: '出力下限', hours: 4800, energy: 13.1, modelingCount: 1 },
  { id: 2, unitName: 'XD#岭东#1', constraintType: '指定出力', hours: 6243.81, energy: 187.31, modelingCount: 365 },
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function deleteByIds(ids) {
  const data = getList();
  const filteredData = data.filter(item => !ids.includes(item.id));
  saveData(filteredData);
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    // 约束类型映射
    const constraintTypeMap = {
      '-1': '出力下限',
      '0': '指定出力',
      '1': '出力上限'
    };

    const converted = {
      id: index + 1,
      unitName: item['注：该表格中数据仅用于系统运行模拟[0]机组名称'] || '',
      constraintType: constraintTypeMap[item['[27约束类型（-1，出力下限，0，指定出力，1出力上限）']] || '指定出力',
      hours: 8760, // 默认年小时数
      energy: 0, // 需要计算累计电量
      modelingCount: 365 // 默认建模天数
    };

    return converted;
  });

  saveData(convertedData);
  return Promise.resolve();
}

// 为了兼容组件调用，添加 getData 方法
const getData = getList;

export {
  getList,
  getData,  // 添加这个方法
  saveData,
  deleteByIds,
  clearAll,
  batchImport,
};