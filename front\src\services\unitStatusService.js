const STORAGE_KEY = 'unit_status_data';

function getList() {
  const data = localStorage.getItem(STORAGE_KEY);
  return data ? JSON.parse(data) : [];
}

function saveData(data) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export default {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 