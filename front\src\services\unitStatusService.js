const STORAGE_KEY = 'unit_status_data';

function getList() {
  const data = localStorage.getItem(STORAGE_KEY);
  return data ? JSON.parse(data) : [];
}

function saveData(data) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STORAGE_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    const converted = {
      id: index + 1,
      unitName: item['注：该表格中数据仅用于系统运行模拟[0]机组名称'] || '',
      startDate: item['[1]起始日期'] || '',
      endDate: item['[2]结束日期'] || '',
      status: parseInt(item['[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））']) || 0
    };

    return converted;
  });

  saveData(convertedData);
  return Promise.resolve();
}

export default {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 