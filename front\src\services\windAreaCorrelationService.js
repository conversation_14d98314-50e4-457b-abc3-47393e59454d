// 风区之间相关系数数据服务
const DATA_KEY = 'windAreaCorrelation_data';

const defaultData = [
  { id: 1, area1Name: '默认风区', area2Name: '默认风区', correlation: 1 },
  { id: 2, area1Name: '默认风区', area2Name: '珠江风区', correlation: 0.8 },
  { id: 3, area1Name: '珠江风区', area2Name: '珠江风区', correlation: 1 }
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport,
}; 