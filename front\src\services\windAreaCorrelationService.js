// 风区之间相关系数数据服务
const DATA_KEY = 'windAreaCorrelation_data';

const defaultData = [
  { id: 1, area1Name: '默认风区', area2Name: '默认风区', correlation: 1 },
  { id: 2, area1Name: '默认风区', area2Name: '珠江风区', correlation: 0.8 },
  { id: 3, area1Name: '珠江风区', area2Name: '珠江风区', correlation: 1 }
];

function getList() {
  let data = localStorage.getItem(DATA_KEY);
  return data ? JSON.parse(data) : defaultData;
}

function saveData(data) {
  localStorage.setItem(DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel相关系数矩阵格式到服务期望的格式
  const convertedData = [];
  let idCounter = 1;

  data.forEach(row => {
    const area1Name = row['注：该表格中数据仅用于系统运行模拟[0]风区名称'] || '';

    // 遍历除第一列外的所有列，这些列代表其他风区
    Object.keys(row).forEach(key => {
      if (key !== '注：该表格中数据仅用于系统运行模拟[0]风区名称') {
        const area2Name = key;
        const correlation = parseFloat(row[key]) || 0;

        convertedData.push({
          id: idCounter++,
          area1Name: area1Name,
          area2Name: area2Name,
          correlation: correlation
        });
      }
    });
  });

  saveData(convertedData);
  return Promise.resolve();
}

// 为了兼容组件调用，添加 getData 方法
const getData = getList;

export {
  getList,
  getData,  // 添加这个方法
  saveData,
  clearAll,
  batchImport,
};