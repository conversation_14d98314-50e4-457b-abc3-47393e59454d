// 风区数据服务

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 风区数据的本地存储键
const AREA_DATA_KEY = 'windArea_data';

const getDefaultWindAreaData = () => {
    const data = {
        id: generateId(),
        areaName: '默认风区',
        startDate: '2030-01-01',
        weibullC: 5.805480868,
        weibullK: 2.374547193,
        autoCorrelationDecay: 0.05,
    };
    const monthValues = [0.897874464, 1.020311891, 1.123314805, 1.173844537, 1.129145159, 0.98727322, 0.950347647, 0.975612512, 0.899817915, 1.065094971, 1.05603126, 0.941331615];
    for (let i = 1; i <= 12; i++) {
        data[`avgWindSpeedM${i}`] = monthValues[i-1];
    }
    const periodValues = [0.948, 0.931, 0.91, 0.89, 0.871, 0.87, 0.892, 0.93, 0.975, 1.026, 1.071, 1.123, 1.151, 1.14, 1.11, 1.079, 1.055, 1.041, 1.03, 1.02, 1.009, 0.992, 0.973, 0.959];
    for (let i = 1; i <= 24; i++) {
        data[`avgWindSpeedP${i}`] = periodValues[i-1];
    }
    return [data];
}

function getList() {
    let data = localStorage.getItem(AREA_DATA_KEY);
    return data ? JSON.parse(data) : getDefaultWindAreaData();
}

function saveData(data) {
    localStorage.setItem(AREA_DATA_KEY, JSON.stringify(data));
}

function clearAll() {
    localStorage.removeItem(AREA_DATA_KEY);
    return Promise.resolve();
}

function batchImport(data) {
    // 转换Excel数据格式到服务期望的格式
    const convertedData = data.map((item, index) => {
        const converted = {
            id: index + 1,
            areaName: item['注：该表格中数据仅用于系统运行模拟[0]风区名称'] || '',
            startDate: item['起始时间'] || '',
            weibullC: parseFloat(item['[1]风速Weibull分布参数c']) || 0,
            weibullK: parseFloat(item['[2]风速Weibull分布参数k']) || 0,
            autoCorrelationDecay: parseFloat(item['[3]风速自相关函数衰减系数']) || 0
        };

        return converted;
    });

    saveData(convertedData);
    return Promise.resolve();
}

export {
    getList,
    saveData,
    clearAll,
    batchImport,
}; 