// 风电场数据服务

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 风电场数据的本地存储键
const STATION_DATA_KEY = 'windStation_data';

// 示例风电场数据
const defaultStationData = [
  {
    id: generateId(),
    stationName: 'FD#珠江风电#1',
    region: '珠江区域',
    stationCode: 'GD_ZDN',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  },
  {
    id: generateId(),
    stationName: 'FD#粤东风电#1',
    region: '粤东区域',
    stationCode: 'GD_YD',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  },
  {
    id: generateId(),
    stationName: 'FD#粤西风电#1',
    region: '粤西区域',
    stationCode: 'GD_ZXN',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  },
  {
    id: generateId(),
    stationName: 'FD#粤北风电#1',
    region: '粤北区域',
    stationCode: 'GD_YB',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  }
];

function getList() {
  let data = localStorage.getItem(STATION_DATA_KEY);
  return data ? JSON.parse(data) : defaultStationData;
}

function saveData(data) {
  localStorage.setItem(STATION_DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STATION_DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  // 转换Excel数据格式到服务期望的格式
  const convertedData = data.map((item, index) => {
    const converted = {
      id: index + 1,
      stationName: item['注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）'] || '',
      region: item['[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）'] || '',
      stationCode: '', // Excel中没有这个字段，使用默认值
      cutInSpeed: parseFloat(item['[2]风机切入风速']) || 0,
      ratedSpeed: parseFloat(item['[3]风机额定风速']) || 0,
      cutOutSpeed: parseFloat(item['[4]风机切出风速']) || 0,
      randomFactor: parseFloat(item['[5]风电场尾流系数']) || 0,
      availability: parseFloat(item['[6]风机可用率']) || 0
    };

    return converted;
  });

  saveData(convertedData);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport
}; 