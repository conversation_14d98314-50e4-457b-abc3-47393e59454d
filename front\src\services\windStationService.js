// 风电场数据服务

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 风电场数据的本地存储键
const STATION_DATA_KEY = 'windStation_data';

// 示例风电场数据
const defaultStationData = [
  {
    id: generateId(),
    stationName: 'FD#珠江风电#1',
    region: '珠江区域',
    stationCode: 'GD_ZDN',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  },
  {
    id: generateId(),
    stationName: 'FD#粤东风电#1',
    region: '粤东区域',
    stationCode: 'GD_YD',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  },
  {
    id: generateId(),
    stationName: 'FD#粤西风电#1',
    region: '粤西区域',
    stationCode: 'GD_ZXN',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  },
  {
    id: generateId(),
    stationName: 'FD#粤北风电#1',
    region: '粤北区域',
    stationCode: 'GD_YB',
    cutInSpeed: 3.5,
    ratedSpeed: 12,
    cutOutSpeed: 25,
    randomFactor: 0.98,
    availability: 0.95
  }
];

function getList() {
  let data = localStorage.getItem(STATION_DATA_KEY);
  return data ? JSON.parse(data) : defaultStationData;
}

function saveData(data) {
  localStorage.setItem(STATION_DATA_KEY, JSON.stringify(data));
}

function clearAll() {
  localStorage.removeItem(STATION_DATA_KEY);
  return Promise.resolve();
}

function batchImport(data) {
  saveData(data);
  return Promise.resolve();
}

export {
  getList,
  saveData,
  clearAll,
  batchImport
}; 