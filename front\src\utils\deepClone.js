export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    // Handle Date
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }

    // Handle Array
    if (Array.isArray(obj)) {
        let newArr = [];
        for (let i = 0; i < obj.length; i++) {
            newArr[i] = deepClone(obj[i]);
        }
        return newArr;
    }

    // Handle Object
    let newObj = {};
    for (let key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            newObj[key] = deepClone(obj[key]);
        }
    }
    return newObj;
} 