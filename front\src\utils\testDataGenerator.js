import * as XLSX from 'xlsx';

// 测试数据模板
const testDataTemplates = {
  '节点': [
    { id: 1, name: '节点1', voltage: 220, type: '变电站' },
    { id: 2, name: '节点2', voltage: 110, type: '变电站' }
  ],
  
  '机组': [
    { id: 1, name: '机组1', capacity: 300, type: '火电', status: '运行' },
    { id: 2, name: '机组2', capacity: 600, type: '水电', status: '运行' }
  ],
  
  '水电三段式出力': [
    { unitId: 1, segment1: 100, segment2: 200, segment3: 300 },
    { unitId: 2, segment1: 150, segment2: 300, segment3: 450 }
  ],
  
  '机组指定出力': [
    { unitId: 1, hour: 1, output: 250 },
    { unitId: 1, hour: 2, output: 280 },
    { unitId: 2, hour: 1, output: 400 }
  ],
  
  '机组指定状态': [
    { unitId: 1, hour: 1, status: 1 },
    { unitId: 1, hour: 2, status: 1 },
    { unitId: 2, hour: 1, status: 0 }
  ],
  
  '机组报价': [
    { unitId: 1, segment: 1, price: 0.35 },
    { unitId: 1, segment: 2, price: 0.40 },
    { unitId: 2, segment: 1, price: 0.30 }
  ],
  
  '风区信息': [
    { id: 1, name: '风区1', capacity: 500, location: '内蒙古' },
    { id: 2, name: '风区2', capacity: 800, location: '新疆' }
  ],
  
  '风电场': [
    { id: 1, name: '风电场1', windAreaId: 1, capacity: 200 },
    { id: 2, name: '风电场2', windAreaId: 1, capacity: 300 }
  ],
  
  '风区之间相关系数': [
    { windArea1: 1, windArea2: 2, correlation: 0.75 }
  ],
  
  '光区信息': [
    { id: 1, name: '光区1', capacity: 400, location: '青海' },
    { id: 2, name: '光区2', capacity: 600, location: '甘肃' }
  ],
  
  '光伏电站': [
    { id: 1, name: '光伏电站1', solarAreaId: 1, capacity: 150 },
    { id: 2, name: '光伏电站2', solarAreaId: 2, capacity: 200 }
  ],
  
  '光区之间相关系数': [
    { solarArea1: 1, solarArea2: 2, correlation: 0.80 }
  ],
  
  '线路': [
    { id: 1, name: '线路1', fromNode: 1, toNode: 2, capacity: 1000 },
    { id: 2, name: '线路2', fromNode: 2, toNode: 3, capacity: 800 }
  ],
  
  '断面': [
    { id: 1, name: '断面1', limit: 2000 },
    { id: 2, name: '断面2', limit: 1500 }
  ],
  
  '断面线路包含关系': [
    { sectionId: 1, lineId: 1, direction: 1 },
    { sectionId: 1, lineId: 2, direction: -1 }
  ],
  
  '断面指定出力': [
    { sectionId: 1, hour: 1, output: 800 },
    { sectionId: 1, hour: 2, output: 900 }
  ],
  
  '负荷曲线': [
    { id: 1, name: '负荷曲线1', type: '日负荷' },
    { id: 2, name: '负荷曲线2', type: '周负荷' }
  ],
  
  '负荷曲线-建模': [
    { loadCurvId: 1, hour: 1, load: 5000 },
    { loadCurvId: 1, hour: 2, load: 5200 },
    { loadCurvId: 2, hour: 1, load: 4800 }
  ],
  
  '机组指定出力-建模': [
    { unitId: 1, hour: 1, modelOutput: 245 },
    { unitId: 1, hour: 2, modelOutput: 275 },
    { unitId: 2, hour: 1, modelOutput: 395 }
  ]
};

/**
 * 生成测试Excel文件
 * @param {Array} sheetNames - 要包含的工作表名称数组，如果为空则包含所有
 * @returns {Blob} Excel文件的Blob对象
 */
export function generateTestExcel(sheetNames = null) {
  const workbook = XLSX.utils.book_new();
  
  const sheetsToInclude = sheetNames || Object.keys(testDataTemplates);
  
  sheetsToInclude.forEach(sheetName => {
    if (testDataTemplates[sheetName]) {
      const worksheet = XLSX.utils.json_to_sheet(testDataTemplates[sheetName]);
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }
  });
  
  // 生成Excel文件
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
}

/**
 * 下载测试Excel文件
 * @param {Array} sheetNames - 要包含的工作表名称数组
 * @param {string} filename - 文件名
 */
export function downloadTestExcel(sheetNames = null, filename = 'test_data.xlsx') {
  const blob = generateTestExcel(sheetNames);
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * 获取所有可用的工作表名称
 * @returns {Array} 工作表名称数组
 */
export function getAvailableSheets() {
  return Object.keys(testDataTemplates);
}

/**
 * 获取指定工作表的测试数据
 * @param {string} sheetName - 工作表名称
 * @returns {Array} 测试数据数组
 */
export function getTestData(sheetName) {
  return testDataTemplates[sheetName] || [];
}

/**
 * 生成空的Excel模板（只有表头）
 * @param {Array} sheetNames - 要包含的工作表名称数组
 * @returns {Blob} Excel文件的Blob对象
 */
export function generateEmptyTemplate(sheetNames = null) {
  const workbook = XLSX.utils.book_new();
  
  const sheetsToInclude = sheetNames || Object.keys(testDataTemplates);
  
  sheetsToInclude.forEach(sheetName => {
    if (testDataTemplates[sheetName] && testDataTemplates[sheetName].length > 0) {
      // 只取第一行作为表头
      const headers = Object.keys(testDataTemplates[sheetName][0]);
      const headerRow = {};
      headers.forEach(header => {
        headerRow[header] = header; // 表头行
      });
      
      const worksheet = XLSX.utils.json_to_sheet([headerRow]);
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }
  });
  
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
}

/**
 * 下载空的Excel模板
 * @param {Array} sheetNames - 要包含的工作表名称数组
 * @param {string} filename - 文件名
 */
export function downloadEmptyTemplate(sheetNames = null, filename = 'template.xlsx') {
  const blob = generateEmptyTemplate(sheetNames);
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

export default {
  generateTestExcel,
  downloadTestExcel,
  getAvailableSheets,
  getTestData,
  generateEmptyTemplate,
  downloadEmptyTemplate
};
