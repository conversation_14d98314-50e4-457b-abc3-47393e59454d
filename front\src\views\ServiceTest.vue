<template>
  <div class="service-test">
    <el-card>
      <div slot="header">
        <span>服务测试工具</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card>
            <div slot="header">快速测试</div>
            <el-button type="primary" @click="testAllServices" :loading="testing">
              测试所有服务
            </el-button>
            <el-button @click="clearAllData" :loading="clearing">
              清空所有数据
            </el-button>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card>
            <div slot="header">测试数据生成</div>
            <el-button type="success" @click="downloadTestData">
              下载测试数据
            </el-button>
            <el-button @click="downloadTemplate">
              下载空模板
            </el-button>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card>
            <div slot="header">单个服务测试</div>
            <el-select v-model="selectedService" placeholder="选择服务" style="width: 100%; margin-bottom: 10px;">
              <el-option
                v-for="serviceName in Object.keys(sheetServiceMap)"
                :key="serviceName"
                :label="serviceName"
                :value="serviceName">
              </el-option>
            </el-select>
            <div>
              <el-button @click="testSingleService" :disabled="!selectedService">
                测试服务
              </el-button>
              <el-button @click="clearSingleService" :disabled="!selectedService">
                清空数据
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 测试结果 -->
      <el-card style="margin-top: 20px;" v-if="testResults.length > 0">
        <div slot="header">测试结果</div>
        <el-table :data="testResults" style="width: 100%">
          <el-table-column prop="serviceName" label="服务名称" width="200"></el-table-column>
          <el-table-column label="clearAll" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.clearAllWorks ? 'success' : 'danger'" size="mini">
                {{ scope.row.clearAllWorks ? '正常' : '异常' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="batchImport" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.batchImportWorks ? 'success' : 'danger'" size="mini">
                {{ scope.row.batchImportWorks ? '正常' : '异常' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="errors" label="错误信息">
            <template slot-scope="scope">
              <div v-if="scope.row.errors.length > 0">
                <div v-for="error in scope.row.errors" :key="error" class="error-text">
                  {{ error }}
                </div>
              </div>
              <span v-else class="success-text">正常</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 数据状态检查 -->
      <el-card style="margin-top: 20px;">
        <div slot="header">
          <span>数据状态检查</span>
          <el-button style="float: right;" @click="checkDataStatus">刷新状态</el-button>
        </div>
        <el-table :data="dataStatus" style="width: 100%">
          <el-table-column prop="serviceName" label="服务名称" width="200"></el-table-column>
          <el-table-column prop="dataCount" label="数据条数" width="100"></el-table-column>
          <el-table-column prop="storageKey" label="存储键" width="200"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button size="mini" @click="viewData(scope.row)">查看数据</el-button>
              <el-button size="mini" type="danger" @click="clearServiceData(scope.row)">清空</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 数据查看对话框 -->
      <el-dialog title="数据详情" :visible.sync="dataDialogVisible" width="80%">
        <div v-if="currentData">
          <p><strong>服务:</strong> {{ currentData.serviceName }}</p>
          <p><strong>数据条数:</strong> {{ currentData.dataCount }}</p>
          <p><strong>存储键:</strong> {{ currentData.storageKey }}</p>
          <el-divider></el-divider>
          <pre class="data-preview">{{ JSON.stringify(currentData.data, null, 2) }}</pre>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import fullImportService from '@/services/fullImportService';
import testDataGenerator from '@/utils/testDataGenerator';

export default {
  name: 'ServiceTest',
  data() {
    return {
      testing: false,
      clearing: false,
      selectedService: '',
      testResults: [],
      dataStatus: [],
      dataDialogVisible: false,
      currentData: null,
      sheetServiceMap: fullImportService.sheetServiceMap
    };
  },
  mounted() {
    this.checkDataStatus();
  },
  methods: {
    async testAllServices() {
      this.testing = true;
      try {
        this.testResults = await fullImportService.testAllServices();
        this.$message.success('所有服务测试完成');
      } catch (error) {
        this.$message.error(`测试失败: ${error.message}`);
      } finally {
        this.testing = false;
      }
    },
    
    async testSingleService() {
      if (!this.selectedService) return;
      
      try {
        const result = await fullImportService.testSingleService(this.selectedService);
        // 更新或添加到测试结果中
        const index = this.testResults.findIndex(r => r.serviceName === this.selectedService);
        if (index >= 0) {
          this.$set(this.testResults, index, result);
        } else {
          this.testResults.push(result);
        }
        this.$message.success(`${this.selectedService} 测试完成`);
      } catch (error) {
        this.$message.error(`测试失败: ${error.message}`);
      }
    },
    
    async clearAllData() {
      try {
        await this.$confirm('确定要清空所有数据吗？此操作不可恢复！', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        this.clearing = true;
        for (const [serviceName, service] of Object.entries(this.sheetServiceMap)) {
          if (service && typeof service.clearAll === 'function') {
            await service.clearAll();
          }
        }
        this.checkDataStatus();
        this.$message.success('所有数据已清空');
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`清空失败: ${error.message}`);
        }
      } finally {
        this.clearing = false;
      }
    },
    
    async clearSingleService() {
      if (!this.selectedService) return;
      
      try {
        await this.$confirm(`确定要清空 ${this.selectedService} 的数据吗？`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const service = this.sheetServiceMap[this.selectedService];
        if (service && typeof service.clearAll === 'function') {
          await service.clearAll();
          this.checkDataStatus();
          this.$message.success(`${this.selectedService} 数据已清空`);
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`清空失败: ${error.message}`);
        }
      }
    },
    
    checkDataStatus() {
      this.dataStatus = [];
      
      // 检查localStorage中的数据
      for (const [serviceName, service] of Object.entries(this.sheetServiceMap)) {
        try {
          // 尝试获取数据（这里需要根据实际的服务实现来调整）
          let data = null;
          let storageKey = '';
          let dataCount = 0;
          
          // 根据服务名称推断存储键
          const keyMap = {
            '节点': 'nodeInfo',
            '机组': 'unitInfo',
            '水电三段式出力': 'hydropowerOutput',
            '机组指定出力': 'unitSpecifiedOutput',
            '机组指定状态': 'unitStatus',
            '机组报价': 'unitPrice',
            '风区信息': 'windArea',
            '风电场': 'windStation',
            '风区之间相关系数': 'windAreaCorrelation',
            '光区信息': 'solarArea',
            '光伏电站': 'solarStation',
            '光区之间相关系数': 'solarAreaCorrelation',
            '线路': 'lineStation',
            '断面': 'section',
            '断面线路包含关系': 'sectionLineRelation',
            '断面指定出力': 'sectionSpecifiedOutput',
            '负荷曲线': 'loadCurv',
            '负荷曲线-建模': 'loadModelingData',
            '机组指定出力-建模': 'unitModelingData'
          };
          
          storageKey = keyMap[serviceName] || serviceName;
          const storedData = localStorage.getItem(storageKey);
          
          if (storedData) {
            try {
              data = JSON.parse(storedData);
              dataCount = Array.isArray(data) ? data.length : 1;
            } catch (e) {
              dataCount = 1; // 非JSON数据
            }
          }
          
          this.dataStatus.push({
            serviceName,
            dataCount,
            storageKey,
            data
          });
        } catch (error) {
          console.error(`检查 ${serviceName} 数据状态失败:`, error);
        }
      }
    },
    
    viewData(row) {
      this.currentData = row;
      this.dataDialogVisible = true;
    },
    
    async clearServiceData(row) {
      try {
        await this.$confirm(`确定要清空 ${row.serviceName} 的数据吗？`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        localStorage.removeItem(row.storageKey);
        this.checkDataStatus();
        this.$message.success(`${row.serviceName} 数据已清空`);
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`清空失败: ${error.message}`);
        }
      }
    },

    downloadTestData() {
      try {
        testDataGenerator.downloadTestExcel(null, 'test_data.xlsx');
        this.$message.success('测试数据文件已下载');
      } catch (error) {
        this.$message.error(`下载失败: ${error.message}`);
      }
    },

    downloadTemplate() {
      try {
        testDataGenerator.downloadEmptyTemplate(null, 'template.xlsx');
        this.$message.success('模板文件已下载');
      } catch (error) {
        this.$message.error(`下载失败: ${error.message}`);
      }
    }
  }
};
</script>

<style scoped>
.service-test {
  padding: 20px;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
  margin: 2px 0;
}

.success-text {
  color: #67c23a;
  font-size: 12px;
}

.data-preview {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
