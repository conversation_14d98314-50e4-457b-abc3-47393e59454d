// 测试数据加载脚本
// 在浏览器控制台中运行此脚本来填充测试数据

// 测试数据（使用正确的存储键）
const testData = {
  'node_info_data': [
    { id: 1, name: '节点A', code: 'NODE_A', type: '类型1', region: '区域1', voltageLevel: '110kV' },
    { id: 2, name: '节点B', code: 'NODE_B', type: '类型2', region: '区域2', voltageLevel: '220kV' },
    { id: 3, name: '节点C', code: 'NODE_C', type: '类型1', region: '区域1', voltageLevel: '500kV' }
  ],

  'unit_info_data': [
    { id: 1, unitName: '机组1', capacity: 300, type: '火电', status: '运行' },
    { id: 2, unitName: '机组2', capacity: 600, type: '水电', status: '运行' }
  ],

  'hydropower_output_data': [
    { id: 1, powerPlantName: '枫树坝', startTime: '20300101', inputTypeA: '平水年', inputTypeB: '平均出力', month1: 50, month2: 53 },
    { id: 2, powerPlantName: '枫树坝', startTime: '20300101', inputTypeA: '平水年', inputTypeB: '预想出力', month1: 150, month2: 136 }
  ],

  'unitSpecifiedOutput_data': [
    { id: 1, unitName: 'QD_RD#桔乡电厂(四会热电)#4', constraintType: '出力下限', hours: 4800, energy: 13.1, modelingCount: 1 },
    { id: 2, unitName: 'XD#岭东#1', constraintType: '指定出力', hours: 6243.81, energy: 187.31, modelingCount: 365 }
  ],

  'unit_status_data': [
    { id: 1, unitId: 1, hour: 1, status: 1 },
    { id: 2, unitId: 1, hour: 2, status: 1 },
    { id: 3, unitId: 2, hour: 1, status: 0 }
  ],

  'unit_price_data': [
    { id: 1, unitId: 1, segment: 1, price: 0.35 },
    { id: 2, unitId: 1, segment: 2, price: 0.40 },
    { id: 3, unitId: 2, segment: 1, price: 0.30 }
  ],

  'windArea_data': [
    { id: 1, areaName: '默认风区', startDate: '2030-01-01', weibullC: 5.805480868, weibullK: 2.374547193 }
  ],

  'windStation_data': [
    { id: 1, stationName: 'FD#珠江风电#1', region: '珠江区域', stationCode: 'GD_ZDN', cutInSpeed: 3.5, ratedSpeed: 12 }
  ],

  'windAreaCorrelation_data': [
    { id: 1, area1Name: '默认风区', area2Name: '默认风区', correlation: 1 },
    { id: 2, area1Name: '默认风区', area2Name: '珠江风区', correlation: 0.8 }
  ],

  'solarArea_data': [
    { id: 1, areaName: '默认光区', startDate: '2030-01-01', solarIrradiance: 1200 }
  ],

  'solarStation_data': [
    { id: 1, stationName: 'PV#珠江光伏#1', region: '珠江区域', stationCode: 'GD_ZDN_PV', capacity: 100 }
  ],

  'solarAreaCorrelation_data': [
    { id: 1, area1Name: '默认光区', area2Name: '默认光区', correlation: 1 },
    { id: 2, area1Name: '默认光区', area2Name: '珠江光区', correlation: 0.8 }
  ],

  'line_station_data': [
    { id: 1, lineName: '线路1', fromNode: '节点A', toNode: '节点B', length: 100, type: 'AC' },
    { id: 2, lineName: '线路2', fromNode: '节点B', toNode: '节点C', length: 150, type: 'DC' }
  ],

  'section_data': [
    { id: 1, name: '断面1', limit: 2000 },
    { id: 2, name: '断面2', limit: 1500 }
  ],

  'section_line_relation_data': [
    { id: 1, sectionId: 1, lineId: 1, direction: 1 },
    { id: 2, sectionId: 1, lineId: 2, direction: -1 }
  ],

  'section_specified_output_data': [
    { id: 1, sectionId: 1, hour: 1, output: 800 },
    { id: 2, sectionId: 1, hour: 2, output: 900 }
  ],

  'load_curv_data': [
    { id: 1, name: '负荷曲线1', type: '日负荷' },
    { id: 2, name: '负荷曲线2', type: '周负荷' }
  ],

  'load_modeling_data_all': [
    { loadCurvId: 1, hour: 1, load: 5000 },
    { loadCurvId: 1, hour: 2, load: 5200 },
    { loadCurvId: 2, hour: 1, load: 4800 }
  ],

  'unit_modeling_data_all': [
    { unitId: 1, hour: 1, modelOutput: 245 },
    { unitId: 1, hour: 2, modelOutput: 275 },
    { unitId: 2, hour: 1, modelOutput: 395 }
  ]
};

// 加载测试数据到 localStorage
function loadTestData() {
  console.log('开始加载测试数据...');
  
  let loadedCount = 0;
  for (const [key, data] of Object.entries(testData)) {
    try {
      localStorage.setItem(key, JSON.stringify(data));
      console.log(`✓ 已加载: ${key} (${data.length} 条记录)`);
      loadedCount++;
    } catch (error) {
      console.error(`✗ 加载失败: ${key} - ${error.message}`);
    }
  }
  
  console.log(`\n测试数据加载完成: ${loadedCount}/${Object.keys(testData).length} 个数据集已加载`);
  console.log('请刷新页面查看效果');
}

// 清空所有测试数据
function clearTestData() {
  console.log('开始清空测试数据...');
  
  let clearedCount = 0;
  for (const key of Object.keys(testData)) {
    try {
      localStorage.removeItem(key);
      console.log(`✓ 已清空: ${key}`);
      clearedCount++;
    } catch (error) {
      console.error(`✗ 清空失败: ${key} - ${error.message}`);
    }
  }
  
  console.log(`\n测试数据清空完成: ${clearedCount}/${Object.keys(testData).length} 个数据集已清空`);
}

// 检查数据状态
function checkDataStatus() {
  console.log('检查数据状态...\n');
  
  for (const [key, expectedData] of Object.entries(testData)) {
    const storedData = localStorage.getItem(key);
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        const count = Array.isArray(parsedData) ? parsedData.length : 1;
        console.log(`✓ ${key}: ${count} 条记录`);
      } catch (error) {
        console.log(`! ${key}: 数据格式错误`);
      }
    } else {
      console.log(`✗ ${key}: 无数据`);
    }
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.loadTestData = loadTestData;
  window.clearTestData = clearTestData;
  window.checkDataStatus = checkDataStatus;
  
  console.log('测试数据工具已加载！');
  console.log('使用方法：');
  console.log('  loadTestData()  - 加载测试数据');
  console.log('  clearTestData() - 清空测试数据');
  console.log('  checkDataStatus() - 检查数据状态');
}

// Node.js 环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { loadTestData, clearTestData, checkDataStatus, testData };
}
