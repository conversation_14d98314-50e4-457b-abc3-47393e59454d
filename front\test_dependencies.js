/**
 * 依赖测试文件
 * 这个文件用于测试Vue和Element UI的依赖是否正确加载
 */

// 检测Vue
try {
    console.log("检测Vue版本:", Vue.version);
    if (Vue.version.startsWith("2.")) {
        console.log("Vue 2.x 加载成功");
    } else {
        console.warn("警告: 加载的Vue版本不是2.x");
    }
} catch (e) {
    console.error("Vue加载失败:", e);
}

// 检测Element UI
try {
    if (ELEMENT) {
        console.log("Element UI加载成功");
    }
} catch (e) {
    console.error("Element UI加载失败:", e);
}

// 检测XLSX和FileSaver
try {
    if (XLSX) {
        console.log("XLSX加载成功");
    }
} catch (e) {
    console.error("XLSX加载失败:", e);
}

try {
    if (saveAs) {
        console.log("FileSaver加载成功");
    }
} catch (e) {
    console.error("FileSaver加载失败:", e);
}

console.log("依赖检测完成"); 