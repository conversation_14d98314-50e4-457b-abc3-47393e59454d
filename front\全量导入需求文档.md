# 全量导入功能开发需求文档

## 1. 功能概述

本功能旨在实现从单个 Excel 文件中一次性导入系统所需的所有基础数据。用户通过界面选择指定的 Excel 文件（如 `运行模拟数据20250708.xls`），系统将自动解析文件中的各个工作表，并将数据写入前端的 `localStorage`，从而完成对节点、机组、风电、光伏、线路、负荷等所有信息的初始化或更新。

此功能是整个“数据维护”模块的核心，其实施依赖于所有数据展示页面的完善。

## 2. 前置任务：完善数据维护页面

在实现全量导入之前，必须确保所有对应的数据管理页面及其配套的数据服务 (`Service.js`) 已开发完毕。根据系统菜单截图，以下页面需要新建或完善：

| 菜单路径                                   | 页面/组件                  | 状态             | 开发任务                                                                                                  |
| ------------------------------------------ | -------------------------- | ---------------- | --------------------------------------------------------------------------------------------------------- |
| **节点信息 -> 节点**                 | `NodeInfo`               | **需新建** | 创建完整的 `NodeInfo` 页面，包括数据表格、工具栏、编辑对话框和 `nodeInfoService.js`。                 |
| **机组信息 -> 机组**                 | `UnitInfo`               | **需新建** | 创建完整的 `UnitInfo` 页面，包括数据表格、工具栏、编辑对话框和 `unitInfoService.js`。                 |
| **机组信息 -> 水电三段式出力**       | `HydropowerOutput`       | **需新建** | 创建完整的 `HydropowerOutput` 页面，包括数据表格、工具栏、编辑对话框和 `hydropowerOutputService.js`。 |
| **线路断面信息 -> 线路**             | `LineStation`            | 已存在           | 功能已存在，可直接使用。                                                                                  |
| **线路断面信息 -> 断面**             | `Section`                | **需新建** | 创建 `Section` 页面及 `sectionService.js`。                                                           |
| **线路断面信息 -> 断面线路包含关系** | `SectionLineRelation`    | **需新建** | 创建 `SectionLineRelation` 页面及 `sectionLineRelationService.js`。                                   |
| **线路断面信息 -> 断面指定出力**     | `SectionSpecifiedOutput` | **需新建** | 创建 `SectionSpecifiedOutput` 页面及 `sectionSpecifiedOutputService.js`。                             |
| *其他页面*                               | -                          | 已存在           | `机组指定出力`, `机组指定状态` 等其余页面功能已存在，可直接使用。                                     |

## 3. 全量导入功能设计

### 3.1. UI/UX 设计

* **入口点**: 在主应用 `App.vue` 的顶部导航栏或一个显眼的位置，添加一个“全量导入”按钮。
* **交互流程**:
  1. 用户点击“全量导入”按钮。
  2. 系统弹出确认框，警告用户：“**此操作将清空所有现有数据并从文件导入，是否继续？**”
  3. 用户确认后，系统弹出文件选择对话框，限制文件类型为 `.xls` 和 `.xlsx`。
  4. 用户选择文件后，界面显示一个全屏的加载遮罩 (`el-loading`)，并动态显示导入状态，例如：
     * "正在解析Excel文件..."
     * "正在清空旧数据..."
     * "正在导入 '节点' 数据..."
     * "正在导入 '机组' 数据..."
     * ...
  5. 导入完成后，弹窗提示用户“全量导入成功！”并显示一个简短的总结报告。
* **导入报告**:
  * **成功**: “数据导入完成！共处理 X 个工作表，导入 Y 条数据。”
  * **失败/部分成功**: “导入过程中出现问题。请检查文件格式。详情：[显示详细错误信息，如 '工作表 "节点" 不存在' 或 '机组数据格式错误']”。

### 3.2. 技术实现方案

* **文件解析库**: 在前端使用 `xlsx` (SheetJS) 库来解析用户上传的 Excel 文件。需要先安装该库：
  ```bash
  npm install xlsx
  ```
* **核心服务 `fullImportService.js`**:
  * 创建一个新的服务文件 `front/src/services/fullImportService.js`。
  * 该服务将提供一个核心方法 `importAllDataFromFile(file)`。
  * 此方法负责：
    1. 使用 `xlsx` 库将文件数据读取为 JSON 对象。
    2. 定义一个工作表名称到服务模块的映射关系（见下表）。
    3. 依次调用每个相关服务的 `clearAll()` 和 `batchImport()` 方法。
* **改造现有/新建服务**:
  * 所有相关的数据服务文件 (`/src/services/*.js`) 都必须提供两个标准接口：
    * `clearAll()`: 清空 `localStorage` 中该模块的所有数据。
    * `batchImport(dataArray)`: 接收从 Excel 解析出的数据数组，并将其一次性写入 `localStorage`。
* **工作表与服务的映射关系 (暂定)**:

| 期望的工作表名称 (Sheet Name) | 对应服务 (Service)                   |
| ----------------------------- | ------------------------------------ |
| `节点`                      | `nodeInfoService.js`               |
| `机组`                      | `unitInfoService.js`               |
| `水电三段式出力`            | `hydropowerOutputService.js`       |
| `机组指定出力`              | `unitSpecifiedOutputService.js`    |
| `机组指定状态`              | `unitStatusService.js`             |
| `机组报价`                  | `unitPriceService.js`              |
| `风区信息`                  | `windAreaService.js`               |
| `风电场`                    | `windStationService.js`            |
| `风区之间相关系数`          | `windAreaCorrelationService.js`    |
| `光区信息`                  | `solarAreaService.js`              |
| `光伏电站`                  | `solarStationService.js`           |
| `光区之间相关系数`          | `solarAreaCorrelationService.js`   |
| `线路`                      | `lineStationService.js`            |
| `断面`                      | `sectionService.js`                |
| `断面线路包含关系`          | `sectionLineRelationService.js`    |
| `断面指定出力`              | `sectionSpecifiedOutputService.js` |
| `负荷曲线`                  | `loadCurvService.js`               |
| `负荷曲线-建模`             | `loadModelingDataService.js`       |
| `机组指定出力-建模`         | `unitModelingDataService.js`       |

*注意：对于“机组指定出力”和“负荷曲线”这类包含主从表（建模数据）的复杂结构，Excel中可能需要两个Sheet页（一个主数据，一个建模数据），或者通过特殊列来关联，这需要与数据提供方确认。*

## 4. 开发与实施计划

1. **阶段一：页面开发 (前置任务)**
   * 按照 **“2. 前置任务”** 列表，逐一完成所有缺失页面的开发。每个页面都应是功能完整的 CRUD 模块。
2. **阶段二：实现全量导入**
   * 安装 `xlsx` 库。
   * 在 `App.vue` 中添加“全量导入”的 UI 入口和交互逻辑。
   * 创建 `fullImportService.js` 并实现核心导入逻辑。
   * 为所有涉及的服务添加 `clearAll()` 和 `batchImport()` 方法。
3. **阶段三：联调与测试**
   * 使用真实的 `运行模拟数据.xls` 文件进行端到端测试。
   * 验证每个页面的数据是否都已正确加载。
   * 测试各种异常情况（如文件格式错误、工作表缺失、数据行格式不正确等）。
