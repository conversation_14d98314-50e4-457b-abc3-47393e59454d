**需求文档：负荷曲线及建模数据管理**

#### **1. 功能概述**

与机组指定出力页面一样，本功能旨在开发“负荷曲线”页面，用于展示和管理各机组的基本出力信息。此页面还将集成一个“建模数据”管理功能，用户可通过点击特定单元格来打开一个专用对话框，以管理该机组详细的分时段出力数据。

#### **2. 功能点分解**

**2.1. 主页面：负荷曲线 (`LoadCurv`)**

* **页面定位**:
  * 在 `front/src/components/` 下创建新目录 `LoadCurv` 来存放其相关组件。
  * 在左侧导航栏“负荷信息”分类下，添加一个名为“负荷曲线”的菜单项，指向这个新页面。
* **页面布局与功能**:
  * **工具栏**: 包含“批量维护”和“删除”按钮，以及一个按“区域名称”搜索的区域。
  * **数据表格**:
    * **列定义**: 复选框、区域名称、建模数。
    * **交互**: “建模数”列的内容将是一个可点击的链接。点击后会打开“负荷建模数据”对话框，并将当前机组的ID和名称传递过去。
* **数据服务 (`LoadCurvService.js`)**:
  * 创建一个服务来管理此页面的数据，包括获取列表、删除、以及批量维护的逻辑。

**2.2. 功能模块：机组建模数据对话框 (`LoadModelingDataDialog.vue`)**

* **触发方式**: 在“负荷曲线”页面的表格中，点击“建模数”链接时触发。
* **组件结构**:
  * 这是一个独立的对话框组件，动态显示在主页面之上。
  * 对话框的标题将动态设置为所选区域的名称，例如 “系统”。
* **对话框功能**:
  * **工具栏**: 包含“修改”、“取消修改”、“保存”、“导出Excel”、“导入Excel”和“清空”六个按钮。
  * **数据表格**: 用于展示该负荷的建模数据，列包括：区域名称、起始日期、结束日期、时段1...时段24 (MW)。
  * **数据表格的交互**:
    * 点击“修改”按钮后，数据表格变为可编辑状态，用户可以修改表格中的数据。
    * 点击“取消修改”按钮后，数据表格恢复为只读状态，用户可以取消修改。
    * 点击“保存”按钮后，数据表格中的数据被保存到数据库中。
    * 点击“导出Excel”按钮后，数据表格中的数据被导出到Excel文件中。
    * 点击“导入Excel”按钮后，数据表格中的数据被导入到Excel文件中。
* **数据服务 (`unitModelingDataService.js`)**:
  * 创建另一个新的服务来专门处理建模数据的获取、清空、导入和模板导出等操作。

#### **3. 备注**

**批量维护功能**: “批量维护”按钮的具体功能是点击此按钮后，允许用户导入导出包含建模数里的所有数据，即负荷曲线的所有数据

**建模数据编辑方式**: 在弹出的建模数据对话框中，数据是只读的，点击对话框的修改按钮可以直接在表格里编辑。

**“建模数”的含义**: 该数字代表对应机组的建模数据记录总数（例如，365代表一年的365条日负荷数据）
