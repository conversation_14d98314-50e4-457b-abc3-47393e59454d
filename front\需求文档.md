### 需求文档

#### 1. 整体布局

采用业界成熟的“上-左-右”布局模式：

* **顶部导航栏 (Header)**：放置全局性操作，如用户中心、系统设置等。
* **左侧菜单栏 (Sidebar)**：作为一级功能导航，清晰展示系统的核心模块。
* **主内容区 (Main Content Area)**：动态展示所选功能模块的详细内容。

#### 2. 功能模块详解

##### 2.1 顶部导航栏

* **Logo/系统名称**: 位于左上角，暂定为“调度运行仿真平台”。
* **全局链接**: 位于右上角，包含 `首页`, `个人中心`, `系统配置`, `退出登录` 四个链接。

##### 2.2 左侧菜单栏

* 设计为可伸缩的侧边栏，以节省屏幕空间。
* 包含以下一级菜单：
  * `基础数据`
  * `运行模拟` (核心功能)
  * `结果展示`
  * `方案对比`
  * `全景网架`
  * `模型库`
  * `资源库`

##### 3.3 主内容区 - `运行模拟` 模块

这是我们要复刻的核心功能区。

* **3.3.1 方案管理**

  * **方案列表**: 以卡片或列表形式展示已创建的方案。每个方案都应清晰地展示其名称，并提供“修改方案”和“删除方案”的操作入口。
  * **添加方案**:
    * 界面上需要有一个醒目的“添加方案”按钮。
    * 点击后，弹出一个模态对话框。
    * 对话框内包含两个表单项：
      * `方案名` (文本输入，必填)
      * `方案描述` (文本域，选填)
    * 对话框提供“完成”和“取消”按钮。
  * **修改方案**:
    * 点击“修改方案”按钮，弹出的模态对话框需要预先填充该方案的现有信息。
  * **删除方案**:
    * 点击“删除方案”按钮，为防止误操作，需要弹出一个确认对话框。
* **3.3.2 运行模拟配置**

  * 选中一个方案后，进入该方案的配置界面。
  * 界面顶部需要明确标识出当前正在配置的方案名称。
  * 提供一个“运行模拟”按钮，用于启动后台计算。
  * 配置项繁多，因此采用 **标签页 (Tabs)** 来组织，分类如下：
    * `基本参数`
    * `运行选项`
    * `电源选项`
    * `调峰选项`
    * `检修选项`
    * `水电运行选项`
    * `新能源运行选项`
    * `优化选项`
  * 每个标签页下是对应的配置表单（具体表单项待后续细化）。
* **3.3.3 进程管理**

  * 用于跟踪和管理模拟任务的执行情况。
  * 使用 **标签页** 分为 `实时进程` 和 `历史进程`。
  * 提供基于“进程方案”名称的 **搜索** 功能和 **重置** 功能。
  * 进程列表需要展示任务的关键信息，如：方案名、状态、开始/结束时间等。
  * 对于“实时进程”，需要有一个进度条来可视化任务的执行进度。

  ##### 3.4 主内容区 - `基础数据` 模块


  * **3.4.1 节点信息页面**

    * **功能定位**: 此页面用于对电网中的节点信息进行集中的CRUD（创建、读取、更新、删除）管理。
    * **页面布局**:

      * **操作栏 (Toolbar)**: 位于表格上方，提供页面级别的核心操作。
      * **筛选/搜索区 (Filter Area)**: 紧随操作栏，提供多个维度的查询条件。
      * **数据表格 (Data Table)**: 页面的核心，用于展示节点数据。
      * **分页器 (Pagination)**: 位于表格下方，用于浏览大量数据。
    * **详细设计**:

      * **操作栏**:

        * `新增节点` 按钮：点击后弹出用于创建新节点的模态框。
        * `批量删除` 按钮：激活表格多选框后可用，用于一次性删除多个节点。
        * `导入` / `导出` 按钮：用于数据的批量导入和导出（通常是 Excel 或 CSV 格式）。
        * 显示/隐藏默认列：用于显示/隐藏默认的列：

        | 是否两控区 | 节点切除损失 | 节点负荷占比(%) | 固定负荷 | 节点强迫停运率 | 节点故障恢复时间 | 供电区域 |
      * **筛选/搜索区**:

        * `节点名称/节点ID`：文本输入框，支持模糊搜索。
        * `查询` 按钮：执行搜索。
        * `重置` 按钮：清空所有筛选条件。
      * **数据表格**:

        * 需要支持 **多选** 功能，以配合批量删除。
        * **列定义**:

          * `节点名称`
          * `节点ID` (唯一标识)
          * 起始日期
          * 退役日期
          * `节点所属区域`
          * 默认的列：

          | 是否两控区 | 节点切除损失 | 节点负荷占比(%) | 固定负荷 | 节点强迫停运率 | 节点故障恢复时间 | 供电区域 |
      * **新增/编辑模态框**:

        * 无论是新增还是编辑，弹出的模态框应包含一个表单。
        * 表单字段应与表格列一一对应，并提供输入验证（如必填项、格式要求等）。
        * 包含 `确认` 和 `取消` 按钮。

##### 3.4.2 机组信息/水电三段式处理页面

*   **功能定位**: 此页面用于管理和处理水电机组的三段式数据，包括机组基本信息和三段式曲线参数的配置。

*   **页面布局**:
    *   **左侧树形导航**: 显示机组的层级结构，用户可通过点击选择特定机组。
    *   **主内容区**: 分为两个主要部分：
        *   **机组基本信息表单**: 位于上部，显示所选机组的基本参数。
        *   **三段式曲线配置**: 位于下部，通过表格形式展示和编辑三段式曲线数据。

*   **详细设计**:
    *   **左侧树形导航**:
        *   显示电站和机组的层级结构
        *   支持展开/折叠操作
        *   当前选中项高亮显示
        *   树节点包含图标，直观区分电站和机组

    *   **机组基本信息表单**:
        *   **字段列表**:
            *   `机组ID`: 唯一标识符，不可编辑
            *   `机组名称`: 文本输入
            *   `所属电站`: 下拉选择
            *   `机组类型`: 下拉选择（如：水轮发电机组、抽水蓄能机组等）
            *   `额定功率(MW)`: 数值输入
            *   `最大出力(MW)`: 数值输入
            *   `最小出力(MW)`: 数值输入
            *   `投产日期`: 日期选择器
            *   `退役日期`: 日期选择器
        *   表单底部包含 `保存` 和 `重置` 按钮

    *   **三段式曲线配置**:
        *   **工具栏**:
            *   `添加段`: 向三段式曲线添加新的数据点
            *   `删除段`: 删除选中的数据点
            *   `导入`: 从Excel文件导入三段式曲线数据
            *   `导出`: 将当前三段式曲线数据导出为Excel文件
            *   `保存`: 保存当前编辑的三段式曲线数据
        *   **数据表格**:
            *   表格列定义:
                *   `段序号`: 自动生成的序号，表示曲线段的顺序
                *   `流量(m³/s)`: 数值输入
                *   `水头(m)`: 数值输入
                *   `出力(MW)`: 数值输入
                *   `效率(%)`: 数值输入，可选
            *   支持表格内直接编辑
            *   支持排序和重新排序
        *   **曲线可视化**:
            *   可选功能：提供三段式曲线的图形化展示
            *   X轴表示流量，Y轴表示出力
            *   点击曲线上的点可快速定位到对应的表格行

*   **交互逻辑**:
    *   用户从左侧树形导航选择机组后，右侧表单和表格自动加载该机组的数据
    *   基本信息和三段式曲线数据分开保存
    *   编辑任何数据后，需点击对应的保存按钮才会保存更改
    *   导入Excel时，会覆盖当前的三段式曲线数据
    *   表格支持批量操作，如复制、粘贴等

*   **数据验证**:
    *   流量、水头、出力必须为正数
    *   效率范围应在0-100%之间
    *   段序号必须连续且不重复
    *   投产日期必须早于退役日期
    *   最小出力必须小于最大出力

机组信息/水电三段式处理页面
![1752025932767](image/需求文档/1752025932767.png)

## 4. 运行说明

### 4.1 环境准备

- Node.js 12.x 或更高版本
- npm 6.x 或更高版本

### 4.2 安装依赖

在项目根目录下执行：

```bash
npm install
```

然后在 front 目录下执行：

```bash
cd front
npm install
```

### 4.3 启动项目

方式一：使用批处理文件

双击 `front/启动前端.bat` 文件即可启动项目。

方式二：使用命令行

在项目根目录下执行：

```bash
npm run serve
```

或者在 front 目录下执行：

```bash
npm run serve
```

### 4.4 访问项目

启动成功后，在浏览器中访问：

```
http://localhost:8080
```

## 5. 已实现功能

- [x] 节点信息页面
  - [x] 数据表格展示
  - [x] 新增、编辑、删除节点
  - [x] Excel导入导出
  - [x] 数据持久化（localStorage）

- [x] 机组信息/水电三段式处理页面
  - [x] 树形结构展示电站和机组
  - [x] 机组基本信息表单
  - [x] 三段式曲线数据表格
  - [x] Excel导入导出
  - [x] 数据持久化（localStorage）