# launcher.py
import subprocess
from main import process_excel  # 导入主模块的函数


def run_scripts():
    # 首先运行main.py的处理函数获取文件路径
    output_file = process_excel()

    if not output_file:
        print("错误: 未获取到文件路径")
        return

    # 要按顺序运行的文件列表（不需要再运行main.py）
    scripts = [
        "validate_excel.py",
        "machine_validator_app.py"
    ]

    for script in scripts:
        print(f"\n正在运行: {script}")

        # 使用subprocess传递参数
        subprocess.run(["python", script, output_file])

        # 或者使用os.system（注意路径中的空格）
        # os.system(f'python "{script}" "{output_file}"')


if __name__ == "__main__":
    run_scripts()