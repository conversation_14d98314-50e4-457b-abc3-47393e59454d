"""
机组验证器应用程序 - 专门处理机组指定出力和机组指定状态
"""
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import sys
import os
import importlib

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.machine_validator import MachineValidator
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class MachineValidatorApp:
    """机组验证器应用程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("机组验证器 - 机组指定出力和状态检查")
        self.validator = None
        self.file_path = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口大小和位置
        window_width = 500
        window_height = 400
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.resizable(False, False)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="机组验证器", 
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        subtitle_label = ttk.Label(
            main_frame, 
            text="专门处理机组指定出力和机组指定状态", 
            font=("Arial", 10)
        )
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        self.file_label = ttk.Label(file_frame, text="未选择文件", foreground="gray")
        self.file_label.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        select_button = ttk.Button(
            file_frame, 
            text="选择Excel文件", 
            command=self.select_file
        )
        select_button.grid(row=0, column=1)
        
        file_frame.columnconfigure(0, weight=1)
        
        # 操作区域
        operation_frame = ttk.LabelFrame(main_frame, text="验证操作", padding="10")
        operation_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 检查机组指定状态按钮
        self.status_button = ttk.Button(
            operation_frame,
            text="检查机组指定状态",
            command=self.check_machine_status,
            state="disabled",
            width=25
        )
        self.status_button.grid(row=0, column=0, pady=5, padx=5)
        
        # 检查机组指定出力按钮
        self.output_button = ttk.Button(
            operation_frame,
            text="检查机组指定出力",
            command=self.check_machine_output,
            state="disabled",
            width=25
        )
        self.output_button.grid(row=0, column=1, pady=5, padx=5)
        
        # 批量检查按钮
        self.batch_button = ttk.Button(
            operation_frame,
            text="批量检查（状态+出力）",
            command=self.batch_check,
            state="disabled",
            width=25
        )
        self.batch_button.grid(row=1, column=0, columnspan=2, pady=10)
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="10")
        status_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建文本框和滚动条
        self.status_text = tk.Text(
            status_frame, 
            height=8, 
            width=60, 
            wrap=tk.WORD,
            font=("Consolas", 9)
        )
        scrollbar = ttk.Scrollbar(status_frame, orient="vertical", command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)
        
        # 配置主框架的权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # 初始状态信息
        self.log_message("欢迎使用机组验证器！")
        self.log_message("请先选择要验证的Excel文件。")
        
    def log_message(self, message):
        """在状态文本框中添加消息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def select_file(self):
        """选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="请选择要验证的Excel文件",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
            
        self.file_path = file_path
        filename = os.path.basename(file_path)
        self.file_label.config(text=filename, foreground="black")
        
        self.log_message(f"已选择文件: {filename}")
        self.log_message("正在加载文件...")
        
        try:
            # 重新加载模块以确保使用最新代码
            importlib.reload(sys.modules['src.core.machine_validator'])
            
            # 创建验证器实例
            self.validator = MachineValidator(file_path)
            
            # 加载数据
            if self.validator.load_data():
                self.log_message("✓ 文件加载成功！")
                self.log_message("可以开始验证操作。")
                
                # 启用按钮
                self.status_button.config(state="normal")
                self.output_button.config(state="normal")
                self.batch_button.config(state="normal")
            else:
                self.log_message("✗ 文件加载失败！")
                self.validator = None
                
        except Exception as e:
            self.log_message(f"✗ 加载文件时出错: {str(e)}")
            self.validator = None
            
    def check_machine_status(self):
        """检查机组指定状态"""
        if not self.validator:
            messagebox.showerror("错误", "请先选择并加载Excel文件")
            return
            
        self.log_message("开始检查机组指定状态...")
        
        try:
            if self.validator.check_machine_status():
                self.log_message("✓ 机组指定状态检查完成")
                messagebox.showinfo("成功", "机组指定状态检查完成")
            else:
                self.log_message("✗ 机组指定状态检查失败")
                
        except Exception as e:
            self.log_message(f"✗ 检查机组指定状态时出错: {str(e)}")
            
    def check_machine_output(self):
        """检查机组指定出力"""
        if not self.validator:
            messagebox.showerror("错误", "请先选择并加载Excel文件")
            return
            
        self.log_message("开始检查机组指定出力...")
        
        try:
            if self.validator.check_machine_output():
                self.log_message("✓ 机组指定出力检查完成")
                messagebox.showinfo("成功", "机组指定出力检查完成")
            else:
                self.log_message("✗ 机组指定出力检查失败")
                
        except Exception as e:
            self.log_message(f"✗ 检查机组指定出力时出错: {str(e)}")
            
    def batch_check(self):
        """批量检查机组指定状态和出力"""
        if not self.validator:
            messagebox.showerror("错误", "请先选择并加载Excel文件")
            return
            
        self.log_message("开始批量检查...")
        
        try:
            # 检查机组指定状态
            self.log_message("1/2 检查机组指定状态...")
            status_result = self.validator.check_machine_status()
            
            if status_result:
                self.log_message("✓ 机组指定状态检查完成")
            else:
                self.log_message("✗ 机组指定状态检查失败")
            
            # 检查机组指定出力
            self.log_message("2/2 检查机组指定出力...")
            output_result = self.validator.check_machine_output()
            
            if output_result:
                self.log_message("✓ 机组指定出力检查完成")
            else:
                self.log_message("✗ 机组指定出力检查失败")
                
            # 显示总结
            if status_result and output_result:
                self.log_message("🎉 批量检查全部完成！")
                messagebox.showinfo("成功", "批量检查全部完成！")
            else:
                self.log_message("⚠️ 批量检查完成，但有部分失败")
                messagebox.showwarning("完成", "批量检查完成，但有部分失败，请查看详细信息")
                
        except Exception as e:
            self.log_message(f"✗ 批量检查时出错: {str(e)}")
            
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MachineValidatorApp()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败：\n{str(e)}")
        logger.error(f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
