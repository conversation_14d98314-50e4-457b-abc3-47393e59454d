"""
主程序入口
"""
import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
from openpyxl import load_workbook
from datetime import datetime
from src.core.excel_reader import ExcelReader
from src.processors.machine_name import MachineNameProcessor
from src.processors.region_processor import RegionProcessor
from src.processors.region_letter_processor import RegionLetterProcessor
from src.processors.machine_type_code_processor import MachineTypeCodeProcessor
from src.utils.logger import setup_logger
from src.utils.config import EXCEL_CONFIG, COLUMN_NAMES

logger = setup_logger(__name__)

def convert_date(date_str, use_first_day=False):
    """
    转换日期格式
    
    Args:
        date_str: 日期字符串，格式如 '2025/6/30'
        use_first_day: 是否使用每月第一天
        
    Returns:
        str: 转换后的日期字符串，格式如 '20250630' 或 '20250601'
    """
    try:
        if pd.isna(date_str):
            return ''
            
        # 解析日期
        date = pd.to_datetime(date_str)
        
        # 如果使用每月第一天，将日期设为当月第一天
        if use_first_day:
            date = date.replace(day=1)
            
        # 格式化为YYYYMMDD
        return date.strftime('%Y%m%d')
    except Exception as e:
        logger.warning(f"日期转换失败: {date_str}, 错误: {str(e)}")
        return ''

def process_excel():
    """处理Excel文件的主函数"""
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    try:
        # 选择输入文件
        input_file = filedialog.askopenfilename(
            title="请选择电源明细表Excel文件",
            filetypes=[("Excel files", "*.xlsx")]
        )
        
        if not input_file:
            messagebox.showinfo("提示", "未选择文件，程序退出")
            return

        # 显示正在读取文件的提示
        messagebox.showinfo("提示", "正在读取Excel文件，请稍候...")
        
        # 读取Excel文件
        reader = ExcelReader(input_file)
        df = reader.read()
        
        # 读取分区映射表
        region_mapping = pd.read_excel(
            input_file,
            sheet_name='分区',
            usecols='B:C',
            names=['B', 'C'],  # 保持原始列名
            engine=EXCEL_CONFIG['engine'],
            na_filter=EXCEL_CONFIG['na_filter']
        )
        
        # 读取字母映射表
        letter_mapping = pd.read_excel(
            input_file,
            sheet_name='备注',
            usecols='E:F',
            names=['E', 'F'],  # 保持原始列名
            skiprows=16,  # 跳过前16行，本来是17行，不知道为啥17行就会把第一个珠东南给丢掉了
            engine=EXCEL_CONFIG['engine'],
            na_filter=EXCEL_CONFIG['na_filter']
        )
        
        # 读取机组类型映射表
        type_mapping = pd.read_excel(
            input_file,
            sheet_name='备注',
            usecols='A:B',
            names=['A', 'B'],  # 保持原始列名
            skiprows=16,  # 跳过前16行，本来是17行，不知道为啥17行就会把第一个机组类型给丢掉
            engine=EXCEL_CONFIG['engine'],
            na_filter=EXCEL_CONFIG['na_filter']
        )
        
        # 处理机组名称
        processor = MachineNameProcessor(df)
        df = processor.get_result()
        
        # 处理分区
        region_processor = RegionProcessor(df, region_mapping)
        df = region_processor.get_result()
        
        # 处理分区代码
        letter_processor = RegionLetterProcessor(df, letter_mapping)
        df = letter_processor.get_result()
        
        # 处理机组类型数字表示
        type_code_processor = MachineTypeCodeProcessor(df, type_mapping)
        df = type_code_processor.get_result()
        
        # 询问是否使用每月第一天
        use_first_day = messagebox.askyesno(
            "日期处理",
            "是否将所有日期的日部分统一为01（每月第一天）？"
        )
        
        # 转换日期格式
        df[COLUMN_NAMES['start_date']] = df['投产时间'].apply(lambda x: convert_date(x, use_first_day))
        df[COLUMN_NAMES['end_date']] = df['退役时间'].apply(lambda x: convert_date(x, use_first_day))
        
        # 选择输出文件
        output_file = filedialog.askopenfilename(
            title="请选择要更新的Excel文件",
            filetypes=[("Excel files", "*.xlsx")]
        )
        
        if not output_file:
            messagebox.showinfo("提示", "未选择输出文件，程序退出")
            return
        
        # 显示正在处理输出文件的提示
        messagebox.showinfo("提示", "正在处理输出文件，请稍候...")
        
        # 使用openpyxl加载工作簿
        wb = load_workbook(output_file)
        ws = wb[EXCEL_CONFIG['output_sheet']]
        
        # 找到所有需要更新的列的列号
        col_letters = {}
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=1, column=col).value
            if cell_value == COLUMN_NAMES['machine_name']:
                col_letters['machine_name'] = col
            elif cell_value == COLUMN_NAMES['node']:
                col_letters['node'] = col
            elif cell_value == COLUMN_NAMES['power_plant']:
                col_letters['power_plant'] = col
            elif cell_value == COLUMN_NAMES['company']:
                col_letters['company'] = col
            elif cell_value == COLUMN_NAMES['start_date']:
                col_letters['start_date'] = col
            elif cell_value == COLUMN_NAMES['end_date']:
                col_letters['end_date'] = col
            elif cell_value == COLUMN_NAMES['machine_type_code']:
                col_letters['machine_type_code'] = col
            elif cell_value == COLUMN_NAMES['capacity_output']:
                col_letters['capacity'] = col
        
        # 检查所有必需的列是否存在
        required_columns = ['machine_name', 'node', 'power_plant', 'company', 'start_date', 'end_date', 'machine_type_code', 'capacity']
        for col in required_columns:
            if col not in col_letters:
                logger.error(f"未找到列: {COLUMN_NAMES[col if col != 'capacity' else 'capacity_output']}")
                return
        
        # 更新所有列的值
        for i, row in df.iterrows():
            row_num = i + 2  # Excel行号从2开始
            ws.cell(row=row_num, column=col_letters['machine_name'], value=row[COLUMN_NAMES['machine_name']])
            ws.cell(row=row_num, column=col_letters['node'], value=row[COLUMN_NAMES['node']])
            ws.cell(row=row_num, column=col_letters['power_plant'], value=row[COLUMN_NAMES['project_name']])
            ws.cell(row=row_num, column=col_letters['company'], value=row[COLUMN_NAMES['machine_type']])
            ws.cell(row=row_num, column=col_letters['start_date'], value=row[COLUMN_NAMES['start_date']])
            ws.cell(row=row_num, column=col_letters['end_date'], value=row[COLUMN_NAMES['end_date']])
            ws.cell(row=row_num, column=col_letters['machine_type_code'], value=row[COLUMN_NAMES['machine_type_code']])
            ws.cell(row=row_num, column=col_letters['capacity'], value=row[COLUMN_NAMES['capacity']])
        
        # 保存工作簿
        wb.save(output_file)
        messagebox.showinfo("成功", "处理完成！")
        
    except Exception as e:
        error_msg = f"处理过程中出现错误：\n{str(e)}"
        messagebox.showerror("错误", error_msg)
        logger.error(error_msg)

if __name__ == "__main__":
    process_excel() 