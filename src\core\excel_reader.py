"""
Excel文件读取模块
"""
import pandas as pd
import os
import time
from ..utils.config import EXCEL_CONFIG, DTYPE_CONFIG, REQUIRED_COLUMNS
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class ExcelReader:
    """Excel文件读取类"""
    
    def __init__(self, file_path):
        """
        初始化Excel读取器
        
        Args:
            file_path (str): Excel文件路径
        """
        self.file_path = file_path
        self.df = None
        
    def read(self):
        """
        读取Excel文件
        
        Returns:
            pandas.DataFrame: 读取的数据框
        """
        try:
            logger.info(f"开始读取文件: {self.file_path}")
            logger.info(f"文件大小: {round(os.path.getsize(self.file_path) / (1024*1024), 2)} MB")
            
            start_time = time.time()
            logger.info(f"开始读取时间: {time.strftime('%H:%M:%S')}")
            
            # 读取Excel文件
            self.df = pd.read_excel(
                self.file_path,
                sheet_name=EXCEL_CONFIG['input_sheet'],
                engine=EXCEL_CONFIG['engine'],
                dtype=DTYPE_CONFIG,
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            end_time = time.time()
            logger.info(f"读取完成时间: {time.strftime('%H:%M:%S')}")
            logger.info(f"读取耗时: {end_time - start_time:.2f} 秒")
            
            # 显示列名
            logger.info("\n文件列名:")
            for col in self.df.columns:
                logger.info(f"- {col}")
            
            # 检查必要的列是否存在
            self._check_required_columns()
            
            return self.df
            
        except Exception as e:
            logger.error(f"读取Excel文件时出错: {str(e)}")
            raise
    
    def _check_required_columns(self):
        """
        检查必要的列是否存在
        """
        missing_columns = [col for col in REQUIRED_COLUMNS if col not in self.df.columns]
        if missing_columns:
            error_msg = f"Excel文件缺少以下必要的列：\n{', '.join(missing_columns)}"
            logger.error(error_msg)
            raise ValueError(error_msg) 