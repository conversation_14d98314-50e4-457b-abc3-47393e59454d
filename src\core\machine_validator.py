"""
机组验证器模块 - 专门处理机组指定出力和机组指定状态
"""
import pandas as pd
from openpyxl import load_workbook
import tkinter as tk
from tkinter import messagebox
from ..utils.logger import setup_logger
from ..utils.config import EXCEL_CONFIG, COLUMN_NAMES

logger = setup_logger(__name__)

class MachineValidator:
    """机组验证器类 - 专门处理机组指定出力和机组指定状态"""
    
    def __init__(self, file_path: str):
        """
        初始化验证器
        
        Args:
            file_path (str): Excel文件路径
        """
        self.file_path = file_path
        try:
            # 尝试加载工作簿，添加超时和错误处理
            self.wb = load_workbook(file_path, read_only=False, keep_vba=False)
            logger.info(f"成功加载Excel文件: {file_path}")
        except Exception as e:
            logger.error(f"加载Excel文件失败: {str(e)}")
            raise Exception(f"无法打开Excel文件 {file_path}: {str(e)}")
            
        self.df_machines = None  # 机组sheet的数据
        self.df_status = None    # 机组指定状态sheet的数据
        self.df_output = None    # 机组指定出力sheet的数据
        
    def _check_file_access(self):
        """
        检查文件是否可以写入（没有被其他程序锁定）
        
        Returns:
            bool: 文件是否可以写入
        """
        try:
            # 尝试以写入模式打开文件
            with open(self.file_path, 'r+b'):
                pass
            return True
        except (PermissionError, IOError) as e:
            logger.error(f"文件被锁定或无法访问: {str(e)}")
            messagebox.showerror(
                "文件访问错误", 
                f"文件可能被Excel或其他程序打开，请关闭后重试。\n错误信息: {str(e)}"
            )
            return False
    
    def load_data(self):
        """加载所有需要的数据"""
        try:
            # 加载机组sheet数据
            self.df_machines = pd.read_excel(
                self.file_path,
                sheet_name=EXCEL_CONFIG['output_sheet'],
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载机组指定状态sheet数据
            self.df_status = pd.read_excel(
                self.file_path,
                sheet_name='机组指定状态',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载机组指定出力sheet数据
            self.df_output = pd.read_excel(
                self.file_path,
                sheet_name='机组指定出力',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            logger.info("机组相关数据加载完成")
            return True
            
        except Exception as e:
            logger.error(f"加载数据时出错: {str(e)}")
            messagebox.showerror("错误", f"加载数据时出错: {str(e)}")
            return False
    
    def _clear_invalid_rows(self, sheet_name, df_target, invalid_names, data_type="机组"):
        """
        通用的清空无效行方法
        
        Args:
            sheet_name (str): Excel sheet名称
            df_target (pd.DataFrame): 目标DataFrame
            invalid_names (set): 无效的机组名称集合
            data_type (str): 数据类型描述
            
        Returns:
            bool: 是否成功
        """
        try:
            # 显示进度提示
            progress_window = tk.Toplevel()
            progress_window.title("处理中...")
            progress_window.geometry("350x120")
            progress_window.resizable(False, False)
            
            # 居中显示
            progress_window.transient()
            progress_window.grab_set()
            
            progress_label = tk.Label(
                progress_window, 
                text=f"正在删除无效{data_type}，请稍候...",
                font=("Arial", 10)
            )
            progress_label.pack(pady=20)
            
            progress_bar_label = tk.Label(progress_window, text="", font=("Arial", 9))
            progress_bar_label.pack(pady=5)
            
            progress_window.update()
            
            # 找到要清空的行
            rows_to_clear = df_target[df_target.iloc[:, 0].isin(invalid_names)].index.tolist()
            
            if not rows_to_clear:
                progress_window.destroy()
                logger.warning(f"未找到要清空的{data_type}记录")
                return False
            
            logger.info(f"准备清空{len(rows_to_clear)}行{data_type}记录")
            
            # 清空要删除的行的内容（保留行）
            ws = self.wb[sheet_name]
            total_rows = len(rows_to_clear)
            
            for i, row in enumerate(rows_to_clear):
                # 更新进度
                progress_label.config(text=f"正在处理第{i+1}/{total_rows}行...")
                progress_bar_label.config(text=f"进度: {'█' * (i * 20 // total_rows)}{'░' * (20 - i * 20 // total_rows)} {i*100//total_rows}%")
                progress_window.update()
                
                row_num = row + 2  # Excel行号从2开始
                for col in range(1, ws.max_column + 1):
                    ws.cell(row=row_num, column=col).value = None
            
            # 更新进度为保存文件
            progress_label.config(text="正在保存文件...")
            progress_bar_label.config(text="保存中...")
            progress_window.update()
            
            # 检查文件访问权限
            if not self._check_file_access():
                progress_window.destroy()
                return False
            
            # 保存文件
            self.wb.save(self.file_path)
            
            # 重新加载数据以确保数据一致性
            progress_label.config(text="正在重新加载数据...")
            progress_bar_label.config(text="加载中...")
            progress_window.update()
            
            # 重新加载对应的数据
            if sheet_name == '机组指定状态':
                self.df_status = pd.read_excel(
                    self.file_path,
                    sheet_name='机组指定状态',
                    engine=EXCEL_CONFIG['engine'],
                    na_filter=EXCEL_CONFIG['na_filter']
                )
            elif sheet_name == '机组指定出力':
                self.df_output = pd.read_excel(
                    self.file_path,
                    sheet_name='机组指定出力',
                    engine=EXCEL_CONFIG['engine'],
                    na_filter=EXCEL_CONFIG['na_filter']
                )
            
            progress_window.destroy()
            logger.info(f"已清空{len(rows_to_clear)}行无效的{data_type}记录")
            return True
            
        except Exception as save_error:
            if 'progress_window' in locals():
                progress_window.destroy()
            logger.error(f"处理{data_type}时出错: {str(save_error)}")
            messagebox.showerror("错误", f"处理{data_type}时出错: {str(save_error)}")
            return False
    
    def check_machine_status(self):
        """
        检查机组指定状态sheet中的首列的内容是否都在机组sheet中有出现
        如果没有，询问用户是否删除
        """
        try:
            if self.df_machines is None or self.df_status is None:
                logger.error("请先加载数据")
                return False

            machine_name_col = COLUMN_NAMES['machine_name']
            
            # 获取机组sheet中的所有机组名称
            all_machines = set(self.df_machines[machine_name_col])
            
            # 获取机组指定状态sheet中首列的所有机组名称
            status_machines = set(self.df_status.iloc[:, 0])

            # 找出在机组指定状态sheet中存在但在机组sheet中不存在的机组名称
            invalid_names = status_machines - all_machines

            if not invalid_names:
                logger.info("机组指定状态sheet中的所有机组都在机组sheet中存在。")
                return True

            logger.warning(f"机组指定状态sheet中存在以下机组在机组sheet中未找到：{invalid_names}")
            
            # 使用messagebox询问用户
            response = messagebox.askyesno(
                "确认删除",
                f"发现{len(invalid_names)}个无效机组状态记录，是否删除？\n\n"
                f"无效机组包括：{', '.join(list(invalid_names)[:5])}{'...' if len(invalid_names) > 5 else ''}"
            )
            
            if response:
                return self._clear_invalid_rows('机组指定状态', self.df_status, invalid_names, "机组状态")
            else:
                logger.info("用户选择不删除无效机组状态。")
                return True

        except Exception as e:
            logger.error(f"检查机组指定状态时出错: {str(e)}")
            messagebox.showerror("错误", f"检查机组指定状态时出错: {str(e)}")
            return False
    
    def check_machine_output(self):
        """
        检查机组指定出力sheet中的首列的内容是否都在机组sheet中有出现
        如果没有，询问用户是否删除
        """
        try:
            if self.df_machines is None or self.df_output is None:
                logger.error("请先加载数据")
                return False

            machine_name_col = COLUMN_NAMES['machine_name']
            
            # 获取机组sheet中的所有机组名称
            all_machines = set(self.df_machines[machine_name_col])
            
            # 获取机组指定出力sheet中首列的所有机组名称
            output_machines = set(self.df_output.iloc[:, 0])

            # 找出在机组指定出力sheet中存在但在机组sheet中不存在的机组名称
            invalid_names = output_machines - all_machines

            if not invalid_names:
                logger.info("机组指定出力sheet中的所有机组都在机组sheet中存在。")
                return True

            logger.warning(f"机组指定出力sheet中存在以下机组在机组sheet中未找到：{invalid_names}")
            
            # 使用messagebox询问用户
            response = messagebox.askyesno(
                "确认删除",
                f"发现{len(invalid_names)}个无效机组出力记录，是否删除？\n\n"
                f"无效机组包括：{', '.join(list(invalid_names)[:5])}{'...' if len(invalid_names) > 5 else ''}"
            )
            
            if response:
                return self._clear_invalid_rows('机组指定出力', self.df_output, invalid_names, "机组出力")
            else:
                logger.info("用户选择不删除无效机组出力。")
                return True

        except Exception as e:
            logger.error(f"检查机组指定出力时出错: {str(e)}")
            messagebox.showerror("错误", f"检查机组指定出力时出错: {str(e)}")
            return False
