"""
处理器基类模块
"""
from abc import ABC, abstractmethod
import pandas as pd
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class BaseProcessor(ABC):
    """处理器基类"""
    
    def __init__(self, df: pd.DataFrame):
        """
        初始化处理器
        
        Args:
            df (pandas.DataFrame): 要处理的数据框
        """
        self.df = df.copy()
    
    @abstractmethod
    def process(self) -> pd.DataFrame:
        """
        处理数据
        
        Returns:
            pandas.DataFrame: 处理后的数据框
        """
        pass
    
    def validate(self) -> bool:
        """
        验证数据
        
        Returns:
            bool: 验证是否通过
        """
        return True
    
    def get_result(self) -> pd.DataFrame:
        """
        获取处理结果
        
        Returns:
            pandas.DataFrame: 处理后的数据框
        """
        if self.validate():
            return self.process()
        return self.df 