"""
机组名称处理器模块
"""
import pandas as pd
from .base_processor import BaseProcessor
from ..utils.config import COLUMN_NAMES
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class MachineNameProcessor(BaseProcessor):
    """机组名称处理器"""
    
    def process(self) -> pd.DataFrame:
        """
        处理机组名称
        
        Returns:
            pandas.DataFrame: 处理后的数据框
        """
        try:
            logger.info("开始处理机组名称...")
            
            # 组合机组名称
            self.df[COLUMN_NAMES['machine_name']] = (
                self.df[COLUMN_NAMES['machine_type']].astype(str) + '#' +
                self.df[COLUMN_NAMES['project_name']].astype(str) + '#' +
                self.df[COLUMN_NAMES['machine_number']].astype(str)
            )
            
            # 检查重复项
            duplicates = self.df[self.df[COLUMN_NAMES['machine_name']].duplicated(keep=False)]
            if not duplicates.empty:
                logger.warning("发现重复的机组名称：")
                for name in duplicates[COLUMN_NAMES['machine_name']].unique():
                    logger.warning(f"- {name}")
            
            logger.info("机组名称处理完成")
            return self.df
            
        except Exception as e:
            logger.error(f"处理机组名称时出错: {str(e)}")
            raise
    
    def validate(self) -> bool:
        """
        验证数据
        
        Returns:
            bool: 验证是否通过
        """
        required_columns = [
            COLUMN_NAMES['machine_type'],
            COLUMN_NAMES['project_name'],
            COLUMN_NAMES['machine_number']
        ]
        
        for col in required_columns:
            if col not in self.df.columns:
                logger.error(f"缺少必要的列: {col}")
                return False
        
        return True 