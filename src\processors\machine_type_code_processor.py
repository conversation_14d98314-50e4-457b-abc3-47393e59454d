"""
机组类型数字表示处理器模块
"""
import pandas as pd
from .base_processor import BaseProcessor
from ..utils.config import COLUMN_NAMES
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class MachineTypeCodeProcessor(BaseProcessor):
    """机组类型数字表示处理器"""
    
    def __init__(self, df: pd.DataFrame, type_mapping: pd.DataFrame):
        """
        初始化处理器
        
        Args:
            df (pandas.DataFrame): 要处理的数据框
            type_mapping (pandas.DataFrame): 机组类型映射表（备注sheet的数据）
        """
        super().__init__(df)
        self.type_mapping = type_mapping
    
    def process(self) -> pd.DataFrame:
        """
        处理机组类型数字表示
        
        Returns:
            pandas.DataFrame: 处理后的数据框
        """
        try:
            logger.info("开始处理机组类型数字表示...")
            
            # 使用merge进行VLOOKUP操作
            self.df = pd.merge(
                self.df,
                self.type_mapping,
                left_on=COLUMN_NAMES['machine_type'],
                right_on='A',  # 备注sheet的A列
                how='left'
            )
            
            # 重命名B列为机组类型数字表示列
            self.df[COLUMN_NAMES['machine_type_code']] = self.df['B']
            
            # 删除临时列
            self.df = self.df.drop(['A', 'B'], axis=1)
            
            # 检查未匹配的记录
            unmatched = self.df[self.df[COLUMN_NAMES['machine_type_code']].isna()]
            if not unmatched.empty:
                logger.warning("发现未匹配到机组类型数字表示的记录：")
                for _, row in unmatched.iterrows():
                    logger.warning(f"- 机组类型: {row[COLUMN_NAMES['machine_type']]}")
            
            logger.info("机组类型数字表示处理完成")
            return self.df
            
        except Exception as e:
            logger.error(f"处理机组类型数字表示时出错: {str(e)}")
            raise
    
    def validate(self) -> bool:
        """
        验证数据
        
        Returns:
            bool: 验证是否通过
        """
        if COLUMN_NAMES['machine_type'] not in self.df.columns:
            logger.error(f"缺少必要的列: {COLUMN_NAMES['machine_type']}")
            return False
            
        if 'A' not in self.type_mapping.columns or 'B' not in self.type_mapping.columns:
            logger.error("机组类型映射表格式不正确")
            return False
            
        return True 