"""
分区代码处理器模块
"""
import pandas as pd
from .base_processor import BaseProcessor
from ..utils.config import COLUMN_NAMES
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class RegionLetterProcessor(BaseProcessor):
    """分区代码处理器"""
    
    def __init__(self, df: pd.DataFrame, letter_mapping: pd.DataFrame):
        """
        初始化处理器
        
        Args:
            df (pandas.DataFrame): 要处理的数据框
            letter_mapping (pandas.DataFrame): 字母映射表（备注sheet的数据）
        """
        super().__init__(df)
        self.letter_mapping = letter_mapping
    
    def process(self) -> pd.DataFrame:
        """
        处理分区代码
        
        Returns:
            pandas.DataFrame: 处理后的数据框
        """
        try:
            logger.info("开始处理分区代码...")
            
            # 使用merge进行VLOOKUP操作
            self.df = pd.merge(
                self.df,
                self.letter_mapping,
                left_on=COLUMN_NAMES['region'],
                right_on='E',  # 备注sheet的E列
                how='left'
            )
            
            # 重命名F列为节点列
            self.df[COLUMN_NAMES['node']] = self.df['F']
            
            # 删除临时列
            self.df = self.df.drop(['E', 'F'], axis=1)
            
            # 检查未匹配的记录
            unmatched = self.df[self.df[COLUMN_NAMES['node']].isna()]
            if not unmatched.empty:
                logger.warning("发现未匹配到分区代码的记录：")
                for _, row in unmatched.iterrows():
                    logger.warning(f"- 分区: {row[COLUMN_NAMES['region']]}")
            
            logger.info("分区代码处理完成")
            return self.df
            
        except Exception as e:
            logger.error(f"处理分区代码时出错: {str(e)}")
            raise
    
    def validate(self) -> bool:
        """
        验证数据
        
        Returns:
            bool: 验证是否通过
        """
        if COLUMN_NAMES['region'] not in self.df.columns:
            logger.error(f"缺少必要的列: {COLUMN_NAMES['region']}")
            return False
            
        if 'E' not in self.letter_mapping.columns or 'F' not in self.letter_mapping.columns:
            logger.error("字母映射表格式不正确")
            return False
            
        return True 