"""
分区处理器模块
"""
import pandas as pd
from .base_processor import BaseProcessor
from ..utils.config import COLUMN_NAMES
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class RegionProcessor(BaseProcessor):
    """分区处理器"""
    
    def __init__(self, df: pd.DataFrame, region_mapping: pd.DataFrame):
        """
        初始化处理器
        
        Args:
            df (pandas.DataFrame): 要处理的数据框
            region_mapping (pandas.DataFrame): 分区映射表（分区sheet的数据）
        """
        super().__init__(df)
        self.region_mapping = region_mapping
    
    def process(self) -> pd.DataFrame:
        """
        处理分区
        
        Returns:
            pandas.DataFrame: 处理后的数据框
        """
        try:
            logger.info("开始处理分区...")
            
            # 使用merge进行VLOOKUP操作
            self.df = pd.merge(
                self.df,
                self.region_mapping,
                left_on=COLUMN_NAMES['county'],
                right_on='B',  # 分区sheet的B列
                how='left'
            )
            
            # 重命名C列为分区列
            self.df[COLUMN_NAMES['region']] = self.df['C']
            
            # 删除临时列
            self.df = self.df.drop(['B', 'C'], axis=1)
            
            # 检查未匹配的记录
            unmatched = self.df[self.df[COLUMN_NAMES['region']].isna()]
            if not unmatched.empty:
                logger.warning("发现未匹配到分区的记录：")
                for _, row in unmatched.iterrows():
                    logger.warning(f"- 送入县区: {row[COLUMN_NAMES['county']]}")
            
            logger.info("分区处理完成")
            return self.df
            
        except Exception as e:
            logger.error(f"处理分区时出错: {str(e)}")
            raise
    
    def validate(self) -> bool:
        """
        验证数据
        
        Returns:
            bool: 验证是否通过
        """
        if COLUMN_NAMES['county'] not in self.df.columns:
            logger.error(f"缺少必要的列: {COLUMN_NAMES['county']}")
            return False
            
        if 'B' not in self.region_mapping.columns or 'C' not in self.region_mapping.columns:
            logger.error("分区映射表格式不正确")
            return False
            
        return True 