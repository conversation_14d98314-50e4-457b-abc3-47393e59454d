"""
配置文件，包含所有常量和配置项
"""

# Excel文件相关配置
EXCEL_CONFIG = {
    'input_sheet': 'Sheet1',
    'output_sheet': '机组',
    'engine': 'openpyxl',
    'na_filter': False
}

# 列名配置
COLUMN_NAMES = {
    'machine_type': '机组类型',
    'project_name': '项目名称',
    'machine_number': '机组序号',
    'machine_name': '机组名称',
    'node': '节点',
    'power_plant': '[2]电厂',
    'company': '[3]所属发电公司',
    'start_date': '[4]实际投产日（按“年月”形式输入）',
    'end_date': '[5]退役时间（按“年月”形式输入）',
    'machine_type_code': '[6]机组类型(1-常规火电;2-可启停火电;3-供热燃机;4-核电;5-水电;6-抽蓄;7-区外;8-热电;9-调峰燃机;10-燃油机组;11-风电；12-光伏；13-光热；14-储能；20-110kV统调机组；21-非统调机组)',
    'capacity': '机组容量',
    'capacity_output': '[7]机组容量(MW)',
    'county': '送入县区',
    'region': '分区'
}

# 数据类型配置
DTYPE_CONFIG = {
    '机组类型': str,
    '项目名称': str,
    '机组序号': str,
    '机组名称': str,
    '节点': str,
    '[2]电厂': str,
    '[3]所属发电公司': str,
    '[4]实际投产日（按“年月”形式输入）': str,
    '[5]退役时间（按“年月”形式输入）': str,
    '[6]机组类型(1-常规火电;2-可启停火电;3-供热燃机;4-核电;5-水电;6-抽蓄;7-区外;8-热电;9-调峰燃机;10-燃油机组;11-风电；12-光伏；13-光热；14-储能；20-110kV统调机组；21-非统调机组)': str,
    '机组容量': float,
    '送入县区': str,
    '分区': str
}

# 必需列配置
REQUIRED_COLUMNS = [
    '机组类型',
    '项目名称',
    '机组序号',
    '送入县区'
]

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}