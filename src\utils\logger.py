"""
日志工具模块
"""
import logging
from .config import LOG_CONFIG

def setup_logger(name):
    """
    设置日志记录器
    
    Args:
        name (str): 日志记录器名称
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(LOG_CONFIG['level'])
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(LOG_CONFIG['level'])
    
    # 创建格式化器
    formatter = logging.Formatter(LOG_CONFIG['format'])
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    
    return logger 