# 标准库配置系统

## 项目概述

标准库配置系统是一个用于配置和管理电力规划软件（如HUST、GOPT等）输入文件格式的工具。用户可以通过Web界面配置各种映射关系，然后一键生成符合不同软件要求的Excel文件。

## 系统架构

### 后端 (Python Flask)
- `api/` - RESTful API接口
- `core/` - 核心业务逻辑
- `models/` - 数据模型
- `converters/` - 不同软件的转换器
- `config/` - 配置管理

### 前端 (Vue2)
- `frontend/` - Vue2前端应用
- 配置界面
- 预览功能
- 导出功能

### 标准库存储
- `database/` - 配置数据库
- `templates/` - 模板文件

## 主要功能

1. **标准库配置**
   - 字段映射配置
   - 数据转换规则
   - 验证规则设置

2. **软件支持**
   - HUST格式转换
   - GOPT格式转换
   - 其他规划软件格式

3. **用户界面**
   - 拖拽式配置
   - 实时预览
   - 批量处理

## 技术栈

- **后端**: Python Flask + SQLAlchemy
- **前端**: Vue2 + Element UI
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **Excel处理**: pandas + openpyxl

## 开发计划

1. 第一阶段：核心架构和基础API
2. 第二阶段：Vue2前端界面
3. 第三阶段：HUST转换器
4. 第四阶段：GOPT转换器
5. 第五阶段：测试和优化 