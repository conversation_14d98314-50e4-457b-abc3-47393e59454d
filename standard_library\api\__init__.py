"""
标准库配置系统 API
提供RESTful接口用于配置管理和Excel转换
"""

from flask import Flask
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

# 创建Flask应用
app = Flask(__name__)

# 配置
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///standard_library.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = 'your-secret-key-here'

# 初始化扩展
db = SQLAlchemy(app)
migrate = Migrate(app, db)

# 允许跨域请求
CORS(app)

# 导入路由
from .routes import config_routes, converter_routes

# 注册蓝图
app.register_blueprint(config_routes.bp, url_prefix='/api/config')
app.register_blueprint(converter_routes.bp, url_prefix='/api/converter')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000) 