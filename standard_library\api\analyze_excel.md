 # API接口文档：/api/analyze_excel

## 简介
用于上传Excel文件，自动解析所有sheet，返回每个sheet的完整数据（含表头、所有行、所有列），便于前端进行Excel结构可视化。

---

## 接口信息
- **接口地址**：`/api/analyze_excel`
- **请求方式**：POST
- **请求类型**：`multipart/form-data`
- **鉴权**：无

---

## 请求参数
| 参数名 | 类型   | 必填 | 说明                 |
| ------ | ------ | ---- | -------------------- |
| file   | file   | 是   | 需上传的Excel文件    |

---

## 请求示例

```bash
curl -X POST http://localhost:5001/api/analyze_excel \
  -F "file=@/path/to/your/file.xlsx"
```

---

## 返回参数

### 成功返回

```json
{
  "code": 0,
  "sheets": [
    {
      "name": "Sheet1",
      "data": [
        ["列1", "列2", "列3"],
        ["A", "B", "C"],
        ["D", "E", "F"]
      ]
    },
    {
      "name": "Sheet2",
      "data": [
        ["字段A", "字段B"],
        [1, 2],
        [3, 4]
      ]
    }
  ]
}
```

#### 字段说明
| 字段   | 类型     | 说明                         |
| ------ | -------- | ---------------------------- |
| code   | int      | 0表示成功                    |
| sheets | array    | 所有sheet的列表              |
| name   | string   | sheet名称                    |
| data   | 2D array | sheet的所有数据（含表头）    |

---

### 失败返回

```json
{
  "code": 1001,
  "msg": "未上传文件"
}
```

#### 错误码说明
| code  | 含义                       | 说明                         |
| ----- | -------------------------- | ---------------------------- |
| 0     | 成功                       |                              |
| 1001  | 未上传文件                 | file参数缺失                 |
| 1002  | 文件格式不支持/解析失败     | 不是Excel或内容损坏          |
| 500   | 服务器内部错误             | 其它异常                     |

---

## 备注
- 支持`.xls`和`.xlsx`格式。
- 返回的所有数据均为字符串，便于前端直接渲染。
- 若Excel内容较大，建议前端分页或懒加载展示。
