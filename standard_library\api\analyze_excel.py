from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import io

app = Flask(__name__)
CORS(app)

@app.route('/api/analyze_excel', methods=['POST'])
def analyze_excel():
    if 'file' not in request.files:
        return jsonify({"code": 1001, "msg": "未上传文件"}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({"code": 1001, "msg": "未上传文件"}), 400
    try:
        in_memory = io.BytesIO(file.read())
        # 读取所有sheet
        xls = pd.ExcelFile(in_memory)
        sheets = []
        for sheet_name in xls.sheet_names:
            df = xls.parse(sheet_name, dtype=str)  # 全部转为字符串，便于前端展示
            df = df.fillna('')  # 将所有NaN值替换为空字符串
            data = [df.columns.tolist()] + df.values.tolist()
            sheets.append({
                "name": sheet_name,
                "data": data
            })
        return jsonify({"code": 0, "sheets": sheets})
    except Exception as e:
        return jsonify({"code": 1002, "msg": f"文件格式不支持或解析失败: {str(e)}"}), 400

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
