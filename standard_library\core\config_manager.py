#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
管理标准库的配置信息，提供配置的增删改查功能
"""

import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from ..models.standard_config import StandardConfig

logger = logging.getLogger(__name__)

class ConfigManager:
    """
    配置管理器
    负责标准库配置的存储、加载和管理
    """
    
    def __init__(self, config_dir: str = "configs"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件存储目录
        """
        self.config_dir = config_dir
        self.configs: Dict[str, StandardConfig] = {}
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 加载现有配置
        self._load_configs()
        
    def _load_configs(self) -> None:
        """加载所有配置文件"""
        if not os.path.exists(self.config_dir):
            return
            
        for filename in os.listdir(self.config_dir):
            if filename.endswith('.json'):
                config_path = os.path.join(self.config_dir, filename)
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        config = StandardConfig.from_dict(config_data)
                        self.configs[config.config_id] = config
                        logger.info(f"加载配置: {config.name}")
                except Exception as e:
                    logger.error(f"加载配置文件 {filename} 失败: {str(e)}")
                    
    def _save_config(self, config: StandardConfig) -> None:
        """保存配置到文件"""
        config_path = os.path.join(self.config_dir, f"{config.config_id}.json")
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, ensure_ascii=False, indent=2)
            logger.info(f"保存配置: {config.name}")
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            raise
            
    def create_config(self, config_id: str, name: str, description: str = "") -> StandardConfig:
        """
        创建新配置
        
        Args:
            config_id: 配置ID
            name: 配置名称
            description: 配置描述
            
        Returns:
            创建的配置对象
        """
        if config_id in self.configs:
            raise ValueError(f"配置ID {config_id} 已存在")
            
        config = StandardConfig(config_id, name, description)
        self.configs[config_id] = config
        self._save_config(config)
        
        return config
        
    def get_config(self, config_id: str) -> Optional[StandardConfig]:
        """
        获取配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            配置对象，如果不存在返回None
        """
        return self.configs.get(config_id)
        
    def list_configs(self) -> List[Dict[str, Any]]:
        """
        列出所有配置
        
        Returns:
            配置列表
        """
        return [
            {
                'config_id': config.config_id,
                'name': config.name,
                'description': config.description,
                'created_at': config.created_at.isoformat(),
                'updated_at': config.updated_at.isoformat(),
                'is_active': config.is_active
            }
            for config in self.configs.values()
        ]
        
    def update_config(self, config_id: str, **kwargs) -> StandardConfig:
        """
        更新配置
        
        Args:
            config_id: 配置ID
            **kwargs: 要更新的字段
            
        Returns:
            更新后的配置对象
        """
        config = self.get_config(config_id)
        if not config:
            raise ValueError(f"配置 {config_id} 不存在")
            
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
                
        config.updated_at = datetime.now()
        self._save_config(config)
        
        return config
        
    def delete_config(self, config_id: str) -> bool:
        """
        删除配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            是否删除成功
        """
        if config_id not in self.configs:
            return False
            
        # 删除文件
        config_path = os.path.join(self.config_dir, f"{config_id}.json")
        if os.path.exists(config_path):
            os.remove(config_path)
            
        # 从内存中删除
        del self.configs[config_id]
        
        logger.info(f"删除配置: {config_id}")
        return True
        
    def add_field_mapping(self, config_id: str, source_field: str, target_field: str,
                         mapping_type: str = "direct", transformation: Optional[str] = None) -> None:
        """
        添加字段映射
        
        Args:
            config_id: 配置ID
            source_field: 源字段
            target_field: 目标字段
            mapping_type: 映射类型
            transformation: 转换规则
        """
        config = self.get_config(config_id)
        if not config:
            raise ValueError(f"配置 {config_id} 不存在")
            
        config.add_field_mapping(source_field, target_field, mapping_type, transformation)
        self._save_config(config)
        
    def add_software_template(self, config_id: str, software_name: str, 
                            template_config: Dict[str, Any]) -> None:
        """
        添加软件模板
        
        Args:
            config_id: 配置ID
            software_name: 软件名称
            template_config: 模板配置
        """
        config = self.get_config(config_id)
        if not config:
            raise ValueError(f"配置 {config_id} 不存在")
            
        config.add_software_template(software_name, template_config)
        self._save_config(config)
        
    def export_config(self, config_id: str) -> Dict[str, Any]:
        """
        导出配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            配置的完整数据
        """
        config = self.get_config(config_id)
        if not config:
            raise ValueError(f"配置 {config_id} 不存在")
            
        return config.to_dict()
        
    def import_config(self, config_data: Dict[str, Any]) -> StandardConfig:
        """
        导入配置
        
        Args:
            config_data: 配置数据
            
        Returns:
            导入的配置对象
        """
        config = StandardConfig.from_dict(config_data)
        
        # 如果配置ID已存在，生成新的ID
        if config.config_id in self.configs:
            import uuid
            config.config_id = f"{config.config_id}_{uuid.uuid4().hex[:8]}"
            
        self.configs[config.config_id] = config
        self._save_config(config)
        
        return config 