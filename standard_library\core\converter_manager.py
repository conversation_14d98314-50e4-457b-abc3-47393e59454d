#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换器管理器
管理不同软件的Excel转换
"""

import pandas as pd
import os
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from .config_manager import ConfigManager
from ..models.standard_config import StandardConfig

logger = logging.getLogger(__name__)

class ConverterManager:
    """
    转换器管理器
    负责将标准配置转换为不同软件格式的Excel文件
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化转换器管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.supported_software = ['HUST', 'GOPT', 'PSASP', 'BPA']
        
    def convert_to_software_format(self, config_id: str, software_name: str, 
                                 source_data: pd.DataFrame, 
                                 output_path: Optional[str] = None) -> str:
        """
        将数据转换为指定软件格式
        
        Args:
            config_id: 配置ID
            software_name: 目标软件名称
            source_data: 源数据
            output_path: 输出文件路径
            
        Returns:
            输出文件路径
        """
        config = self.config_manager.get_config(config_id)
        if not config:
            raise ValueError(f"配置 {config_id} 不存在")
            
        if software_name not in self.supported_software:
            raise ValueError(f"不支持的软件: {software_name}")
            
        # 获取软件模板
        if software_name not in config.software_templates:
            raise ValueError(f"配置 {config_id} 中没有 {software_name} 的模板")
            
        template = config.software_templates[software_name]
        
        # 应用字段映射
        mapped_data = self._apply_field_mappings(source_data, config.field_mappings)
        
        # 应用转换规则
        converted_data = self._apply_conversion_rules(mapped_data, config.conversion_rules)
        
        # 生成输出文件
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"output/{software_name}_{config_id}_{timestamp}.xlsx"
            
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 根据软件类型生成不同的Excel格式
        if software_name == 'HUST':
            self._generate_hust_format(converted_data, template, output_path)
        elif software_name == 'GOPT':
            self._generate_gopt_format(converted_data, template, output_path)
        else:
            # 默认格式
            converted_data.to_excel(output_path, index=False)
            
        logger.info(f"成功生成 {software_name} 格式文件: {output_path}")
        return output_path
        
    def _apply_field_mappings(self, data: pd.DataFrame, 
                            field_mappings: Dict[str, Any]) -> pd.DataFrame:
        """
        应用字段映射
        
        Args:
            data: 源数据
            field_mappings: 字段映射配置
            
        Returns:
            映射后的数据
        """
        result_data = pd.DataFrame()
        
        for source_field, mapping in field_mappings.items():
            target_field = mapping['target_field']
            mapping_type = mapping['mapping_type']
            transformation = mapping.get('transformation')
            
            if source_field in data.columns:
                if mapping_type == 'direct':
                    result_data[target_field] = data[source_field]
                elif mapping_type == 'transform' and transformation:
                    # 这里可以实现更复杂的转换逻辑
                    result_data[target_field] = data[source_field]
                else:
                    result_data[target_field] = data[source_field]
            else:
                logger.warning(f"源字段 {source_field} 不存在于数据中")
                
        return result_data
        
    def _apply_conversion_rules(self, data: pd.DataFrame, 
                              conversion_rules: Dict[str, Any]) -> pd.DataFrame:
        """
        应用转换规则
        
        Args:
            data: 数据
            conversion_rules: 转换规则
            
        Returns:
            转换后的数据
        """
        result_data = data.copy()
        
        for rule_name, rule in conversion_rules.items():
            rule_type = rule['type']
            rule_config = rule['config']
            
            if rule_type == 'data_type_conversion':
                self._apply_data_type_conversion(result_data, rule_config)
            elif rule_type == 'value_mapping':
                self._apply_value_mapping(result_data, rule_config)
            elif rule_type == 'calculation':
                self._apply_calculation(result_data, rule_config)
                
        return result_data
        
    def _apply_data_type_conversion(self, data: pd.DataFrame, config: Dict[str, Any]) -> None:
        """应用数据类型转换"""
        for field, target_type in config.items():
            if field in data.columns:
                try:
                    if target_type == 'int':
                        data[field] = pd.to_numeric(data[field], errors='coerce').astype('Int64')
                    elif target_type == 'float':
                        data[field] = pd.to_numeric(data[field], errors='coerce')
                    elif target_type == 'str':
                        data[field] = data[field].astype(str)
                    elif target_type == 'datetime':
                        data[field] = pd.to_datetime(data[field], errors='coerce')
                except Exception as e:
                    logger.warning(f"转换字段 {field} 到 {target_type} 失败: {str(e)}")
                    
    def _apply_value_mapping(self, data: pd.DataFrame, config: Dict[str, Any]) -> None:
        """应用值映射"""
        for field, mapping in config.items():
            if field in data.columns:
                data[field] = data[field].map(mapping).fillna(data[field])
                
    def _apply_calculation(self, data: pd.DataFrame, config: Dict[str, Any]) -> None:
        """应用计算规则"""
        for target_field, calculation in config.items():
            try:
                # 这里可以实现更复杂的计算逻辑
                # 目前只是简单的示例
                if calculation['type'] == 'formula':
                    formula = calculation['formula']
                    # 这里需要实现公式解析和执行
                    pass
            except Exception as e:
                logger.warning(f"应用计算规则失败: {str(e)}")
                
    def _generate_hust_format(self, data: pd.DataFrame, template: Dict[str, Any], 
                            output_path: str) -> None:
        """
        生成HUST格式的Excel文件
        
        Args:
            data: 数据
            template: HUST模板配置
            output_path: 输出路径
        """
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 写入主数据表
            data.to_excel(writer, sheet_name='电源明细表', index=False)
            
            # 根据模板配置添加其他sheet
            template_config = template['config']
            if 'additional_sheets' in template_config:
                for sheet_name, sheet_config in template_config['additional_sheets'].items():
                    if 'data' in sheet_config:
                        sheet_data = pd.DataFrame(sheet_config['data'])
                        sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                        
    def _generate_gopt_format(self, data: pd.DataFrame, template: Dict[str, Any], 
                            output_path: str) -> None:
        """
        生成GOPT格式的Excel文件
        
        Args:
            data: 数据
            template: GOPT模板配置
            output_path: 输出路径
        """
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 写入主数据表
            data.to_excel(writer, sheet_name='机组数据', index=False)
            
            # 根据模板配置添加其他sheet
            template_config = template['config']
            if 'additional_sheets' in template_config:
                for sheet_name, sheet_config in template_config['additional_sheets'].items():
                    if 'data' in sheet_config:
                        sheet_data = pd.DataFrame(sheet_config['data'])
                        sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                        
    def preview_conversion(self, config_id: str, software_name: str, 
                          source_data: pd.DataFrame) -> Dict[str, Any]:
        """
        预览转换结果
        
        Args:
            config_id: 配置ID
            software_name: 软件名称
            source_data: 源数据
            
        Returns:
            预览信息
        """
        config = self.config_manager.get_config(config_id)
        if not config:
            raise ValueError(f"配置 {config_id} 不存在")
            
        # 应用映射和转换
        mapped_data = self._apply_field_mappings(source_data, config.field_mappings)
        converted_data = self._apply_conversion_rules(mapped_data, config.conversion_rules)
        
        return {
            'original_columns': list(source_data.columns),
            'mapped_columns': list(mapped_data.columns),
            'final_columns': list(converted_data.columns),
            'sample_data': converted_data.head(10).to_dict('records'),
            'field_mappings': config.field_mappings,
            'conversion_rules': config.conversion_rules
        }
        
    def validate_config(self, config_id: str, software_name: str) -> Dict[str, Any]:
        """
        验证配置的有效性
        
        Args:
            config_id: 配置ID
            software_name: 软件名称
            
        Returns:
            验证结果
        """
        config = self.config_manager.get_config(config_id)
        if not config:
            return {'valid': False, 'errors': ['配置不存在']}
            
        errors = []
        warnings = []
        
        # 检查软件模板
        if software_name not in config.software_templates:
            errors.append(f"缺少 {software_name} 软件模板")
            
        # 检查字段映射
        if not config.field_mappings:
            warnings.append("没有配置字段映射")
            
        # 检查转换规则
        if not config.conversion_rules:
            warnings.append("没有配置转换规则")
            
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        } 