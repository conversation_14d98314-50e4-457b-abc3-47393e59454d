#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel Sheet 分析器
读取除机组sheet外的所有有数据的sheet，为标准库设计做准备
"""

import pandas as pd
import os
import json
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExcelSheetAnalyzer:
    def __init__(self, excel_file_path: str):
        """
        初始化Excel Sheet分析器
        
        Args:
            excel_file_path: Excel文件路径
        """
        self.excel_file_path = excel_file_path
        self.excel_data = {}
        self.sheet_info = {}
        
    def read_all_sheets(self, exclude_sheets: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """
        读取所有sheet（排除指定的sheet）
        
        Args:
            exclude_sheets: 要排除的sheet名称列表，默认为['机组']
            
        Returns:
            包含所有sheet数据的字典
        """
        if exclude_sheets is None:
            exclude_sheets = ['机组']
            
        try:
            # 读取Excel文件的所有sheet
            excel_file = pd.ExcelFile(self.excel_file_path)
            logger.info(f"Excel文件包含的sheet: {excel_file.sheet_names}")
            
            for sheet_name in excel_file.sheet_names:
                # 跳过完全匹配的sheet名称，而不是包含关系
                if sheet_name in exclude_sheets:
                    logger.info(f"跳过sheet: {sheet_name}")
                    continue
                    
                try:
                    # 读取sheet数据
                    df = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)
                    
                    # 检查sheet是否有数据
                    if not df.empty and df.shape[0] > 0 and df.shape[1] > 0:
                        self.excel_data[sheet_name] = df
                        logger.info(f"成功读取sheet: {sheet_name}, 形状: {df.shape}")
                    else:
                        logger.warning(f"Sheet {sheet_name} 没有数据")
                        
                except Exception as e:
                    logger.error(f"读取sheet {sheet_name} 时出错: {str(e)}")
                    
        except Exception as e:
            logger.error(f"读取Excel文件时出错: {str(e)}")
            raise
            
        return self.excel_data
    
    def analyze_sheet_structure(self) -> Dict[str, Any]:
        """
        分析每个sheet的结构
        
        Returns:
            包含每个sheet结构信息的字典
        """
        for sheet_name, df in self.excel_data.items():
            sheet_info = {
                'sheet_name': sheet_name,
                'shape': df.shape,
                'columns': list(df.columns),
                'data_types': df.dtypes.to_dict(),
                'null_counts': df.isnull().sum().to_dict(),
                'sample_data': df.head(5).to_dict('records'),
                'unique_values': {},
                'column_analysis': {}
            }
            
            # 分析每列的唯一值数量
            for col in df.columns:
                if df[col].dtype == 'object':
                    unique_count = df[col].nunique()
                    sample_values = df[col].dropna().unique()[:10].tolist()
                    sheet_info['unique_values'][col] = {
                        'count': unique_count,
                        'sample_values': sample_values
                    }
                    
                    # 分析列的内容特征
                    sheet_info['column_analysis'][col] = {
                        'is_categorical': unique_count < min(50, len(df) * 0.1),
                        'has_chinese': any(isinstance(val, str) and any('\u4e00' <= char <= '\u9fff' for char in val) 
                                         for val in df[col].dropna().head(100))
                    }
            
            self.sheet_info[sheet_name] = sheet_info
            
        return self.sheet_info
    
    def generate_summary_report(self) -> str:
        """
        生成汇总报告
        
        Returns:
            汇总报告文本
        """
        report = []
        report.append("=" * 80)
        report.append("Excel Sheet 分析报告")
        report.append("=" * 80)
        report.append(f"文件路径: {self.excel_file_path}")
        report.append(f"分析时间: {pd.Timestamp.now()}")
        report.append("")
        
        report.append(f"总共发现 {len(self.excel_data)} 个有数据的sheet:")
        report.append("")
        
        for sheet_name, info in self.sheet_info.items():
            report.append(f"Sheet: {sheet_name}")
            report.append(f"  形状: {info['shape']}")
            report.append(f"  列数: {len(info['columns'])}")
            report.append(f"  行数: {info['shape'][0]}")
            report.append("")
            
            # 显示列信息
            report.append("  列信息:")
            for col in info['columns']:
                dtype = info['data_types'][col]
                null_count = info['null_counts'][col]
                report.append(f"    - {col}: {dtype} (空值: {null_count})")
                
                # 如果是分类数据，显示唯一值信息
                if col in info['unique_values']:
                    unique_info = info['unique_values'][col]
                    report.append(f"      唯一值数量: {unique_info['count']}")
                    if unique_info['count'] <= 10:
                        report.append(f"      唯一值: {unique_info['sample_values']}")
            report.append("")
            
        return "\n".join(report)
    
    def save_analysis_results(self, output_dir: str = "."):
        """
        保存分析结果到文件
        
        Args:
            output_dir: 输出目录
        """
        # 保存汇总报告
        report_path = os.path.join(output_dir, "sheet_analysis_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(self.generate_summary_report())
        logger.info(f"汇总报告已保存到: {report_path}")
        
        # 保存详细的结构信息
        structure_path = os.path.join(output_dir, "sheet_structure.json")
        with open(structure_path, 'w', encoding='utf-8') as f:
            json.dump(self.sheet_info, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"详细结构信息已保存到: {structure_path}")
        
        # 保存每个sheet的样本数据
        for sheet_name, df in self.excel_data.items():
            sample_path = os.path.join(output_dir, f"{sheet_name}_sample.csv")
            df.head(100).to_csv(sample_path, index=False, encoding='utf-8-sig')
            logger.info(f"Sheet {sheet_name} 样本数据已保存到: {sample_path}")
    
    def identify_potential_standards(self) -> Dict[str, List[str]]:
        """
        识别可能的标准库候选
        
        Returns:
            包含潜在标准库信息的字典
        """
        standards = {
            'categorical_columns': [],
            'reference_tables': [],
            'lookup_tables': []
        }
        
        for sheet_name, info in self.sheet_info.items():
            for col, col_info in info['column_analysis'].items():
                if col_info['is_categorical']:
                    standards['categorical_columns'].append({
                        'sheet': sheet_name,
                        'column': col,
                        'unique_count': info['unique_values'][col]['count'],
                        'sample_values': info['unique_values'][col]['sample_values']
                    })
                    
            # 如果sheet的行数较少且列数适中，可能是参考表
            if info['shape'][0] < 1000 and 2 <= info['shape'][1] <= 10:
                standards['reference_tables'].append({
                    'sheet': sheet_name,
                    'shape': info['shape'],
                    'columns': info['columns']
                })
                
        return standards

def main():
    """主函数"""
    # Excel文件路径
    excel_file = "../【最后版本】运行模拟数据20250417(1).xls"
    
    if not os.path.exists(excel_file):
        logger.error(f"Excel文件不存在: {excel_file}")
        return
    
    # 创建分析器
    analyzer = ExcelSheetAnalyzer(excel_file)
    
    # 读取所有sheet（除机组sheet外）
    logger.info("开始读取Excel sheets...")
    analyzer.read_all_sheets(exclude_sheets=['机组'])
    
    # 分析结构
    logger.info("开始分析sheet结构...")
    analyzer.analyze_sheet_structure()
    
    # 保存结果
    logger.info("保存分析结果...")
    analyzer.save_analysis_results(".")
    
    # 识别潜在标准库
    logger.info("识别潜在标准库...")
    standards = analyzer.identify_potential_standards()
    
    # 保存标准库候选信息
    standards_path = "potential_standards.json"
    with open(standards_path, 'w', encoding='utf-8') as f:
        json.dump(standards, f, ensure_ascii=False, indent=2, default=str)
    logger.info(f"潜在标准库信息已保存到: {standards_path}")
    
    # 打印汇总报告
    print(analyzer.generate_summary_report())
    
    logger.info("分析完成！")

if __name__ == "__main__":
    main() 