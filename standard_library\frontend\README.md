# 标准库配置系统前端

## 环境要求

- Node.js 14+
- npm 或 yarn

## 安装依赖

```bash
cd frontend
npm install
```

## 开发模式运行

```bash
npm run serve
```

前端将在 http://localhost:8080 启动

## 生产构建

```bash
npm run build
```

构建文件将生成在 `dist/` 目录中

## 项目结构

```
frontend/
├── src/
│   ├── views/              # 页面组件
│   │   └── ConfigManager.vue # 配置管理页面
│   ├── components/         # 通用组件
│   ├── api/               # API调用
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── App.vue            # 主组件
│   └── main.js            # 入口文件
├── public/                # 静态资源
└── package.json           # 项目配置
```

## 主要功能

1. **配置管理**: 创建、编辑、删除标准配置
2. **字段映射**: 可视化配置字段映射关系
3. **软件模板**: 管理不同软件的输出模板
4. **转换预览**: 实时预览转换结果

## 技术栈

- Vue 2.6.14
- Element UI 2.15.13
- Vue Router 3.5.3
- Vuex 3.6.2
- Axios 0.27.2 