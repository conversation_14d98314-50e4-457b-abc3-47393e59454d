{"name": "standard-library-frontend", "version": "1.0.0", "description": "标准库配置系统前端", "main": "index.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"vue": "^2.6.14", "vue-router": "^3.5.3", "vuex": "^3.6.2", "element-ui": "^2.15.13", "axios": "^0.27.2"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}