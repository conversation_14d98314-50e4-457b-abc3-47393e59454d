<template>
  <div id="app">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>标准库配置系统</h1>
          <div class="header-actions">
            <el-button type="primary" @click="showHelp">帮助</el-button>
          </div>
        </div>
      </el-header>
      
      <el-container>
        <el-aside width="200px">
          <el-menu
            :default-active="currentRoute"
            router
            class="sidebar-menu">
            <el-menu-item index="/">
              <i class="el-icon-s-home"></i>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/config">
              <i class="el-icon-setting"></i>
              <span>配置管理</span>
            </el-menu-item>
            <el-menu-item index="/converter">
              <i class="el-icon-refresh"></i>
              <span>格式转换</span>
            </el-menu-item>
            <el-menu-item index="/templates">
              <i class="el-icon-document"></i>
              <span>模板管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'App',
  computed: {
    currentRoute() {
      return this.$route ? this.$route.path : '/'
    }
  },
  methods: {
    showHelp() {
      this.$message.info('标准库配置系统帮助信息');
    }
  }
}
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  height: 100vh;
}

.el-header {
  background-color: #409EFF;
  color: white;
  line-height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
}

.el-aside {
  background-color: #f5f5f5;
}

.sidebar-menu {
  border-right: none;
}

.el-main {
  padding: 20px;
  background-color: #fafafa;
}
</style> 