import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加认证token等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 配置相关API
export const configAPI = {
  // 获取配置列表
  getConfigs() {
    return api.get('/configs')
  },

  // 获取单个配置
  getConfig(id) {
    return api.get(`/configs/${id}`)
  },

  // 创建配置
  createConfig(config) {
    return api.post('/configs', config)
  },

  // 更新配置
  updateConfig(id, config) {
    return api.put(`/configs/${id}`, config)
  },

  // 删除配置
  deleteConfig(id) {
    return api.delete(`/configs/${id}`)
  },

  // 导入配置
  importConfig(file) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/configs/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出配置
  exportConfig(id) {
    return api.get(`/configs/${id}/export`, {
      responseType: 'blob'
    })
  }
}

// 转换相关API
export const converterAPI = {
  // 转换文件
  convertFile(file, targetFormat) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('targetFormat', targetFormat)
    return api.post('/convert', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取支持的格式
  getSupportedFormats() {
    return api.get('/convert/formats')
  }
}

// 模板相关API
export const templateAPI = {
  // 获取模板列表
  getTemplates() {
    return api.get('/templates')
  },

  // 创建模板
  createTemplate(template) {
    return api.post('/templates', template)
  },

  // 更新模板
  updateTemplate(id, template) {
    return api.put(`/templates/${id}`, template)
  },

  // 删除模板
  deleteTemplate(id) {
    return api.delete(`/templates/${id}`)
  }
}

export default api 