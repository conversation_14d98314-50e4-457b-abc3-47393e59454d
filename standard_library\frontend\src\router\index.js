import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'
import ExcelPreview from '@/views/ExcelPreview.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/config',
    name: 'ConfigManager',
    component: () => import('../views/ConfigManager.vue')
  },
  {
    path: '/converter',
    name: 'Converter',
    component: () => import('../views/Converter.vue')
  },
  {
    path: '/templates',
    name: 'Templates',
    component: () => import('../views/Templates.vue')
  },
  {
    path: '/excel-preview',
    name: 'ExcelPreview',
    component: ExcelPreview
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router 