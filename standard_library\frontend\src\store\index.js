import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    configs: [],
    currentConfig: null,
    loading: false
  },
  mutations: {
    SET_CONFIGS(state, configs) {
      state.configs = configs
    },
    SET_CURRENT_CONFIG(state, config) {
      state.currentConfig = config
    },
    SET_LOADING(state, loading) {
      state.loading = loading
    }
  },
  actions: {
    async loadConfigs({ commit }) {
      commit('SET_LOADING', true)
      try {
        // 这里可以调用API获取配置列表
        const configs = []
        commit('SET_CONFIGS', configs)
      } catch (error) {
        console.error('加载配置失败:', error)
      } finally {
        commit('SET_LOADING', false)
      }
    }
  },
  getters: {
    configs: state => state.configs,
    currentConfig: state => state.currentConfig,
    loading: state => state.loading
  }
}) 