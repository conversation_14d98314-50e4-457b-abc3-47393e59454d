<template>
  <div class="config-manager">
    <el-card>
      <div slot="header">
        <span>配置管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createConfig">
          新建配置
        </el-button>
      </div>
      
      <!-- 配置列表 -->
      <el-table :data="configList" style="width: 100%">
        <el-table-column prop="config_id" label="配置ID" width="180"></el-table-column>
        <el-table-column prop="name" label="配置名称" width="200"></el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="editConfig(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deleteConfig(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新建/编辑配置对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%">
      <el-form :model="configForm" :rules="rules" ref="configForm" label-width="100px">
        <el-form-item label="配置ID" prop="config_id">
          <el-input v-model="configForm.config_id" :disabled="isEdit"></el-input>
        </el-form-item>
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="configForm.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="configForm.description"></el-input>
        </el-form-item>
        
        <!-- 字段映射配置 -->
        <el-form-item label="字段映射">
          <el-button size="mini" @click="addFieldMapping">添加映射</el-button>
          <el-table :data="configForm.field_mappings" style="width: 100%; margin-top: 10px;">
            <el-table-column label="源字段" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.source_field" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="目标字段" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.target_field" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="映射类型" width="120">
              <template slot-scope="scope">
                <el-select v-model="scope.row.mapping_type" size="mini">
                  <el-option label="直接映射" value="direct"></el-option>
                  <el-option label="转换映射" value="transform"></el-option>
                  <el-option label="查找映射" value="lookup"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="mini" type="danger" @click="removeFieldMapping(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        
        <!-- 软件模板配置 -->
        <el-form-item label="软件模板">
          <el-button size="mini" @click="addSoftwareTemplate">添加模板</el-button>
          <el-table :data="configForm.software_templates" style="width: 100%; margin-top: 10px;">
            <el-table-column label="软件名称" width="150">
              <template slot-scope="scope">
                <el-select v-model="scope.row.software_name" size="mini">
                  <el-option label="HUST" value="HUST"></el-option>
                  <el-option label="GOPT" value="GOPT"></el-option>
                  <el-option label="PSASP" value="PSASP"></el-option>
                  <el-option label="BPA" value="BPA"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="主表名" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.main_sheet" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="mini" type="danger" @click="removeSoftwareTemplate(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveConfig">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { configAPI } from '@/api/config'

export default {
  name: 'ConfigManager',
  data() {
    return {
      configList: [],
      dialogVisible: false,
      isEdit: false,
      configForm: {
        config_id: '',
        name: '',
        description: '',
        field_mappings: [],
        software_templates: []
      },
      rules: {
        config_id: [
          { required: true, message: '请输入配置ID', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入配置名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑配置' : '新建配置'
    }
  },
  mounted() {
    this.loadConfigList()
  },
  methods: {
    async loadConfigList() {
      try {
        const response = await configAPI.getConfigs()
        this.configList = response.data || []
      } catch (error) {
        console.error('加载配置列表失败:', error)
        // 使用模拟数据
        this.configList = [
          {
            config_id: 'HUST_POWER_PLANT',
            name: 'HUST电源明细表配置',
            description: '用于HUST软件的电源明细表格式转换配置',
            created_at: '2025-06-25 10:00:00'
          }
        ]
      }
    },
    
    createConfig() {
      this.isEdit = false
      this.configForm = {
        config_id: '',
        name: '',
        description: '',
        field_mappings: [],
        software_templates: []
      }
      this.dialogVisible = true
    },
    
    editConfig(row) {
      this.isEdit = true
      this.configForm = { ...row }
      this.dialogVisible = true
    },
    
    async deleteConfig(row) {
      try {
        await this.$confirm('确认删除该配置吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await configAPI.deleteConfig(row.config_id)
        this.$message.success('删除成功')
        this.loadConfigList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    
    addFieldMapping() {
      this.configForm.field_mappings.push({
        source_field: '',
        target_field: '',
        mapping_type: 'direct'
      })
    },
    
    removeFieldMapping(index) {
      this.configForm.field_mappings.splice(index, 1)
    },
    
    addSoftwareTemplate() {
      this.configForm.software_templates.push({
        software_name: 'HUST',
        main_sheet: ''
      })
    },
    
    removeSoftwareTemplate(index) {
      this.configForm.software_templates.splice(index, 1)
    },
    
    async saveConfig() {
      try {
        this.$refs.configForm.validate(async (valid) => {
          if (valid) {
            if (this.isEdit) {
              await configAPI.updateConfig(this.configForm.config_id, this.configForm)
              this.$message.success('更新成功')
            } else {
              await configAPI.createConfig(this.configForm)
              this.$message.success('创建成功')
            }
            this.dialogVisible = false
            this.loadConfigList()
          }
        })
      } catch (error) {
        this.$message.error('保存失败')
      }
    }
  }
}
</script>

<style scoped>
.config-manager {
  padding: 20px;
}
</style> 