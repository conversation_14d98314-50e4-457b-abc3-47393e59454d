<template>
  <div class="converter">
    <el-card>
      <div slot="header">
        <span>格式转换</span>
      </div>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="选择配置">
          <el-select v-model="form.configId" placeholder="请选择配置">
            <el-option label="电厂机组配置" value="power_plant_config"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标软件">
          <el-select v-model="form.software" placeholder="请选择目标软件">
            <el-option label="HUST" value="HUST"></el-option>
            <el-option label="GOPT" value="GOPT"></el-option>
            <el-option label="PSASP" value="PSASP"></el-option>
            <el-option label="BPA" value="BPA"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="上传数据文件">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="previewConversion" :loading="previewLoading">
            预览转换
          </el-button>
          <el-button type="success" @click="exportExcel" :loading="exportLoading">
            导出Excel
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 预览结果 -->
      <div v-if="previewData" class="preview-section">
        <h3>转换预览</h3>
        <el-table :data="previewData" border style="width: 100%">
          <el-table-column
            v-for="column in previewColumns"
            :key="column"
            :prop="column"
            :label="column">
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Converter',
  data() {
    return {
      form: {
        configId: '',
        software: '',
        file: null
      },
      previewData: null,
      previewColumns: [],
      previewLoading: false,
      exportLoading: false
    }
  },
  methods: {
    handleFileChange(file) {
      this.form.file = file.raw
    },
    
    async previewConversion() {
      if (!this.form.configId || !this.form.software) {
        this.$message.warning('请选择配置和目标软件')
        return
      }
      
      this.previewLoading = true
      try {
        // 模拟预览数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        this.previewData = [
          { '机组编号': '机组1', '电厂编号': '电厂A', '额定容量': 600 },
          { '机组编号': '机组2', '电厂编号': '电厂B', '额定容量': 300 },
          { '机组编号': '机组3', '电厂编号': '电厂C', '额定容量': 1000 }
        ]
        
        this.previewColumns = ['机组编号', '电厂编号', '额定容量']
        
        this.$message.success('预览生成成功')
      } catch (error) {
        this.$message.error('预览失败')
      } finally {
        this.previewLoading = false
      }
    },
    
    async exportExcel() {
      if (!this.form.configId || !this.form.software) {
        this.$message.warning('请选择配置和目标软件')
        return
      }
      
      this.exportLoading = true
      try {
        // 模拟导出过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        this.$message.success('Excel文件已生成！请查看output目录。')
      } catch (error) {
        this.$message.error('导出失败')
      } finally {
        this.exportLoading = false
      }
    }
  }
}
</script>

<style scoped>
.converter {
  padding: 20px;
}

.preview-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.preview-section h3 {
  color: #409EFF;
  margin-bottom: 20px;
}
</style> 