<template>
  <div>
    <el-upload
      class="upload-demo"
      action=""
      :http-request="handleUpload"
      :show-file-list="false"
      accept=".xls,.xlsx"
      :before-upload="beforeUpload"
    >
      <el-button type="primary">上传Excel文件</el-button>
    </el-upload>

    <div v-if="sheets.length > 0" style="margin-top: 24px;">
      <el-tabs v-model="activeTab">
        <el-tab-pane
          v-for="(sheet, idx) in sheets"
          :key="sheet.name"
          :label="sheet.name"
          :name="String(idx)"
        >
          <el-table v-if="sheet.data && sheet.data.length > 0" :data="sheet.data.slice(1)" border style="width: 100%">
            <el-table-column
              v-for="(col, colIdx) in sheet.data[0]"
              :key="colIdx"
              :prop="String(colIdx)"
              :label="col"
            >
              <template slot-scope="scope">
                {{ scope.row[colIdx] }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ExcelPreview',
  data() {
    return {
      sheets: [],
      activeTab: '0'
    }
  },
  methods: {
    beforeUpload(file) {
      const isExcel = file.type.includes('excel') || file.type.includes('spreadsheetml.sheet')
      if (!isExcel) {
        this.$message.error('只支持Excel文件！')
      }
      return isExcel
    },

    async handleUpload({ file }) {
      const formData = new FormData()
      formData.append('file', file)
      try {
        const res = await axios.post('/api/analyze_excel', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        if (res.data && res.data.code === 0) {
          this.sheets = res.data.sheets
          this.activeTab = '0'
          this.$message.success('解析成功！')
        } else {
          this.$message.error(res.data.msg || '解析失败')
        }
      } catch (e) {
        console.error('上传或解析失败:', e)
        this.$message.error('上传或解析失败，请检查控制台')
      }
    }
  }
}
</script>
