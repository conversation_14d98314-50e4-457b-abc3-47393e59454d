<template>
  <div class="home">
    <el-card class="welcome-card">
      <div slot="header">
        <h2>欢迎使用标准库配置系统</h2>
      </div>
      
      <div class="welcome-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="feature-card" shadow="hover">
              <div slot="header">
                <i class="el-icon-setting"></i>
                配置管理
              </div>
              <p>创建和管理标准配置，定义字段映射关系和转换规则</p>
              <el-button type="primary" @click="$router.push('/config')">
                开始配置
              </el-button>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="feature-card" shadow="hover">
              <div slot="header">
                <i class="el-icon-refresh"></i>
                格式转换
              </div>
              <p>将数据转换为HUST、GOPT等规划软件需要的格式</p>
              <el-button type="primary" @click="$router.push('/converter')">
                开始转换
              </el-button>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="feature-card" shadow="hover">
              <div slot="header">
                <i class="el-icon-document"></i>
                模板管理
              </div>
              <p>管理不同软件的输出模板，支持多种格式</p>
              <el-button type="primary" @click="$router.push('/templates')">
                管理模板
              </el-button>
            </el-card>
          </el-col>
        </el-row>
        
        <el-divider></el-divider>
        
        <div class="quick-start">
          <h3>快速开始</h3>
          <el-steps :active="1" simple>
            <el-step title="创建配置" description="设置配置基本信息"></el-step>
            <el-step title="字段映射" description="配置源字段到目标字段的映射"></el-step>
            <el-step title="软件模板" description="选择目标软件格式"></el-step>
            <el-step title="生成文件" description="导出符合要求的Excel文件"></el-step>
          </el-steps>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 20px;
}

.welcome-content {
  padding: 20px 0;
}

.feature-card {
  text-align: center;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.feature-card .el-card__header {
  background-color: #f5f7fa;
  font-weight: bold;
}

.feature-card i {
  margin-right: 8px;
  color: #409EFF;
}

.quick-start {
  margin-top: 30px;
}

.quick-start h3 {
  color: #409EFF;
  margin-bottom: 20px;
}
</style> 