<template>
  <div class="templates">
    <el-card>
      <div slot="header">
        <span>模板管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="createTemplate">
          新建模板
        </el-button>
      </div>
      
      <el-table :data="templates" style="width: 100%">
        <el-table-column prop="name" label="模板名称" width="200"></el-table-column>
        <el-table-column prop="software" label="适用软件" width="150"></el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="editTemplate(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deleteTemplate(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新建/编辑模板对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%">
      <el-form :model="templateForm" :rules="rules" ref="templateForm" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name"></el-input>
        </el-form-item>
        <el-form-item label="适用软件" prop="software">
          <el-select v-model="templateForm.software" placeholder="请选择软件">
            <el-option label="HUST" value="HUST"></el-option>
            <el-option label="GOPT" value="GOPT"></el-option>
            <el-option label="PSASP" value="PSASP"></el-option>
            <el-option label="BPA" value="BPA"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="templateForm.description"></el-input>
        </el-form-item>
        
        <el-form-item label="主表名" prop="mainSheet">
          <el-input v-model="templateForm.mainSheet"></el-input>
        </el-form-item>
        
        <el-form-item label="必需字段">
          <el-tag
            v-for="field in templateForm.requiredFields"
            :key="field"
            closable
            @close="removeRequiredField(field)">
            {{ field }}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm">
          </el-input>
          <el-button v-else class="button-new-tag" size="small" @click="showInput">
            + 添加字段
          </el-button>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveTemplate">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Templates',
  data() {
    return {
      templates: [
        {
          name: 'HUST电源明细表模板',
          software: 'HUST',
          description: '用于HUST软件的电源明细表格式',
          createdAt: '2025-06-25 10:00:00'
        },
        {
          name: 'GOPT机组数据模板',
          software: 'GOPT',
          description: '用于GOPT软件的机组数据格式',
          createdAt: '2025-06-25 10:30:00'
        }
      ],
      dialogVisible: false,
      isEdit: false,
      templateForm: {
        name: '',
        software: '',
        description: '',
        mainSheet: '',
        requiredFields: []
      },
      rules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        software: [
          { required: true, message: '请选择适用软件', trigger: 'change' }
        ]
      },
      inputVisible: false,
      inputValue: ''
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑模板' : '新建模板'
    }
  },
  methods: {
    createTemplate() {
      this.isEdit = false
      this.templateForm = {
        name: '',
        software: '',
        description: '',
        mainSheet: '',
        requiredFields: []
      }
      this.dialogVisible = true
    },
    
    editTemplate(row) {
      this.isEdit = true
      this.templateForm = { ...row }
      this.dialogVisible = true
    },
    
    async deleteTemplate(row) {
      try {
        await this.$confirm('确认删除该模板吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const index = this.templates.findIndex(item => item.name === row.name)
        if (index > -1) {
          this.templates.splice(index, 1)
          this.$message.success('删除成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    
    saveTemplate() {
      this.$refs.templateForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 更新模板
            const index = this.templates.findIndex(item => item.name === this.templateForm.name)
            if (index > -1) {
              this.templates[index] = { ...this.templateForm }
            }
            this.$message.success('更新成功')
          } else {
            // 新建模板
            this.templates.push({
              ...this.templateForm,
              createdAt: new Date().toLocaleString()
            })
            this.$message.success('创建成功')
          }
          this.dialogVisible = false
        }
      })
    },
    
    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    
    handleInputConfirm() {
      let inputValue = this.inputValue
      if (inputValue) {
        this.templateForm.requiredFields.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    
    removeRequiredField(field) {
      const index = this.templateForm.requiredFields.indexOf(field)
      if (index > -1) {
        this.templateForm.requiredFields.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped>
.templates {
  padding: 20px;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style> 