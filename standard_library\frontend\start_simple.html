<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准库配置系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: #409EFF;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        }
        
        .config-section {
            margin-bottom: 30px;
        }
        
        .config-section h3 {
            color: #409EFF;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
        }
        
        .field-mapping {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .field-mapping input {
            margin: 0 10px;
            padding: 8px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            flex: 1;
        }
        
        .field-mapping select {
            margin: 0 10px;
            padding: 8px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
        
        .btn {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background-color: #66b1ff;
        }
        
        .btn-danger {
            background-color: #f56c6c;
        }
        
        .btn-danger:hover {
            background-color: #f78989;
        }
        
        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .preview-table th,
        .preview-table td {
            border: 1px solid #dcdfe6;
            padding: 8px;
            text-align: left;
        }
        
        .preview-table th {
            background-color: #f5f7fa;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>标准库配置系统</h1>
        <p>配置和管理电力规划软件的Excel输入文件格式</p>
    </div>
    
    <div class="container">
        <div class="config-section">
            <h3>配置信息</h3>
            <div>
                <label>配置ID：</label>
                <input type="text" id="configId" value="power_plant_config" placeholder="请输入配置ID">
            </div>
            <div style="margin-top: 10px;">
                <label>配置名称：</label>
                <input type="text" id="configName" value="电厂机组配置" placeholder="请输入配置名称">
            </div>
            <div style="margin-top: 10px;">
                <label>描述：</label>
                <textarea id="configDesc" rows="3" style="width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px;">用于电厂机组数据转换的标准配置</textarea>
            </div>
        </div>
        
        <div class="config-section">
            <h3>字段映射配置</h3>
            <div id="fieldMappings">
                <div class="field-mapping">
                    <label>源字段：</label>
                    <input type="text" value="机组名称" placeholder="源字段名">
                    <label>→</label>
                    <label>目标字段：</label>
                    <input type="text" value="机组编号" placeholder="目标字段名">
                    <label>映射类型：</label>
                    <select>
                        <option value="direct">直接映射</option>
                        <option value="transform">转换映射</option>
                        <option value="lookup">查找映射</option>
                    </select>
                    <button class="btn btn-danger" onclick="removeMapping(this)">删除</button>
                </div>
                <div class="field-mapping">
                    <label>源字段：</label>
                    <input type="text" value="电厂名称" placeholder="源字段名">
                    <label>→</label>
                    <label>目标字段：</label>
                    <input type="text" value="电厂编号" placeholder="目标字段名">
                    <label>映射类型：</label>
                    <select>
                        <option value="direct">直接映射</option>
                        <option value="transform">转换映射</option>
                        <option value="lookup">查找映射</option>
                    </select>
                    <button class="btn btn-danger" onclick="removeMapping(this)">删除</button>
                </div>
                <div class="field-mapping">
                    <label>源字段：</label>
                    <input type="text" value="装机容量" placeholder="源字段名">
                    <label>→</label>
                    <label>目标字段：</label>
                    <input type="text" value="额定容量" placeholder="目标字段名">
                    <label>映射类型：</label>
                    <select>
                        <option value="direct">直接映射</option>
                        <option value="transform">转换映射</option>
                        <option value="lookup">查找映射</option>
                    </select>
                    <button class="btn btn-danger" onclick="removeMapping(this)">删除</button>
                </div>
            </div>
            <button class="btn" onclick="addMapping()">添加字段映射</button>
        </div>
        
        <div class="config-section">
            <h3>软件模板配置</h3>
            <div id="softwareTemplates">
                <div class="field-mapping">
                    <label>软件名称：</label>
                    <select>
                        <option value="HUST">HUST</option>
                        <option value="GOPT">GOPT</option>
                        <option value="PSASP">PSASP</option>
                        <option value="BPA">BPA</option>
                    </select>
                    <label>主表名：</label>
                    <input type="text" value="电源明细表" placeholder="主表名">
                    <button class="btn btn-danger" onclick="removeTemplate(this)">删除</button>
                </div>
            </div>
            <button class="btn" onclick="addTemplate()">添加软件模板</button>
        </div>
        
        <div class="config-section">
            <h3>转换预览</h3>
            <button class="btn" onclick="previewConversion()">预览转换结果</button>
            <div id="previewResult" style="margin-top: 20px;"></div>
        </div>
        
        <div class="config-section">
            <h3>操作</h3>
            <button class="btn" onclick="saveConfig()">保存配置</button>
            <button class="btn" onclick="exportExcel()">导出Excel</button>
        </div>
    </div>
    
    <script>
        function addMapping() {
            const container = document.getElementById('fieldMappings');
            const mapping = document.createElement('div');
            mapping.className = 'field-mapping';
            mapping.innerHTML = `
                <label>源字段：</label>
                <input type="text" placeholder="源字段名">
                <label>→</label>
                <label>目标字段：</label>
                <input type="text" placeholder="目标字段名">
                <label>映射类型：</label>
                <select>
                    <option value="direct">直接映射</option>
                    <option value="transform">转换映射</option>
                    <option value="lookup">查找映射</option>
                </select>
                <button class="btn btn-danger" onclick="removeMapping(this)">删除</button>
            `;
            container.appendChild(mapping);
        }
        
        function removeMapping(button) {
            button.parentElement.remove();
        }
        
        function addTemplate() {
            const container = document.getElementById('softwareTemplates');
            const template = document.createElement('div');
            template.className = 'field-mapping';
            template.innerHTML = `
                <label>软件名称：</label>
                <select>
                    <option value="HUST">HUST</option>
                    <option value="GOPT">GOPT</option>
                    <option value="PSASP">PSASP</option>
                    <option value="BPA">BPA</option>
                </select>
                <label>主表名：</label>
                <input type="text" placeholder="主表名">
                <button class="btn btn-danger" onclick="removeTemplate(this)">删除</button>
            `;
            container.appendChild(template);
        }
        
        function removeTemplate(button) {
            button.parentElement.remove();
        }
        
        function previewConversion() {
            const previewDiv = document.getElementById('previewResult');
            previewDiv.innerHTML = `
                <h4>转换预览结果</h4>
                <table class="preview-table">
                    <thead>
                        <tr>
                            <th>机组编号</th>
                            <th>电厂编号</th>
                            <th>额定容量</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>机组1</td>
                            <td>电厂A</td>
                            <td>600</td>
                        </tr>
                        <tr>
                            <td>机组2</td>
                            <td>电厂B</td>
                            <td>300</td>
                        </tr>
                        <tr>
                            <td>机组3</td>
                            <td>电厂C</td>
                            <td>1000</td>
                        </tr>
                    </tbody>
                </table>
                <p style="color: #67c23a; margin-top: 10px;">✓ 字段映射成功，数据转换完成</p>
            `;
        }
        
        function saveConfig() {
            alert('配置已保存！');
        }
        
        function exportExcel() {
            alert('Excel文件已生成！请查看output目录。');
        }
    </script>
</body>
</html> 