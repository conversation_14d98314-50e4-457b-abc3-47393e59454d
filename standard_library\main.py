#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准库配置系统主程序
演示配置管理和Excel转换功能
"""

import pandas as pd
import logging
import os
from datetime import datetime

from core.config_manager import ConfigManager
from core.converter_manager import ConverterManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_data() -> pd.DataFrame:
    """创建示例数据"""
    data = {
        '机组名称': ['机组1', '机组2', '机组3', '机组4', '机组5'],
        '电厂名称': ['电厂A', '电厂B', '电厂C', '电厂A', '电厂B'],
        '装机容量': [600, 300, 1000, 600, 300],
        '机组类型': ['火电', '水电', '核电', '火电', '水电'],
        '投产年份': [2020, 2019, 2021, 2020, 2019],
        '所在省份': ['广东', '四川', '浙江', '广东', '四川']
    }
    return pd.DataFrame(data)

def create_hust_template() -> dict:
    """创建HUST软件模板"""
    return {
        'config': {
            'main_sheet': '电源明细表',
            'required_columns': ['机组名称', '电厂名称', '装机容量', '机组类型'],
            'additional_sheets': {
                '机组报价': {
                    'data': [
                        {'机组名称': '机组1', '报价': 0.5},
                        {'机组名称': '机组2', '报价': 0.3},
                        {'机组名称': '机组3', '报价': 0.8}
                    ]
                },
                '机组规划类型': {
                    'data': [
                        {'机组名称': '机组1', '规划类型': '新建'},
                        {'机组名称': '机组2', '规划类型': '扩建'},
                        {'机组名称': '机组3', '规划类型': '新建'}
                    ]
                }
            }
        }
    }

def create_gopt_template() -> dict:
    """创建GOPT软件模板"""
    return {
        'config': {
            'main_sheet': '机组数据',
            'required_columns': ['机组名称', '电厂名称', '装机容量'],
            'additional_sheets': {
                '机组参数': {
                    'data': [
                        {'机组名称': '机组1', '最小出力': 200, '最大出力': 600},
                        {'机组名称': '机组2', '最小出力': 100, '最大出力': 300},
                        {'机组名称': '机组3', '最小出力': 300, '最大出力': 1000}
                    ]
                }
            }
        }
    }

def demo_config_management():
    """演示配置管理功能"""
    logger.info("=== 配置管理演示 ===")
    
    # 创建配置管理器
    config_manager = ConfigManager("configs")
    
    # 创建新配置
    config = config_manager.create_config(
        config_id="power_plant_config",
        name="电厂机组配置",
        description="用于电厂机组数据转换的标准配置"
    )
    
    # 添加字段映射
    config_manager.add_field_mapping(
        config_id="power_plant_config",
        source_field="机组名称",
        target_field="机组编号",
        mapping_type="direct"
    )
    
    config_manager.add_field_mapping(
        config_id="power_plant_config",
        source_field="电厂名称",
        target_field="电厂编号",
        mapping_type="direct"
    )
    
    config_manager.add_field_mapping(
        config_id="power_plant_config",
        source_field="装机容量",
        target_field="额定容量",
        mapping_type="direct"
    )
    
    # 添加软件模板
    config_manager.add_software_template(
        config_id="power_plant_config",
        software_name="HUST",
        template_config=create_hust_template()
    )
    
    config_manager.add_software_template(
        config_id="power_plant_config",
        software_name="GOPT",
        template_config=create_gopt_template()
    )
    
    # 列出所有配置
    configs = config_manager.list_configs()
    logger.info(f"当前配置列表: {configs}")
    
    return config_manager

def demo_conversion():
    """演示转换功能"""
    logger.info("=== 转换功能演示 ===")
    
    # 创建配置管理器
    config_manager = ConfigManager("configs")
    
    # 创建转换器管理器
    converter_manager = ConverterManager(config_manager)
    
    # 创建示例数据
    sample_data = create_sample_data()
    logger.info(f"示例数据:\n{sample_data}")
    
    # 预览转换结果
    preview = converter_manager.preview_conversion(
        config_id="power_plant_config",
        software_name="HUST",
        source_data=sample_data
    )
    
    logger.info("转换预览:")
    logger.info(f"原始列: {preview['original_columns']}")
    logger.info(f"映射后列: {preview['mapped_columns']}")
    logger.info(f"最终列: {preview['final_columns']}")
    
    # 验证配置
    validation = converter_manager.validate_config(
        config_id="power_plant_config",
        software_name="HUST"
    )
    
    logger.info(f"配置验证结果: {validation}")
    
    # 执行转换
    try:
        output_path = converter_manager.convert_to_software_format(
            config_id="power_plant_config",
            software_name="HUST",
            source_data=sample_data
        )
        logger.info(f"转换成功，输出文件: {output_path}")
    except Exception as e:
        logger.error(f"转换失败: {str(e)}")

def main():
    """主函数"""
    logger.info("启动标准库配置系统演示")
    
    try:
        # 演示配置管理
        config_manager = demo_config_management()
        
        # 演示转换功能
        demo_conversion()
        
        logger.info("演示完成！")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {str(e)}")
        raise

if __name__ == "__main__":
    main() 