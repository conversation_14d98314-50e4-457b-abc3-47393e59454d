"""
标准配置数据模型
定义标准库的核心配置结构
"""

from datetime import datetime
from typing import Dict, Any, Optional

class StandardConfig:
    """
    标准配置类
    用于存储和管理标准库的配置信息
    """
    
    def __init__(self, config_id: str, name: str, description: str = ""):
        self.config_id = config_id
        self.name = name
        self.description = description
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.is_active = True
        
        # 配置内容
        self.field_mappings = {}  # 字段映射
        self.conversion_rules = {}  # 转换规则
        self.validation_rules = {}  # 验证规则
        self.software_templates = {}  # 软件模板
        
    def add_field_mapping(self, source_field: str, target_field: str, 
                         mapping_type: str = "direct", 
                         transformation: Optional[str] = None) -> None:
        """
        添加字段映射
        
        Args:
            source_field: 源字段名
            target_field: 目标字段名
            mapping_type: 映射类型 (direct, transform, lookup)
            transformation: 转换规则
        """
        self.field_mappings[source_field] = {
            'target_field': target_field,
            'mapping_type': mapping_type,
            'transformation': transformation
        }
        self.updated_at = datetime.now()
        
    def add_conversion_rule(self, rule_name: str, rule_type: str, 
                           rule_config: Dict[str, Any]) -> None:
        """
        添加转换规则
        
        Args:
            rule_name: 规则名称
            rule_type: 规则类型
            rule_config: 规则配置
        """
        self.conversion_rules[rule_name] = {
            'type': rule_type,
            'config': rule_config,
            'created_at': datetime.now()
        }
        self.updated_at = datetime.now()
        
    def add_software_template(self, software_name: str, template_config: Dict[str, Any]) -> None:
        """
        添加软件模板
        
        Args:
            software_name: 软件名称 (如 HUST, GOPT)
            template_config: 模板配置
        """
        self.software_templates[software_name] = {
            'config': template_config,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        self.updated_at = datetime.now()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'config_id': self.config_id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'is_active': self.is_active,
            'field_mappings': self.field_mappings,
            'conversion_rules': self.conversion_rules,
            'validation_rules': self.validation_rules,
            'software_templates': self.software_templates
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StandardConfig':
        """从字典创建配置对象"""
        config = cls(
            config_id=data['config_id'],
            name=data['name'],
            description=data.get('description', '')
        )
        
        config.created_at = datetime.fromisoformat(data['created_at'])
        config.updated_at = datetime.fromisoformat(data['updated_at'])
        config.is_active = data.get('is_active', True)
        config.field_mappings = data.get('field_mappings', {})
        config.conversion_rules = data.get('conversion_rules', {})
        config.validation_rules = data.get('validation_rules', {})
        config.software_templates = data.get('software_templates', {})
        
        return config 