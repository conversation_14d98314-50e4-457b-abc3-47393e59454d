#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准库配置系统演示程序
简化版本，避免复杂的导入问题
"""

import pandas as pd
import json
import os
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_data():
    """创建示例数据"""
    data = {
        '机组名称': ['机组1', '机组2', '机组3', '机组4', '机组5'],
        '电厂名称': ['电厂A', '电厂B', '电厂C', '电厂A', '电厂B'],
        '装机容量': [600, 300, 1000, 600, 300],
        '机组类型': ['火电', '水电', '核电', '火电', '水电'],
        '投产年份': [2020, 2019, 2021, 2020, 2019],
        '所在省份': ['广东', '四川', '浙江', '广东', '四川']
    }
    return pd.DataFrame(data)

def demo_basic_functionality():
    """演示基本功能"""
    logger.info("=== 标准库配置系统演示 ===")
    
    # 创建示例数据
    sample_data = create_sample_data()
    logger.info(f"示例数据:\n{sample_data}")
    
    # 创建配置目录
    config_dir = "configs"
    os.makedirs(config_dir, exist_ok=True)
    
    # 创建示例配置
    config = {
        "config_id": "power_plant_config",
        "name": "电厂机组配置",
        "description": "用于电厂机组数据转换的标准配置",
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "is_active": True,
        "field_mappings": {
            "机组名称": {
                "target_field": "机组编号",
                "mapping_type": "direct",
                "transformation": None
            },
            "电厂名称": {
                "target_field": "电厂编号", 
                "mapping_type": "direct",
                "transformation": None
            },
            "装机容量": {
                "target_field": "额定容量",
                "mapping_type": "direct", 
                "transformation": None
            }
        },
        "conversion_rules": {},
        "validation_rules": {},
        "software_templates": {
            "HUST": {
                "config": {
                    "main_sheet": "电源明细表",
                    "required_columns": ["机组名称", "电厂名称", "装机容量", "机组类型"],
                    "additional_sheets": {
                        "机组报价": {
                            "data": [
                                {"机组名称": "机组1", "报价": 0.5},
                                {"机组名称": "机组2", "报价": 0.3},
                                {"机组名称": "机组3", "报价": 0.8}
                            ]
                        }
                    }
                },
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        }
    }
    
    # 保存配置
    config_path = os.path.join(config_dir, f"{config['config_id']}.json")
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    logger.info(f"配置已保存到: {config_path}")
    
    # 应用字段映射
    mapped_data = pd.DataFrame()
    for source_field, mapping in config['field_mappings'].items():
        target_field = mapping['target_field']
        if source_field in sample_data.columns:
            mapped_data[target_field] = sample_data[source_field]
            logger.info(f"字段映射: {source_field} -> {target_field}")
    
    logger.info(f"映射后的数据:\n{mapped_data}")
    
    # 生成输出文件
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(output_dir, f"HUST_{config['config_id']}_{timestamp}.xlsx")
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # 写入主数据表
        mapped_data.to_excel(writer, sheet_name='电源明细表', index=False)
        
        # 写入附加表
        for sheet_name, sheet_config in config['software_templates']['HUST']['config']['additional_sheets'].items():
            if 'data' in sheet_config:
                sheet_data = pd.DataFrame(sheet_config['data'])
                sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                logger.info(f"添加表: {sheet_name}")
    
    logger.info(f"Excel文件已生成: {output_path}")
    
    # 显示生成的文件信息
    logger.info("=== 生成的文件信息 ===")
    logger.info(f"主表: 电源明细表")
    logger.info(f"列: {list(mapped_data.columns)}")
    logger.info(f"行数: {len(mapped_data)}")
    
    return output_path

def main():
    """主函数"""
    try:
        output_path = demo_basic_functionality()
        logger.info("=== 演示完成 ===")
        logger.info(f"请查看生成的文件: {output_path}")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {str(e)}")
        raise

if __name__ == "__main__":
    main() 