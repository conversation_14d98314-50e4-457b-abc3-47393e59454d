================================================================================
Excel Sheet 分析报告
================================================================================
文件路径: ../【最后版本】运行模拟数据20250417(1).xls
分析时间: 2025-06-25 09:51:18.961287

总共发现 18 个有数据的sheet:

Sheet: 系统
  形状: (22, 48)
  列数: 48
  行数: 22

  列信息:
    - 参数类型号: object (空值: 0)
      唯一值数量: 22
    - 参数[1]: object (空值: 0)
      唯一值数量: 13
    - 参数[2]: object (空值: 0)
      唯一值数量: 12
    - 参数[3]: object (空值: 0)
      唯一值数量: 14
    - 参数[4]: object (空值: 0)
      唯一值数量: 18
    - 参数[5]: object (空值: 0)
      唯一值数量: 17
    - 参数[6]: object (空值: 0)
      唯一值数量: 15
    - 参数[7]: object (空值: 0)
      唯一值数量: 14
    - 参数[8]: object (空值: 0)
      唯一值数量: 16
    - 参数[9]: object (空值: 2)
      唯一值数量: 16
    - 参数[10]: object (空值: 4)
      唯一值数量: 15
    - 参数[11]: object (空值: 4)
      唯一值数量: 14
    - 参数[12]: object (空值: 4)
      唯一值数量: 13
    - 参数[13]: object (空值: 6)
      唯一值数量: 12
    - 参数[14]: object (空值: 6)
      唯一值数量: 10
      唯一值: ['可再生能源运行方式是否为确定性出力（0：聚类后典型出力；1：确定性出力）', '0', '系统负荷调整量(MW)', '抽水蓄能机组在周优化运行分配的容量比例（0~1）', '采样故障统计参数1', '1', '随机数种子类型(0, 随机种子，1，固定种子)', '是否考虑风电场空间相关性（0：否，1：是）', '是否考虑检修（0：否，1：是）', '随机数种子类型']
    - 参数[15]: object (空值: 6)
      唯一值数量: 11
    - 参数[16]: object (空值: 8)
      唯一值数量: 10
      唯一值: ['谷荷时段出力', '25', '是否考虑系统碳排放（0：否，1：是）', '1', '新能源峰荷出力（0~1之间的数）', '0', '是否合并统一节点机组加速计算（0：否，1：是）', '是否考虑风电尾流效应（0：否，1：是）', '是否考虑调频约束（0：否，1：是）', '光伏装机容量调整量(%)']
    - 参数[17]: object (空值: 8)
      唯一值数量: 10
      唯一值: ['是否考虑运行维护成本', '1', '是否考虑系统发电成本（0：否，1：是）', '新能源谷荷出力（0~1之间的数）', '0.5', '是否故障集匹配加速计算（0：否，1：是）', '0', '是否考虑风速日特性（0：否，1：是）', '是否考虑负荷跟踪约束（0：否，1：是）', '常规机组强迫停运率调整量(%)']
    - 参数[18]: object (空值: 8)
      唯一值数量: 10
      唯一值: ['是否考虑总煤耗约束', '1', '规划期系统碳价(元/吨)', '100', '是否考虑水电中长期运行模拟（0：不考虑，1：考虑）', '0', '重要性采样参数（0：不采用重要性采样，1：无自适应，2：一般自适应，3：最优自适应）', '是否考虑风速季节特性（0：否，1：是）', '是否考虑网络约束（0：否，1：是）', '负荷调整量(%)']
    - 参数[19]: object (空值: 8)
      唯一值数量: 10
      唯一值: ['是否考虑潮流约束（0：否，1：是）', '0', '规划期系统发电成本(元/kWh)', '0.3', '水电约束考虑方式（0：考虑电量约束，1：考虑水量约束）', '随机数种子类型（0：随机种子，1：固定种子）', '1', '风机功率曲线模式（0，直线型，1，三次曲线型）', '是否考虑断面约束（0：否，1：是）', '线路容量调整量(%)']
    - 参数[20]: object (空值: 8)
      唯一值数量: 12
    - 参数[21]: object (空值: 10)
      唯一值数量: 9
      唯一值: ['负荷调整量', '0', '是否考虑目标规划（0：否，1：是）', '弃风/弃光顺序（不允许弃风/光时该措施该值为-1）', '1', '新能源发电选项（0：不考虑新能源出力，1：考虑新能源平均出力，2考虑新能源随机出力）', '随机数种子类型(0, 随机种子，1，固定种子)', '负备用(%)', '0.02']
    - 参数[22]: object (空值: 12)
      唯一值数量: 8
      唯一值: ['系统备用率调整量', '0', '新能源出力方式（0：聚类典型出力；1：确定性出力）', '弃水顺序（不允许弃水时该措施该值为-1）', '2', '机组停运概率选项（0：直接获取停运概率，1：通过停运率计算停运概率）', '随机数种子', '3']
    - 参数[23]: object (空值: 12)
      唯一值数量: 8
      唯一值: ['燃煤约束调整量', '0', '新能源低谷出力', '0.5', '单次计算迭代时间限制（秒）', '300', '线路停运概率选项（0：直接获取停运概率，1：通过停运率计算停运概率）', '风电装机容量调整量']
    - 参数[24]: object (空值: 12)
      唯一值数量: 8
      唯一值: ['线路传输容量调整量', '0', '新能源高峰出力', '0.5', '最大迭代次数（次）', '3000', '是否考虑系统稳定性（0：否，1：是）', '常规机组强迫停运率调整量']
    - 参数[25]: object (空值: 12)
      唯一值数量: 8
      唯一值: ['平均网损率', '0', '是否考虑N-1约束（0：否，1：是）', '优化精度', '0.01', '功率基值', '100', '负荷调整量']
    - 参数[26]: object (空值: 12)
      唯一值数量: 8
      唯一值: ['最大迭代次数', '200000', '是否输出潮流（0：否，1：是）', '1', '调峰或备用不足时是否可以去除备用（0：否，1：是）', '是否考虑安控措施（0：否，1：是）', '0', '线路容量调整量']
    - 参数[27]: object (空值: 12)
      唯一值数量: 8
      唯一值: ['最大计算时间（秒）', '1800', '是否更新电网规划方案（0：否，1：是）', '0', '是否进行检修模拟（0：否，1：是）', '2', '是否考虑连锁故障（0：否，1：是）', '线路强迫停运率调整量']
    - 参数[28]: object (空值: 16)
      唯一值数量: 5
      唯一值: ['整数计算间隙', '0.0001', '检修优化目标（0：等备用；1：等备用率；2：等风险度）', '0', '是否考虑同杆并架线路（0：否，1：是）']
    - 参数[29]: object (空值: 18)
      唯一值数量: 3
      唯一值: ['检修安排方式（0：按等效检修天数安排；1：考虑机组大修小修）', '0', '是否考虑设备老化（0：否，1：是）']
    - 参数[30]: object (空值: 18)
      唯一值数量: 3
      唯一值: ['深度调峰门槛设定（%）（小于这个最低负荷率则采用深度调峰措施）', '0', '灾害发生情况（用4位2进制表示，0：不考虑灾害，最低位表示雷电，次低位表示污闪，次高位表示冰害，最高位表示鸟害）']
    - 参数[31]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['燃机启停调峰措施对应顺序（不采用该措施该值为-1）', '-1', '灾区内故障相关性类型（0：不考虑相关性，1：外部给定，2：强相关，3：中相关，4：弱相关）', '0']
    - 参数[32]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['燃机最小开机时间（小时）', '6', '节点停运概率选项（0：直接获取停运概率，1：通过停运率计算停运概率）', '0']
    - 参数[33]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['火电机组深度调峰措施对应顺序（不采用该措施该值为-1）', '-1', '负荷不确定性考虑方式（0：仅考虑最高负荷，1：仅考虑平均负荷，2：考虑时序负荷）', '0']
    - 参数[34]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['火电机组最小负荷率（0~1之间的数）', '0.3', ' 负荷不确定性选项（0：不考虑不确定性，1：考虑各区域负荷服从独立的正态分布，2：考虑各区域负荷服从非独立正态分布）', '0']
    - 参数[35]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['火电机组启停调峰措施对应顺序（不采用该措施该值为-1）', '-1', '机组停运率调整幅度', '0']
    - 参数[36]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['火电机组启停的容量级别（MW）（安排小于等于该容量的煤电机组启停）', '300', '线路停运率调整幅度', '0']
    - 参数[37]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['核电机组降低出力运行措施对应顺序（不采用该措施该值为-1）', '-1', '节点停运率调整幅度', '0']
    - 参数[38]: object (空值: 18)
      唯一值数量: 3
      唯一值: ['是否考虑排放惩罚（0：否，1：是）', '0', '负荷倍数调整幅度']
    - 参数[39]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['排放惩罚设定（元/吨CO2）', '20', '单次优化计算最大迭代次数（次）', '3000']
    - 参数[40]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['是否考虑网损修正（0：否，1：是）', '0', '单次优化计算迭代时间限制（秒）', '5']
    - 参数[41]: object (空值: 18)
      唯一值数量: 4
      唯一值: ['核电机组运行方式（0：按额定容量运行，1：调峰容量不足时安排降出力运行）', '0', '单次优化计算精度', '0.01']
    - 参数[42]: object (空值: 20)
      唯一值数量: 2
      唯一值: ['系统停机备用率', '0.05']
    - 参数[43]: object (空值: 20)
      唯一值数量: 2
      唯一值: ['是否考虑日内滚动优化，0：不考虑；1：考虑', '0']
    - 参数[44]: object (空值: 20)
      唯一值数量: 2
      唯一值: ['是否允许分布式光伏弃用（0：不允许；1：允许）', '0']
    - 参数[45]: object (空值: 20)
      唯一值数量: 2
      唯一值: ['是否联动燃气机组（0：否，1：是）', '0']
    - 参数[46]: object (空值: 20)
      唯一值数量: 2
      唯一值: ['是否鼓励直流（0：否，1：是）', '0']
    - 参数[47]: object (空值: 20)
      唯一值数量: 2
      唯一值: ['系统最小惯量约束(小于1e-3：不考虑，其他:单位秒) ', '0']

Sheet: 机组报价
  形状: (718, 26)
  列数: 26
  行数: 718

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]机组名称: object (空值: 0)
      唯一值数量: 718
    - [1]起始日期: int64 (空值: 0)
    - [2]时段1: float64 (空值: 0)
    - [3]时段2: float64 (空值: 0)
    - [4]时段3: float64 (空值: 0)
    - [5]时段4: float64 (空值: 0)
    - [6]时段5: float64 (空值: 0)
    - [7]时段6: float64 (空值: 0)
    - [8]时段7: float64 (空值: 0)
    - [9]时段8: float64 (空值: 0)
    - [10]时段9: float64 (空值: 0)
    - [11]时段10: float64 (空值: 0)
    - [12]时段11: float64 (空值: 0)
    - [13]时段12: float64 (空值: 0)
    - [14]时段13: float64 (空值: 0)
    - [15]时段14: float64 (空值: 0)
    - [16]时段15: float64 (空值: 0)
    - [17]时段16: float64 (空值: 0)
    - [18]时段17: float64 (空值: 0)
    - [19]时段18: float64 (空值: 0)
    - [20]时段19: float64 (空值: 0)
    - [21]时段20: float64 (空值: 0)
    - [22]时段21: float64 (空值: 0)
    - [23]时段22: float64 (空值: 0)
    - [24]时段23: float64 (空值: 0)
    - [25]时段24: float64 (空值: 0)

Sheet: 水电三段式出力
  形状: (78, 16)
  列数: 16
  行数: 78

  列信息:
    - 电厂名称: object (空值: 0)
      唯一值数量: 13
    - 起始时间: int64 (空值: 0)
    - 输入类型A（1：枯水年；2：平水年；3：丰水年）: int64 (空值: 0)
    - 输入类型B（1：预想出力；2：平均出力；3：强迫出力）: int64 (空值: 0)
    - 1月: float64 (空值: 0)
    - 2月: float64 (空值: 0)
    - 3月: float64 (空值: 0)
    - 4月: float64 (空值: 0)
    - 5月: float64 (空值: 0)
    - 6月: float64 (空值: 0)
    - 7月: float64 (空值: 0)
    - 8月: float64 (空值: 0)
    - 9月: float64 (空值: 0)
    - 10月: float64 (空值: 0)
    - 11月: float64 (空值: 0)
    - 12月: float64 (空值: 0)

Sheet: 风电场
  形状: (17, 11)
  列数: 11
  行数: 17

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）: object (空值: 0)
      唯一值数量: 17
    - [1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认风区']
    - [2]风机切入风速: float64 (空值: 0)
    - [3]风机额定风速: int64 (空值: 0)
    - [4]风机切出风速: int64 (空值: 0)
    - [5]风电场尾流系数: float64 (空值: 0)
    - [6]风机可用率: float64 (空值: 0)
    - [7]风电预测绝对误差占装机容量的百分比: float64 (空值: 0)
    - Unnamed: 8: float64 (空值: 17)
    - Unnamed: 9: float64 (空值: 17)
    - Unnamed: 10: float64 (空值: 17)

Sheet: 风区信息
  形状: (1, 41)
  列数: 41
  行数: 1

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]风区名称: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认风区']
    - 起始时间: int64 (空值: 0)
    - [1]风速Weibull分布参数c: float64 (空值: 0)
    - [2]风速Weibull分布参数k: float64 (空值: 0)
    - [3]风速自相关函数衰减系数: float64 (空值: 0)
    - 此列开始为风区各月平均风速（标幺值）[4]1月: float64 (空值: 0)
    - [5]2月: float64 (空值: 0)
    - [6]3月: float64 (空值: 0)
    - [7]4月: float64 (空值: 0)
    - [8]5月: float64 (空值: 0)
    - [9]6月: float64 (空值: 0)
    - [10]7月: float64 (空值: 0)
    - [11]8月: float64 (空值: 0)
    - [12]9月: float64 (空值: 0)
    - [13]10月: float64 (空值: 0)
    - [14]11月: float64 (空值: 0)
    - [15]12月: float64 (空值: 0)
    - 此列开始为风区日内各时段平均风速（标幺值）[16]时段1: float64 (空值: 0)
    - [17]时段2: float64 (空值: 0)
    - [18]时段3: float64 (空值: 0)
    - [19]时段4: float64 (空值: 0)
    - [20]时段5: float64 (空值: 0)
    - [21]时段6: float64 (空值: 0)
    - [22]时段7: float64 (空值: 0)
    - [23]时段8: float64 (空值: 0)
    - [24]时段9: float64 (空值: 0)
    - [25]时段10: float64 (空值: 0)
    - [26]时段11: float64 (空值: 0)
    - [27]时段12: float64 (空值: 0)
    - [28]时段13: float64 (空值: 0)
    - [29]时段14: float64 (空值: 0)
    - [30]时段15: float64 (空值: 0)
    - [31]时段16: float64 (空值: 0)
    - [32]时段17: float64 (空值: 0)
    - [33]时段18: float64 (空值: 0)
    - [34]时段19: float64 (空值: 0)
    - [35]时段20: float64 (空值: 0)
    - [36]时段21: float64 (空值: 0)
    - [37]时段22: float64 (空值: 0)
    - [38]时段23: float64 (空值: 0)
    - [39]时段24: float64 (空值: 0)

Sheet: 风区之间相关系数
  形状: (1, 2)
  列数: 2
  行数: 1

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]风区名称: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认风区']
    - 默认风区: int64 (空值: 0)

Sheet: 风电备用
  形状: (20, 3)
  列数: 3
  行数: 20

  列信息:
    - [0]风电出力占装机比例（%）: int64 (空值: 0)
    - [1]正备用（%）: int64 (空值: 0)
    - [2]负备用（%）: int64 (空值: 0)

Sheet: 光伏电站
  形状: (7, 8)
  列数: 8
  行数: 7

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）: object (空值: 0)
      唯一值数量: 7
      唯一值: ['GF#粤北光伏发电#1', 'GF#粤东光伏发电#1', 'GF#粤西光伏发电#1', 'GF#珠东北光伏发电#1', 'GF#珠东南光伏发电#1', 'GF#珠西北光伏发电#1', 'GF#珠西南光伏发电#1']
    - [1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认光区']
    - [2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴): int64 (空值: 0)
    - [3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°): int64 (空值: 0)
    - [4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°): int64 (空值: 0)
    - [5]光伏组件单元个数: int64 (空值: 0)
    - [6]光伏板可用率（0~1之间的数）: int64 (空值: 0)
    - [7]预测绝对误差占装机容量的百分比: float64 (空值: 0)

Sheet: 光区信息
  形状: (1, 19)
  列数: 19
  行数: 1

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]光区名称: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认光区']
    - 起始时间: int64 (空值: 0)
    - [1]晴空指数概率分布参数C: float64 (空值: 0)
    - [2]晴空指数概率分布参数lamda: float64 (空值: 0)
    - [3]晴空指数概率分布参数ktu: float64 (空值: 0)
    - [4]地理位置--经度: float64 (空值: 0)
    - [5]地理位置--纬度: float64 (空值: 0)
    - [6]地理位置--海拔高度: float64 (空值: 0)
    - [7]地面反射率: float64 (空值: 0)
    - [8]大气散射系数p: int64 (空值: 0)
    - [9]大气散射系数q: int64 (空值: 0)
    - [10]天气类型1概率（晴）: float64 (空值: 0)
    - [11]天气类型2概率（多云、雾): float64 (空值: 0)
    - [12]天气类型3概率（阴、小雨、小雪、冻雨）: float64 (空值: 0)
    - [13]天气类型4概率（中雨、大雨、暴雨、大暴雨、特大暴雨、中雪、大雪、暴雪、沙尘暴）: float64 (空值: 0)
    - [14]天气类型5概率（晴间多云、多云间晴）: float64 (空值: 0)
    - [15]天气类型6概率（阴间多云、多云间阴）: float64 (空值: 0)
    - [16]天气类型7概率（阵雨、雨夹雪、雷阵雨、雷阵雨伴有冰雹、阵雪、小到中雨、小到中雪）: float64 (空值: 0)
    - [17]天气类型8概率（中到大雨、大到暴雨、暴雨到大暴雨、大暴雨到特大暴雨、中到大雪、大到暴雪）: float64 (空值: 0)

Sheet: 光区之间相关系数
  形状: (1, 2)
  列数: 2
  行数: 1

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]光区名称: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认光区']
    - 默认光区: int64 (空值: 0)

Sheet: 节点
  形状: (9, 10)
  列数: 10
  行数: 9

  列信息:
    - 注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称: object (空值: 0)
      唯一值数量: 9
      唯一值: ['GD_ZDN', 'GD_ZDB', 'GD_ZXN', 'GD_ZXB', 'GD_YD', 'GD_YX', 'GD_YB', 'QW_AM', 'QW_HK']
    - [1]投产日期: int64 (空值: 0)
    - [2]退役日期: int64 (空值: 0)
    - [3]节点负荷占总负荷的比例(%): float64 (空值: 0)
    - [4]节点固定负荷(MW): int64 (空值: 0)
    - [5]节点所属大区: object (空值: 0)
      唯一值数量: 2
      唯一值: ['广东', '外送']
    - [6]节点切除负荷损失(元/kWh): int64 (空值: 0)
    - [7]节点强迫停运率: int64 (空值: 0)
    - [8]节点故障恢复时间: int64 (空值: 0)
    - [9]供电区域: object (空值: 0)
      唯一值数量: 1
      唯一值: ['系统']

Sheet: 发电公司
  形状: (21, 1)
  列数: 1
  行数: 21

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]发电公司名称: object (空值: 0)
      唯一值数量: 21

Sheet: 机组规划类型
  形状: (15, 11)
  列数: 11
  行数: 15

  列信息:
    - 注：该表格中数据仅用于电源规划优化计算[0]分类名称: object (空值: 0)
      唯一值数量: 15
    - [1]是否可启停: int64 (空值: 0)
    - [2]单位启停费用（元/MW): int64 (空值: 0)
    - [3]是否必开: int64 (空值: 0)
    - [4]利用小时数: int64 (空值: 0)
    - [5]最小出力率: float64 (空值: 0)
    - [6]厂用电: float64 (空值: 0)
    - [7]煤耗(g/kWh): int64 (空值: 0)
    - [8]运行成本(元/kWh): float64 (空值: 0)
    - [9]碳排放强度（tCO2/MWh）: float64 (空值: 0)
    - [10]冬季检修率: int64 (空值: 0)

Sheet: 机组指定出力
  形状: (13976, 28)
  列数: 28
  行数: 13976

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]机组名称: object (空值: 0)
      唯一值数量: 144
    - [1]起始日期: int64 (空值: 0)
    - [2]结束日期: int64 (空值: 0)
    - [3]时段1: float64 (空值: 0)
    - [4]时段2: float64 (空值: 0)
    - [5]时段3: float64 (空值: 0)
    - [6]时段4: float64 (空值: 0)
    - [7]时段5: float64 (空值: 0)
    - [8时段6: float64 (空值: 0)
    - [9]时段7: float64 (空值: 0)
    - [10]时段8: float64 (空值: 0)
    - [11]时段9: float64 (空值: 0)
    - [12]时段10: float64 (空值: 0)
    - [13]时段11: float64 (空值: 0)
    - [14]时段12: float64 (空值: 0)
    - [15]时段13: float64 (空值: 0)
    - [16]时段14: float64 (空值: 0)
    - [17]时段15: float64 (空值: 0)
    - [18]时段16: float64 (空值: 0)
    - [19]时段17: float64 (空值: 0)
    - [20]时段18: float64 (空值: 0)
    - [21]时段19: float64 (空值: 0)
    - [22]时段20: float64 (空值: 0)
    - [23]时段21: float64 (空值: 0)
    - [24]时段22: float64 (空值: 0)
    - [25]时段23: float64 (空值: 0)
    - [26]时段24: float64 (空值: 0)
    - [27约束类型（-1，出力下限，0，指定出力，1出力上限）: int64 (空值: 0)

Sheet: 机组指定状态
  形状: (24, 4)
  列数: 4
  行数: 24

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]机组名称: object (空值: 0)
      唯一值数量: 24
    - [1]起始日期: int64 (空值: 0)
    - [2]结束日期: int64 (空值: 0)
    - [3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））: int64 (空值: 0)

Sheet: 线路
  形状: (9, 28)
  列数: 28
  行数: 9

  列信息:
    - 注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称: object (空值: 0)
      唯一值数量: 9
      唯一值: ['珠西南-区外AM', '粤东-珠东北', '粤东-珠东南', '粤西-珠西南', '珠东南-珠西南', '珠东北-珠西北', '粤西-珠西北', '粤北-珠西北', '珠东南-区外HK']
    - [1]原投产日期: int64 (空值: 0)
    - [2]原退役日期: int64 (空值: 0)
    - [3]起始节点: object (空值: 0)
      唯一值数量: 6
      唯一值: ['GD_ZXN', 'GD_YD', 'GD_YX', 'GD_ZDN', 'GD_ZDB', 'GD_YB']
    - [4]终止节点: object (空值: 0)
      唯一值数量: 6
      唯一值: ['QW_AM', 'GD_ZDB', 'GD_ZDN', 'GD_ZXN', 'GD_ZXB', 'QW_HK']
    - [5]线路区域: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认区域']
    - [6]线路电抗x: float64 (空值: 0)
    - [7]线路电阻R: float64 (空值: 0)
    - [8]线路充电电容C/2: float64 (空值: 0)
    - [9]传输容量（MVA）: int64 (空值: 0)
    - [10]线路热稳极限: int64 (空值: 0)
    - [11]新建/扩建/技改输变电投资（万元）: int64 (空值: 0)
    - [12]投资回收年限(年): int64 (空值: 0)
    - [13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器): int64 (空值: 0)
    - [14]该线路是否导入数据库: int64 (空值: 0)
    - [15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）: int64 (空值: 0)
    - [16]线路长度(km): int64 (空值: 0)
    - [17]线路跳闸率（次/年/百千米）: float64 (空值: 0)
    - [18]线路故障恢复时间（h）: int64 (空值: 0)
    - [19]重合闸成功率: int64 (空值: 0)
    - [20]线路暂态安全稳定极限(MW): int64 (空值: 0)
    - [21]一段继电保护平均动作时间（秒）: float64 (空值: 0)
    - [22]一段继电保护动作时间的标准差（秒）: float64 (空值: 0)
    - [23]线路三段距离继电保护整定值: int64 (空值: 0)
    - [24]保护拒动概率: int64 (空值: 0)
    - [25]保护误动概率: int64 (空值: 0)
    - [26]线路状态(0停运；1正常；2非正常): int64 (空值: 0)
    - [27]线路故障率增益: int64 (空值: 0)

Sheet: 线路区域
  形状: (1, 1)
  列数: 1
  行数: 1

  列信息:
    - 注：该表格中数据仅用于系统运行模拟[0]线路区域名称: object (空值: 0)
      唯一值数量: 1
      唯一值: ['默认区域']

Sheet: 负荷曲线
  形状: (1095, 27)
  列数: 27
  行数: 1095

  列信息:
    - [0]地区: object (空值: 0)
      唯一值数量: 3
      唯一值: ['外送', '广东', '系统']
    - [1]开始日期: int64 (空值: 0)
    - [2]结束日期: int64 (空值: 0)
    - [1~25]时段1~24: float64 (空值: 0)
    - [1~25]时段1~24.1: float64 (空值: 0)
    - [2~25]时段1~24: float64 (空值: 0)
    - [2~25]时段1~24.1: float64 (空值: 0)
    - [2~25]时段1~24.2: float64 (空值: 0)
    - [2~25]时段1~24.3: float64 (空值: 0)
    - [2~25]时段1~24.4: float64 (空值: 0)
    - [2~25]时段1~24.5: float64 (空值: 0)
    - [2~25]时段1~24.6: float64 (空值: 0)
    - [2~25]时段1~24.7: float64 (空值: 0)
    - [2~25]时段1~24.8: float64 (空值: 0)
    - [2~25]时段1~24.9: float64 (空值: 0)
    - [2~25]时段1~24.10: float64 (空值: 0)
    - [2~25]时段1~24.11: float64 (空值: 0)
    - [2~25]时段1~24.12: float64 (空值: 0)
    - [2~25]时段1~24.13: float64 (空值: 0)
    - [2~25]时段1~24.14: float64 (空值: 0)
    - [2~25]时段1~24.15: float64 (空值: 0)
    - [2~25]时段1~24.16: float64 (空值: 0)
    - [2~25]时段1~24.17: float64 (空值: 0)
    - [2~25]时段1~24.18: float64 (空值: 0)
    - [2~25]时段1~24.19: float64 (空值: 0)
    - [2~25]时段1~24.20: float64 (空值: 0)
    - [2~25]时段1~24.21: float64 (空值: 0)
