{"系统": {"sheet_name": "系统", "shape": [22, 48], "columns": ["参数类型号", "参数[1]", "参数[2]", "参数[3]", "参数[4]", "参数[5]", "参数[6]", "参数[7]", "参数[8]", "参数[9]", "参数[10]", "参数[11]", "参数[12]", "参数[13]", "参数[14]", "参数[15]", "参数[16]", "参数[17]", "参数[18]", "参数[19]", "参数[20]", "参数[21]", "参数[22]", "参数[23]", "参数[24]", "参数[25]", "参数[26]", "参数[27]", "参数[28]", "参数[29]", "参数[30]", "参数[31]", "参数[32]", "参数[33]", "参数[34]", "参数[35]", "参数[36]", "参数[37]", "参数[38]", "参数[39]", "参数[40]", "参数[41]", "参数[42]", "参数[43]", "参数[44]", "参数[45]", "参数[46]", "参数[47]"], "data_types": {"参数类型号": "object", "参数[1]": "object", "参数[2]": "object", "参数[3]": "object", "参数[4]": "object", "参数[5]": "object", "参数[6]": "object", "参数[7]": "object", "参数[8]": "object", "参数[9]": "object", "参数[10]": "object", "参数[11]": "object", "参数[12]": "object", "参数[13]": "object", "参数[14]": "object", "参数[15]": "object", "参数[16]": "object", "参数[17]": "object", "参数[18]": "object", "参数[19]": "object", "参数[20]": "object", "参数[21]": "object", "参数[22]": "object", "参数[23]": "object", "参数[24]": "object", "参数[25]": "object", "参数[26]": "object", "参数[27]": "object", "参数[28]": "object", "参数[29]": "object", "参数[30]": "object", "参数[31]": "object", "参数[32]": "object", "参数[33]": "object", "参数[34]": "object", "参数[35]": "object", "参数[36]": "object", "参数[37]": "object", "参数[38]": "object", "参数[39]": "object", "参数[40]": "object", "参数[41]": "object", "参数[42]": "object", "参数[43]": "object", "参数[44]": "object", "参数[45]": "object", "参数[46]": "object", "参数[47]": "object"}, "null_counts": {"参数类型号": 0, "参数[1]": 0, "参数[2]": 0, "参数[3]": 0, "参数[4]": 0, "参数[5]": 0, "参数[6]": 0, "参数[7]": 0, "参数[8]": 0, "参数[9]": 2, "参数[10]": 4, "参数[11]": 4, "参数[12]": 4, "参数[13]": 6, "参数[14]": 6, "参数[15]": 6, "参数[16]": 8, "参数[17]": 8, "参数[18]": 8, "参数[19]": 8, "参数[20]": 8, "参数[21]": 10, "参数[22]": 12, "参数[23]": 12, "参数[24]": 12, "参数[25]": 12, "参数[26]": 12, "参数[27]": 12, "参数[28]": 16, "参数[29]": 18, "参数[30]": 18, "参数[31]": 18, "参数[32]": 18, "参数[33]": 18, "参数[34]": 18, "参数[35]": 18, "参数[36]": 18, "参数[37]": 18, "参数[38]": 18, "参数[39]": 18, "参数[40]": 18, "参数[41]": 18, "参数[42]": 20, "参数[43]": 20, "参数[44]": 20, "参数[45]": 20, "参数[46]": 20, "参数[47]": 20}, "sample_data": [{"参数类型号": "电源规划优化", "参数[1]": "规划开始年", "参数[2]": "规划中止日期", "参数[3]": "贴现率", "参数[4]": "正备用率", "参数[5]": "负备用率", "参数[6]": "是否更新电源计划", "参数[7]": "运行约束考虑方式（0：考虑年最大最小负荷；1：考虑月最大最小负荷）", "参数[8]": "年最大最小负荷日设定方式（0：自动检索，1：人工指定）", "参数[9]": "最大负荷日", "参数[10]": "最小负荷日", "参数[11]": "月最大最小负荷日设定方式（0：自动检索，1：人工指定）", "参数[12]": "最大负荷日", "参数[13]": "最小负荷日", "参数[14]": "可再生能源运行方式是否为确定性出力（0：聚类后典型出力；1：确定性出力）", "参数[15]": "峰荷时段出力", "参数[16]": "谷荷时段出力", "参数[17]": "是否考虑运行维护成本", "参数[18]": "是否考虑总煤耗约束", "参数[19]": "是否考虑潮流约束（0：否，1：是）", "参数[20]": "新建机组投资调整量", "参数[21]": "负荷调整量", "参数[22]": "系统备用率调整量", "参数[23]": "燃煤约束调整量", "参数[24]": "线路传输容量调整量", "参数[25]": "平均网损率", "参数[26]": "最大迭代次数", "参数[27]": "最大计算时间（秒）", "参数[28]": "整数计算间隙", "参数[29]": NaN, "参数[30]": NaN, "参数[31]": NaN, "参数[32]": NaN, "参数[33]": NaN, "参数[34]": NaN, "参数[35]": NaN, "参数[36]": NaN, "参数[37]": NaN, "参数[38]": NaN, "参数[39]": NaN, "参数[40]": NaN, "参数[41]": NaN, "参数[42]": NaN, "参数[43]": NaN, "参数[44]": NaN, "参数[45]": NaN, "参数[46]": NaN, "参数[47]": NaN}, {"参数类型号": "1", "参数[1]": "2000", "参数[2]": "2050", "参数[3]": "0.08", "参数[4]": "0.15", "参数[5]": "0.15", "参数[6]": "0", "参数[7]": "0", "参数[8]": "0", "参数[9]": "20100701", "参数[10]": "20100101", "参数[11]": "0", "参数[12]": "-19999986", "参数[13]": "-20000000", "参数[14]": "0", "参数[15]": "0", "参数[16]": "25", "参数[17]": "1", "参数[18]": "1", "参数[19]": "0", "参数[20]": "0", "参数[21]": "0", "参数[22]": "0", "参数[23]": "0", "参数[24]": "0", "参数[25]": "0", "参数[26]": "200000", "参数[27]": "1800", "参数[28]": "0.0001", "参数[29]": NaN, "参数[30]": NaN, "参数[31]": NaN, "参数[32]": NaN, "参数[33]": NaN, "参数[34]": NaN, "参数[35]": NaN, "参数[36]": NaN, "参数[37]": NaN, "参数[38]": NaN, "参数[39]": NaN, "参数[40]": NaN, "参数[41]": NaN, "参数[42]": NaN, "参数[43]": NaN, "参数[44]": NaN, "参数[45]": NaN, "参数[46]": NaN, "参数[47]": NaN}, {"参数类型号": "电网规划优化参数", "参数[1]": "规划起始年份", "参数[2]": "规划结束年份", "参数[3]": "是否考虑线路故障（0：否，1：是）", "参数[4]": "是否考虑系统稳定（0：否，1：是）", "参数[5]": "是否考虑网络简化（0：否，1：是）", "参数[6]": "负荷采样方式（0：概率采样；1：时序采样）", "参数[7]": "迭代收敛精度", "参数[8]": "迭代收敛最大次数", "参数[9]": "迭代收敛最长时间(秒)", "参数[10]": "规划期贴现率", "参数[11]": "线路投资成本调整量(亿元)", "参数[12]": "线路经济传输容量调整量(MW)", "参数[13]": "线路极限传输容量调整量(MW)", "参数[14]": "系统负荷调整量(MW)", "参数[15]": "是否考虑系统网损（0：否，1：是）", "参数[16]": "是否考虑系统碳排放（0：否，1：是）", "参数[17]": "是否考虑系统发电成本（0：否，1：是）", "参数[18]": "规划期系统碳价(元/吨)", "参数[19]": "规划期系统发电成本(元/kWh)", "参数[20]": "规划期系统碳排放强度(吨/MWh)", "参数[21]": "是否考虑目标规划（0：否，1：是）", "参数[22]": "新能源出力方式（0：聚类典型出力；1：确定性出力）", "参数[23]": "新能源低谷出力", "参数[24]": "新能源高峰出力", "参数[25]": "是否考虑N-1约束（0：否，1：是）", "参数[26]": "是否输出潮流（0：否，1：是）", "参数[27]": "是否更新电网规划方案（0：否，1：是）", "参数[28]": NaN, "参数[29]": NaN, "参数[30]": NaN, "参数[31]": NaN, "参数[32]": NaN, "参数[33]": NaN, "参数[34]": NaN, "参数[35]": NaN, "参数[36]": NaN, "参数[37]": NaN, "参数[38]": NaN, "参数[39]": NaN, "参数[40]": NaN, "参数[41]": NaN, "参数[42]": NaN, "参数[43]": NaN, "参数[44]": NaN, "参数[45]": NaN, "参数[46]": NaN, "参数[47]": NaN}, {"参数类型号": "2", "参数[1]": "2000", "参数[2]": "2050", "参数[3]": "0", "参数[4]": "0", "参数[5]": "0", "参数[6]": "1", "参数[7]": "0.01", "参数[8]": "3000", "参数[9]": "5", "参数[10]": "0.08", "参数[11]": "0", "参数[12]": "0", "参数[13]": "0", "参数[14]": "0", "参数[15]": "1", "参数[16]": "1", "参数[17]": "1", "参数[18]": "100", "参数[19]": "0.3", "参数[20]": "0.7", "参数[21]": "0", "参数[22]": "0", "参数[23]": "0.5", "参数[24]": "0.5", "参数[25]": "0", "参数[26]": "1", "参数[27]": "0", "参数[28]": NaN, "参数[29]": NaN, "参数[30]": NaN, "参数[31]": NaN, "参数[32]": NaN, "参数[33]": NaN, "参数[34]": NaN, "参数[35]": NaN, "参数[36]": NaN, "参数[37]": NaN, "参数[38]": NaN, "参数[39]": NaN, "参数[40]": NaN, "参数[41]": NaN, "参数[42]": NaN, "参数[43]": NaN, "参数[44]": NaN, "参数[45]": NaN, "参数[46]": NaN, "参数[47]": NaN}, {"参数类型号": "电力系统调度运行仿真参数", "参数[1]": "运行模拟起始日期", "参数[2]": "运行模拟结束日期", "参数[3]": "运行模拟优化目标（0：运行成本最小，1：购电费用最小，2：三公调度，3：节能发电调度）", "参数[4]": "正备用率（%）", "参数[5]": "正备用预留方式（0－不考虑；1－全系统；2－分区域留取；3-每机组留取）", "参数[6]": "负备用率（%）", "参数[7]": "负备用预留方式（0－不考虑；1－全系统；2－分区域留取；3-每机组留取）", "参数[8]": "是否考虑线路约束（0：否，1：是）", "参数[9]": "是否考虑断面约束（0：否，1：是）", "参数[10]": "平均网损率", "参数[11]": "厂用电率（这个参数不在界面提供接口）", "参数[12]": "负荷类型（0：发电负荷；1：供电负荷）（这个参数不在界面提供接口）", "参数[13]": "抽蓄工作方式（0：优先做备用；1：优先抽水发电:2：优化安排出力）", "参数[14]": "抽水蓄能机组在周优化运行分配的容量比例（0~1）", "参数[15]": "新能源运行方式（0：随机模拟出力，1：按外部输入出力安排，2，按固定出力安排）", "参数[16]": "新能源峰荷出力（0~1之间的数）", "参数[17]": "新能源谷荷出力（0~1之间的数）", "参数[18]": "是否考虑水电中长期运行模拟（0：不考虑，1：考虑）", "参数[19]": "水电约束考虑方式（0：考虑电量约束，1：考虑水量约束）", "参数[20]": "是否考虑梯级协调（0：不考虑，1：考虑）", "参数[21]": "弃风/弃光顺序（不允许弃风/光时该措施该值为-1）", "参数[22]": "弃水顺序（不允许弃水时该措施该值为-1）", "参数[23]": "单次计算迭代时间限制（秒）", "参数[24]": "最大迭代次数（次）", "参数[25]": "优化精度", "参数[26]": "调峰或备用不足时是否可以去除备用（0：否，1：是）", "参数[27]": "是否进行检修模拟（0：否，1：是）", "参数[28]": "检修优化目标（0：等备用；1：等备用率；2：等风险度）", "参数[29]": "检修安排方式（0：按等效检修天数安排；1：考虑机组大修小修）", "参数[30]": "深度调峰门槛设定（%）（小于这个最低负荷率则采用深度调峰措施）", "参数[31]": "燃机启停调峰措施对应顺序（不采用该措施该值为-1）", "参数[32]": "燃机最小开机时间（小时）", "参数[33]": "火电机组深度调峰措施对应顺序（不采用该措施该值为-1）", "参数[34]": "火电机组最小负荷率（0~1之间的数）", "参数[35]": "火电机组启停调峰措施对应顺序（不采用该措施该值为-1）", "参数[36]": "火电机组启停的容量级别（MW）（安排小于等于该容量的煤电机组启停）", "参数[37]": "核电机组降低出力运行措施对应顺序（不采用该措施该值为-1）", "参数[38]": "是否考虑排放惩罚（0：否，1：是）", "参数[39]": "排放惩罚设定（元/吨CO2）", "参数[40]": "是否考虑网损修正（0：否，1：是）", "参数[41]": "核电机组运行方式（0：按额定容量运行，1：调峰容量不足时安排降出力运行）", "参数[42]": "系统停机备用率", "参数[43]": "是否考虑日内滚动优化，0：不考虑；1：考虑", "参数[44]": "是否允许分布式光伏弃用（0：不允许；1：允许）", "参数[45]": "是否联动燃气机组（0：否，1：是）", "参数[46]": "是否鼓励直流（0：否，1：是）", "参数[47]": "系统最小惯量约束(小于1e-3：不考虑，其他:单位秒) "}], "unique_values": {"参数类型号": {"count": 22, "sample_values": ["电源规划优化", "1", "电网规划优化参数", "2", "电力系统调度运行仿真参数", "3", "水电运行模拟参数", "4", "风险评估参数", "5"]}, "参数[1]": {"count": 13, "sample_values": ["规划开始年", "2000", "规划起始年份", "运行模拟起始日期", "20300101", "水电模拟起始时间", "20000101", "评估开始时间", "模拟起始时间", "评估起始时间"]}, "参数[2]": {"count": 12, "sample_values": ["规划中止日期", "2050", "规划结束年份", "运行模拟结束日期", "20301231", "水电模拟终止时间", "20500101", "评估终止时间", "模拟终止时间", "2049"]}, "参数[3]": {"count": 14, "sample_values": ["贴现率", "0.08", "是否考虑线路故障（0：否，1：是）", "0", "运行模拟优化目标（0：运行成本最小，1：购电费用最小，2：三公调度，3：节能发电调度）", "迭代收敛精度", "0.01", "是否考虑线路约束（0：否，1：是）", "模拟场景数", "1"]}, "参数[4]": {"count": 18, "sample_values": ["正备用率", "0.15", "是否考虑系统稳定（0：否，1：是）", "0", "正备用率（%）", "0.07", "迭代收敛最大次数", "3000", "是否考虑断面约束（0：否，1：是）", "是否模拟风电预测出力（0：否，1：是）"]}, "参数[5]": {"count": 17, "sample_values": ["负备用率", "0.15", "是否考虑网络简化（0：否，1：是）", "0", "正备用预留方式（0－不考虑；1－全系统；2－分区域留取；3-每机组留取）", "1", "迭代收敛最长时间(秒)", "5", "是否考虑机组停运（0：否，1：是）", "是否考虑风电场空间相关性（0：否，1：是）"]}, "参数[6]": {"count": 15, "sample_values": ["是否更新电源计划", "0", "负荷采样方式（0：概率采样；1：时序采样）", "1", "负备用率（%）", "0.03", "是否考虑梯级电站之间的协调（0：否，1：是）", "是否考虑线路停运（0：否，1：是）", "是否考虑风电机组可靠性（0：否，1：是）", "可靠性参考指标(0为LOLP，1为EENS)"]}, "参数[7]": {"count": 14, "sample_values": ["运行约束考虑方式（0：考虑年最大最小负荷；1：考虑月最大最小负荷）", "0", "迭代收敛精度", "0.01", "负备用预留方式（0－不考虑；1－全系统；2－分区域留取；3-每机组留取）", "1", "是否考虑水库最大用水量（0：否，1：是）", "是否考虑节点停运（0：否，1：是）", "是否考虑风电尾流效应（0：否，1：是）", "序列运算离散化步长(MW)"]}, "参数[8]": {"count": 16, "sample_values": ["年最大最小负荷日设定方式（0：自动检索，1：人工指定）", "0", "迭代收敛最大次数", "3000", "是否考虑线路约束（0：否，1：是）", "1", "是否考虑水库蒸发率（0：否，1：是）", "收敛判定条件（0：LOLP，1：EENS，2：风险损失）", "是否考虑风速日特性（0：否，1：是）", "迭代收敛精度"]}, "参数[9]": {"count": 16, "sample_values": ["最大负荷日", "20100701", "迭代收敛最长时间(秒)", "5", "是否考虑断面约束（0：否，1：是）", "0", "优化运行目标（0：发电量最大化；1：考虑水电机组调峰效益）", "计算目标精度", "0.05", "是否考虑风速季节特性（0：否，1：是）"]}, "参数[10]": {"count": 15, "sample_values": ["最小负荷日", "20100101", "规划期贴现率", "0.08", "平均网损率", "0.02", "是否跨流域之间出力协调（0：否，1：是）", "1", "计算时间限值", "1000"]}, "参数[11]": {"count": 14, "sample_values": ["月最大最小负荷日设定方式（0：自动检索，1：人工指定）", "0", "线路投资成本调整量(亿元)", "厂用电率（这个参数不在界面提供接口）", "0.06", "水电总出力占系统负荷比例上限", "1", "计算次数限值", "10000", "平均风速调整量（m/s）"]}, "参数[12]": {"count": 13, "sample_values": ["最大负荷日", "-19999986", "线路经济传输容量调整量(MW)", "0", "负荷类型（0：发电负荷；1：供电负荷）（这个参数不在界面提供接口）", "水电总出力占系统负荷比例下限", "收敛置信度", "0.05", "预测误差概率分布类型(0，出力预测误差服从正态分布，1，风速预测误差服从正态分布)", "风电出力数据来源(0为通过模拟，1为按指定出力)"]}, "参数[13]": {"count": 12, "sample_values": ["最小负荷日", "-20000000", "线路极限传输容量调整量(MW)", "0", "抽蓄工作方式（0：优先做备用；1：优先抽水发电:2：优化安排出力）", "2", "输出采样最严重故障数量", "预测误差一致性(0, 各时段预测误差标准差相同，1，各时段预测误差逐渐增加)", "是否考虑风电与负荷时序对应(0为考虑时序，1为不考虑时序)", "随机数种子类型(0, 随机种子，1，固定种子)"]}, "参数[14]": {"count": 10, "sample_values": ["可再生能源运行方式是否为确定性出力（0：聚类后典型出力；1：确定性出力）", "0", "系统负荷调整量(MW)", "抽水蓄能机组在周优化运行分配的容量比例（0~1）", "采样故障统计参数1", "1", "随机数种子类型(0, 随机种子，1，固定种子)", "是否考虑风电场空间相关性（0：否，1：是）", "是否考虑检修（0：否，1：是）", "随机数种子类型"]}, "参数[15]": {"count": 11, "sample_values": ["峰荷时段出力", "0", "是否考虑系统网损（0：否，1：是）", "1", "新能源运行方式（0：随机模拟出力，1：按外部输入出力安排，2，按固定出力安排）", "采样故障统计参数2", "2", "随机数种子", "3", "是否考虑风电机组可靠性（0：否，1：是）"]}, "参数[16]": {"count": 10, "sample_values": ["谷荷时段出力", "25", "是否考虑系统碳排放（0：否，1：是）", "1", "新能源峰荷出力（0~1之间的数）", "0", "是否合并统一节点机组加速计算（0：否，1：是）", "是否考虑风电尾流效应（0：否，1：是）", "是否考虑调频约束（0：否，1：是）", "光伏装机容量调整量(%)"]}, "参数[17]": {"count": 10, "sample_values": ["是否考虑运行维护成本", "1", "是否考虑系统发电成本（0：否，1：是）", "新能源谷荷出力（0~1之间的数）", "0.5", "是否故障集匹配加速计算（0：否，1：是）", "0", "是否考虑风速日特性（0：否，1：是）", "是否考虑负荷跟踪约束（0：否，1：是）", "常规机组强迫停运率调整量(%)"]}, "参数[18]": {"count": 10, "sample_values": ["是否考虑总煤耗约束", "1", "规划期系统碳价(元/吨)", "100", "是否考虑水电中长期运行模拟（0：不考虑，1：考虑）", "0", "重要性采样参数（0：不采用重要性采样，1：无自适应，2：一般自适应，3：最优自适应）", "是否考虑风速季节特性（0：否，1：是）", "是否考虑网络约束（0：否，1：是）", "负荷调整量(%)"]}, "参数[19]": {"count": 10, "sample_values": ["是否考虑潮流约束（0：否，1：是）", "0", "规划期系统发电成本(元/kWh)", "0.3", "水电约束考虑方式（0：考虑电量约束，1：考虑水量约束）", "随机数种子类型（0：随机种子，1：固定种子）", "1", "风机功率曲线模式（0，直线型，1，三次曲线型）", "是否考虑断面约束（0：否，1：是）", "线路容量调整量(%)"]}, "参数[20]": {"count": 12, "sample_values": ["新建机组投资调整量", "0", "规划期系统碳排放强度(吨/MWh)", "0.7", "是否考虑梯级协调（0：不考虑，1：考虑）", "1", "随机数种子固定值", "3", "平均风速调整量(MW)", "正备用(%)"]}, "参数[21]": {"count": 9, "sample_values": ["负荷调整量", "0", "是否考虑目标规划（0：否，1：是）", "弃风/弃光顺序（不允许弃风/光时该措施该值为-1）", "1", "新能源发电选项（0：不考虑新能源出力，1：考虑新能源平均出力，2考虑新能源随机出力）", "随机数种子类型(0, 随机种子，1，固定种子)", "负备用(%)", "0.02"]}, "参数[22]": {"count": 8, "sample_values": ["系统备用率调整量", "0", "新能源出力方式（0：聚类典型出力；1：确定性出力）", "弃水顺序（不允许弃水时该措施该值为-1）", "2", "机组停运概率选项（0：直接获取停运概率，1：通过停运率计算停运概率）", "随机数种子", "3"]}, "参数[23]": {"count": 8, "sample_values": ["燃煤约束调整量", "0", "新能源低谷出力", "0.5", "单次计算迭代时间限制（秒）", "300", "线路停运概率选项（0：直接获取停运概率，1：通过停运率计算停运概率）", "风电装机容量调整量"]}, "参数[24]": {"count": 8, "sample_values": ["线路传输容量调整量", "0", "新能源高峰出力", "0.5", "最大迭代次数（次）", "3000", "是否考虑系统稳定性（0：否，1：是）", "常规机组强迫停运率调整量"]}, "参数[25]": {"count": 8, "sample_values": ["平均网损率", "0", "是否考虑N-1约束（0：否，1：是）", "优化精度", "0.01", "功率基值", "100", "负荷调整量"]}, "参数[26]": {"count": 8, "sample_values": ["最大迭代次数", "200000", "是否输出潮流（0：否，1：是）", "1", "调峰或备用不足时是否可以去除备用（0：否，1：是）", "是否考虑安控措施（0：否，1：是）", "0", "线路容量调整量"]}, "参数[27]": {"count": 8, "sample_values": ["最大计算时间（秒）", "1800", "是否更新电网规划方案（0：否，1：是）", "0", "是否进行检修模拟（0：否，1：是）", "2", "是否考虑连锁故障（0：否，1：是）", "线路强迫停运率调整量"]}, "参数[28]": {"count": 5, "sample_values": ["整数计算间隙", "0.0001", "检修优化目标（0：等备用；1：等备用率；2：等风险度）", "0", "是否考虑同杆并架线路（0：否，1：是）"]}, "参数[29]": {"count": 3, "sample_values": ["检修安排方式（0：按等效检修天数安排；1：考虑机组大修小修）", "0", "是否考虑设备老化（0：否，1：是）"]}, "参数[30]": {"count": 3, "sample_values": ["深度调峰门槛设定（%）（小于这个最低负荷率则采用深度调峰措施）", "0", "灾害发生情况（用4位2进制表示，0：不考虑灾害，最低位表示雷电，次低位表示污闪，次高位表示冰害，最高位表示鸟害）"]}, "参数[31]": {"count": 4, "sample_values": ["燃机启停调峰措施对应顺序（不采用该措施该值为-1）", "-1", "灾区内故障相关性类型（0：不考虑相关性，1：外部给定，2：强相关，3：中相关，4：弱相关）", "0"]}, "参数[32]": {"count": 4, "sample_values": ["燃机最小开机时间（小时）", "6", "节点停运概率选项（0：直接获取停运概率，1：通过停运率计算停运概率）", "0"]}, "参数[33]": {"count": 4, "sample_values": ["火电机组深度调峰措施对应顺序（不采用该措施该值为-1）", "-1", "负荷不确定性考虑方式（0：仅考虑最高负荷，1：仅考虑平均负荷，2：考虑时序负荷）", "0"]}, "参数[34]": {"count": 4, "sample_values": ["火电机组最小负荷率（0~1之间的数）", "0.3", " 负荷不确定性选项（0：不考虑不确定性，1：考虑各区域负荷服从独立的正态分布，2：考虑各区域负荷服从非独立正态分布）", "0"]}, "参数[35]": {"count": 4, "sample_values": ["火电机组启停调峰措施对应顺序（不采用该措施该值为-1）", "-1", "机组停运率调整幅度", "0"]}, "参数[36]": {"count": 4, "sample_values": ["火电机组启停的容量级别（MW）（安排小于等于该容量的煤电机组启停）", "300", "线路停运率调整幅度", "0"]}, "参数[37]": {"count": 4, "sample_values": ["核电机组降低出力运行措施对应顺序（不采用该措施该值为-1）", "-1", "节点停运率调整幅度", "0"]}, "参数[38]": {"count": 3, "sample_values": ["是否考虑排放惩罚（0：否，1：是）", "0", "负荷倍数调整幅度"]}, "参数[39]": {"count": 4, "sample_values": ["排放惩罚设定（元/吨CO2）", "20", "单次优化计算最大迭代次数（次）", "3000"]}, "参数[40]": {"count": 4, "sample_values": ["是否考虑网损修正（0：否，1：是）", "0", "单次优化计算迭代时间限制（秒）", "5"]}, "参数[41]": {"count": 4, "sample_values": ["核电机组运行方式（0：按额定容量运行，1：调峰容量不足时安排降出力运行）", "0", "单次优化计算精度", "0.01"]}, "参数[42]": {"count": 2, "sample_values": ["系统停机备用率", "0.05"]}, "参数[43]": {"count": 2, "sample_values": ["是否考虑日内滚动优化，0：不考虑；1：考虑", "0"]}, "参数[44]": {"count": 2, "sample_values": ["是否允许分布式光伏弃用（0：不允许；1：允许）", "0"]}, "参数[45]": {"count": 2, "sample_values": ["是否联动燃气机组（0：否，1：是）", "0"]}, "参数[46]": {"count": 2, "sample_values": ["是否鼓励直流（0：否，1：是）", "0"]}, "参数[47]": {"count": 2, "sample_values": ["系统最小惯量约束(小于1e-3：不考虑，其他:单位秒) ", "0"]}}, "column_analysis": {"参数类型号": {"is_categorical": false, "has_chinese": true}, "参数[1]": {"is_categorical": false, "has_chinese": true}, "参数[2]": {"is_categorical": false, "has_chinese": true}, "参数[3]": {"is_categorical": false, "has_chinese": true}, "参数[4]": {"is_categorical": false, "has_chinese": true}, "参数[5]": {"is_categorical": false, "has_chinese": true}, "参数[6]": {"is_categorical": false, "has_chinese": true}, "参数[7]": {"is_categorical": false, "has_chinese": true}, "参数[8]": {"is_categorical": false, "has_chinese": true}, "参数[9]": {"is_categorical": false, "has_chinese": true}, "参数[10]": {"is_categorical": false, "has_chinese": true}, "参数[11]": {"is_categorical": false, "has_chinese": true}, "参数[12]": {"is_categorical": false, "has_chinese": true}, "参数[13]": {"is_categorical": false, "has_chinese": true}, "参数[14]": {"is_categorical": false, "has_chinese": true}, "参数[15]": {"is_categorical": false, "has_chinese": true}, "参数[16]": {"is_categorical": false, "has_chinese": true}, "参数[17]": {"is_categorical": false, "has_chinese": true}, "参数[18]": {"is_categorical": false, "has_chinese": true}, "参数[19]": {"is_categorical": false, "has_chinese": true}, "参数[20]": {"is_categorical": false, "has_chinese": true}, "参数[21]": {"is_categorical": false, "has_chinese": true}, "参数[22]": {"is_categorical": false, "has_chinese": true}, "参数[23]": {"is_categorical": false, "has_chinese": true}, "参数[24]": {"is_categorical": false, "has_chinese": true}, "参数[25]": {"is_categorical": false, "has_chinese": true}, "参数[26]": {"is_categorical": false, "has_chinese": true}, "参数[27]": {"is_categorical": false, "has_chinese": true}, "参数[28]": {"is_categorical": false, "has_chinese": true}, "参数[29]": {"is_categorical": false, "has_chinese": true}, "参数[30]": {"is_categorical": false, "has_chinese": true}, "参数[31]": {"is_categorical": false, "has_chinese": true}, "参数[32]": {"is_categorical": false, "has_chinese": true}, "参数[33]": {"is_categorical": false, "has_chinese": true}, "参数[34]": {"is_categorical": false, "has_chinese": true}, "参数[35]": {"is_categorical": false, "has_chinese": true}, "参数[36]": {"is_categorical": false, "has_chinese": true}, "参数[37]": {"is_categorical": false, "has_chinese": true}, "参数[38]": {"is_categorical": false, "has_chinese": true}, "参数[39]": {"is_categorical": false, "has_chinese": true}, "参数[40]": {"is_categorical": false, "has_chinese": true}, "参数[41]": {"is_categorical": false, "has_chinese": true}, "参数[42]": {"is_categorical": true, "has_chinese": true}, "参数[43]": {"is_categorical": true, "has_chinese": true}, "参数[44]": {"is_categorical": true, "has_chinese": true}, "参数[45]": {"is_categorical": true, "has_chinese": true}, "参数[46]": {"is_categorical": true, "has_chinese": true}, "参数[47]": {"is_categorical": true, "has_chinese": true}}}, "机组报价": {"sheet_name": "机组报价", "shape": [718, 26], "columns": ["注：该表格中数据仅用于系统运行模拟[0]机组名称", "[1]起始日期", "[2]时段1", "[3]时段2", "[4]时段3", "[5]时段4", "[6]时段5", "[7]时段6", "[8]时段7", "[9]时段8", "[10]时段9", "[11]时段10", "[12]时段11", "[13]时段12", "[14]时段13", "[15]时段14", "[16]时段15", "[17]时段16", "[18]时段17", "[19]时段18", "[20]时段19", "[21]时段20", "[22]时段21", "[23]时段22", "[24]时段23", "[25]时段24"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "object", "[1]起始日期": "int64", "[2]时段1": "float64", "[3]时段2": "float64", "[4]时段3": "float64", "[5]时段4": "float64", "[6]时段5": "float64", "[7]时段6": "float64", "[8]时段7": "float64", "[9]时段8": "float64", "[10]时段9": "float64", "[11]时段10": "float64", "[12]时段11": "float64", "[13]时段12": "float64", "[14]时段13": "float64", "[15]时段14": "float64", "[16]时段15": "float64", "[17]时段16": "float64", "[18]时段17": "float64", "[19]时段18": "float64", "[20]时段19": "float64", "[21]时段20": "float64", "[22]时段21": "float64", "[23]时段22": "float64", "[24]时段23": "float64", "[25]时段24": "float64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": 0, "[1]起始日期": 0, "[2]时段1": 0, "[3]时段2": 0, "[4]时段3": 0, "[5]时段4": 0, "[6]时段5": 0, "[7]时段6": 0, "[8]时段7": 0, "[9]时段8": 0, "[10]时段9": 0, "[11]时段10": 0, "[12]时段11": 0, "[13]时段12": 0, "[14]时段13": 0, "[15]时段14": 0, "[16]时段15": 0, "[17]时段16": 0, "[18]时段17": 0, "[19]时段18": 0, "[20]时段19": 0, "[21]时段20": 0, "[22]时段21": 0, "[23]时段22": 0, "[24]时段23": 0, "[25]时段24": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]机组名称": "XN#中洞蓄能#1", "[1]起始日期": 20300101, "[2]时段1": 0.5, "[3]时段2": 0.5, "[4]时段3": 0.5, "[5]时段4": 0.5, "[6]时段5": 0.5, "[7]时段6": 0.5, "[8]时段7": 0.5, "[9]时段8": 0.5, "[10]时段9": 0.5, "[11]时段10": 0.5, "[12]时段11": 0.5, "[13]时段12": 0.5, "[14]时段13": 0.5, "[15]时段14": 0.5, "[16]时段15": 0.5, "[17]时段16": 0.5, "[18]时段17": 0.5, "[19]时段18": 0.5, "[20]时段19": 0.5, "[21]时段20": 0.5, "[22]时段21": 0.5, "[23]时段22": 0.5, "[24]时段23": 0.5, "[25]时段24": 0.5}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "XN#中洞蓄能#2", "[1]起始日期": 20300101, "[2]时段1": 0.5, "[3]时段2": 0.5, "[4]时段3": 0.5, "[5]时段4": 0.5, "[6]时段5": 0.5, "[7]时段6": 0.5, "[8]时段7": 0.5, "[9]时段8": 0.5, "[10]时段9": 0.5, "[11]时段10": 0.5, "[12]时段11": 0.5, "[13]时段12": 0.5, "[14]时段13": 0.5, "[15]时段14": 0.5, "[16]时段15": 0.5, "[17]时段16": 0.5, "[18]时段17": 0.5, "[19]时段18": 0.5, "[20]时段19": 0.5, "[21]时段20": 0.5, "[22]时段21": 0.5, "[23]时段22": 0.5, "[24]时段23": 0.5, "[25]时段24": 0.5}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "XN#中洞蓄能#3", "[1]起始日期": 20300101, "[2]时段1": 0.5, "[3]时段2": 0.5, "[4]时段3": 0.5, "[5]时段4": 0.5, "[6]时段5": 0.5, "[7]时段6": 0.5, "[8]时段7": 0.5, "[9]时段8": 0.5, "[10]时段9": 0.5, "[11]时段10": 0.5, "[12]时段11": 0.5, "[13]时段12": 0.5, "[14]时段13": 0.5, "[15]时段14": 0.5, "[16]时段15": 0.5, "[17]时段16": 0.5, "[18]时段17": 0.5, "[19]时段18": 0.5, "[20]时段19": 0.5, "[21]时段20": 0.5, "[22]时段21": 0.5, "[23]时段22": 0.5, "[24]时段23": 0.5, "[25]时段24": 0.5}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "XN#梅州蓄能#1", "[1]起始日期": 20300101, "[2]时段1": 0.5, "[3]时段2": 0.5, "[4]时段3": 0.5, "[5]时段4": 0.5, "[6]时段5": 0.5, "[7]时段6": 0.5, "[8]时段7": 0.5, "[9]时段8": 0.5, "[10]时段9": 0.5, "[11]时段10": 0.5, "[12]时段11": 0.5, "[13]时段12": 0.5, "[14]时段13": 0.5, "[15]时段14": 0.5, "[16]时段15": 0.5, "[17]时段16": 0.5, "[18]时段17": 0.5, "[19]时段18": 0.5, "[20]时段19": 0.5, "[21]时段20": 0.5, "[22]时段21": 0.5, "[23]时段22": 0.5, "[24]时段23": 0.5, "[25]时段24": 0.5}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "XN#梅州蓄能#2", "[1]起始日期": 20300101, "[2]时段1": 0.5, "[3]时段2": 0.5, "[4]时段3": 0.5, "[5]时段4": 0.5, "[6]时段5": 0.5, "[7]时段6": 0.5, "[8]时段7": 0.5, "[9]时段8": 0.5, "[10]时段9": 0.5, "[11]时段10": 0.5, "[12]时段11": 0.5, "[13]时段12": 0.5, "[14]时段13": 0.5, "[15]时段14": 0.5, "[16]时段15": 0.5, "[17]时段16": 0.5, "[18]时段17": 0.5, "[19]时段18": 0.5, "[20]时段19": 0.5, "[21]时段20": 0.5, "[22]时段21": 0.5, "[23]时段22": 0.5, "[24]时段23": 0.5, "[25]时段24": 0.5}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": {"count": 718, "sample_values": ["XN#中洞蓄能#1", "XN#中洞蓄能#2", "XN#中洞蓄能#3", "XN#梅州蓄能#1", "XN#梅州蓄能#2", "XN#梅州蓄能#3", "XN#梅州蓄能#4", "XN#浪江蓄能#1", "XN#浪江蓄能#2", "XN#浪江蓄能#3"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": {"is_categorical": false, "has_chinese": true}}}, "水电三段式出力": {"sheet_name": "水电三段式出力", "shape": [78, 16], "columns": ["电厂名称", "起始时间", "输入类型A（1：枯水年；2：平水年；3：丰水年）", "输入类型B（1：预想出力；2：平均出力；3：强迫出力）", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"], "data_types": {"电厂名称": "object", "起始时间": "int64", "输入类型A（1：枯水年；2：平水年；3：丰水年）": "int64", "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": "int64", "1月": "float64", "2月": "float64", "3月": "float64", "4月": "float64", "5月": "float64", "6月": "float64", "7月": "float64", "8月": "float64", "9月": "float64", "10月": "float64", "11月": "float64", "12月": "float64"}, "null_counts": {"电厂名称": 0, "起始时间": 0, "输入类型A（1：枯水年；2：平水年；3：丰水年）": 0, "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": 0, "1月": 0, "2月": 0, "3月": 0, "4月": 0, "5月": 0, "6月": 0, "7月": 0, "8月": 0, "9月": 0, "10月": 0, "11月": 0, "12月": 0}, "sample_data": [{"电厂名称": "新丰江", "起始时间": 2030, "输入类型A（1：枯水年；2：平水年；3：丰水年）": 1, "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": 1, "1月": 200.0, "2月": 172.0, "3月": 195.0, "4月": 195.0, "5月": 241.0, "6月": 214.0, "7月": 241.0, "8月": 241.0, "9月": 190.0, "10月": 215.0, "11月": 227.0, "12月": 223.0}, {"电厂名称": "新丰江", "起始时间": 2030, "输入类型A（1：枯水年；2：平水年；3：丰水年）": 1, "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": 2, "1月": 91.0, "2月": 99.0, "3月": 108.0, "4月": 125.0, "5月": 86.0, "6月": 82.0, "7月": 89.0, "8月": 73.0, "9月": 76.0, "10月": 73.0, "11月": 85.0, "12月": 80.0}, {"电厂名称": "新丰江", "起始时间": 2030, "输入类型A（1：枯水年；2：平水年；3：丰水年）": 1, "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": 3, "1月": 0.0, "2月": 0.0, "3月": 0.0, "4月": 21.0, "5月": 50.0, "6月": 45.0, "7月": 72.0, "8月": 57.0, "9月": 21.0, "10月": 43.0, "11月": 29.0, "12月": 25.0}, {"电厂名称": "新丰江", "起始时间": 2030, "输入类型A（1：枯水年；2：平水年；3：丰水年）": 2, "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": 1, "1月": 279.0, "2月": 253.0, "3月": 220.0, "4月": 242.0, "5月": 234.0, "6月": 242.0, "7月": 254.0, "8月": 278.0, "9月": 241.0, "10月": 259.0, "11月": 259.0, "12月": 259.0}, {"电厂名称": "新丰江", "起始时间": 2030, "输入类型A（1：枯水年；2：平水年；3：丰水年）": 2, "输入类型B（1：预想出力；2：平均出力；3：强迫出力）": 2, "1月": 94.0, "2月": 98.0, "3月": 82.0, "4月": 154.0, "5月": 101.0, "6月": 139.0, "7月": 146.0, "8月": 144.0, "9月": 68.0, "10月": 96.0, "11月": 84.0, "12月": 96.0}], "unique_values": {"电厂名称": {"count": 13, "sample_values": ["新丰江", "长湖水电厂", "飞来峡", "乐昌峡", "青溪电站", "枫树坝", "粤北小水电", "粤西小水电", "粤东小水电", "珠东北小水电"]}}, "column_analysis": {"电厂名称": {"is_categorical": false, "has_chinese": true}}}, "风电场": {"sheet_name": "风电场", "shape": [17, 11], "columns": ["注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）", "[2]风机切入风速", "[3]风机额定风速", "[4]风机切出风速", "[5]风电场尾流系数", "[6]风机可用率", "[7]风电预测绝对误差占装机容量的百分比", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": "object", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": "object", "[2]风机切入风速": "float64", "[3]风机额定风速": "int64", "[4]风机切出风速": "int64", "[5]风电场尾流系数": "float64", "[6]风机可用率": "float64", "[7]风电预测绝对误差占装机容量的百分比": "float64", "Unnamed: 8": "float64", "Unnamed: 9": "float64", "Unnamed: 10": "float64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": 0, "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": 0, "[2]风机切入风速": 0, "[3]风机额定风速": 0, "[4]风机切出风速": 0, "[5]风电场尾流系数": 0, "[6]风机可用率": 0, "[7]风电预测绝对误差占装机容量的百分比": 0, "Unnamed: 8": 17, "Unnamed: 9": 17, "Unnamed: 10": 17}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": "粤东风电深海", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": "默认风区", "[2]风机切入风速": 3.5, "[3]风机额定风速": 12, "[4]风机切出风速": 25, "[5]风电场尾流系数": 0.98, "[6]风机可用率": 0.95, "[7]风电预测绝对误差占装机容量的百分比": 0.15, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": "粤西风电深海", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": "默认风区", "[2]风机切入风速": 3.5, "[3]风机额定风速": 12, "[4]风机切出风速": 25, "[5]风电场尾流系数": 0.98, "[6]风机可用率": 0.95, "[7]风电预测绝对误差占装机容量的百分比": 0.15, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": "珠东南风电深海", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": "默认风区", "[2]风机切入风速": 3.5, "[3]风机额定风速": 12, "[4]风机切出风速": 25, "[5]风电场尾流系数": 0.98, "[6]风机可用率": 0.95, "[7]风电预测绝对误差占装机容量的百分比": 0.15, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": "珠西北风电深海", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": "默认风区", "[2]风机切入风速": 3.5, "[3]风机额定风速": 12, "[4]风机切出风速": 25, "[5]风电场尾流系数": 0.98, "[6]风机可用率": 0.95, "[7]风电预测绝对误差占装机容量的百分比": 0.15, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}, {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": "珠西南风电深海", "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": "默认风区", "[2]风机切入风速": 3.5, "[3]风机额定风速": 12, "[4]风机切出风速": 25, "[5]风电场尾流系数": 0.98, "[6]风机可用率": 0.95, "[7]风电预测绝对误差占装机容量的百分比": 0.15, "Unnamed: 8": NaN, "Unnamed: 9": NaN, "Unnamed: 10": NaN}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": {"count": 17, "sample_values": ["粤东风电深海", "粤西风电深海", "珠东南风电深海", "珠西北风电深海", "珠西南风电深海", "粤东风电近海", "粤西风电近海", "珠东南风电近海", "珠西北风电近海", "珠西南风电近海"]}, "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": {"count": 1, "sample_values": ["默认风区"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）": {"is_categorical": false, "has_chinese": true}, "[1]所在风区名称（注：该表中的风区名称需与风区信息表中的一致）": {"is_categorical": true, "has_chinese": true}}}, "风区信息": {"sheet_name": "风区信息", "shape": [1, 41], "columns": ["注：该表格中数据仅用于系统运行模拟[0]风区名称", "起始时间", "[1]风速Weibull分布参数c", "[2]风速Weibull分布参数k", "[3]风速自相关函数衰减系数", "此列开始为风区各月平均风速（标幺值）[4]1月", "[5]2月", "[6]3月", "[7]4月", "[8]5月", "[9]6月", "[10]7月", "[11]8月", "[12]9月", "[13]10月", "[14]11月", "[15]12月", "此列开始为风区日内各时段平均风速（标幺值）[16]时段1", "[17]时段2", "[18]时段3", "[19]时段4", "[20]时段5", "[21]时段6", "[22]时段7", "[23]时段8", "[24]时段9", "[25]时段10", "[26]时段11", "[27]时段12", "[28]时段13", "[29]时段14", "[30]时段15", "[31]时段16", "[32]时段17", "[33]时段18", "[34]时段19", "[35]时段20", "[36]时段21", "[37]时段22", "[38]时段23", "[39]时段24"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": "object", "起始时间": "int64", "[1]风速Weibull分布参数c": "float64", "[2]风速Weibull分布参数k": "float64", "[3]风速自相关函数衰减系数": "float64", "此列开始为风区各月平均风速（标幺值）[4]1月": "float64", "[5]2月": "float64", "[6]3月": "float64", "[7]4月": "float64", "[8]5月": "float64", "[9]6月": "float64", "[10]7月": "float64", "[11]8月": "float64", "[12]9月": "float64", "[13]10月": "float64", "[14]11月": "float64", "[15]12月": "float64", "此列开始为风区日内各时段平均风速（标幺值）[16]时段1": "float64", "[17]时段2": "float64", "[18]时段3": "float64", "[19]时段4": "float64", "[20]时段5": "float64", "[21]时段6": "float64", "[22]时段7": "float64", "[23]时段8": "float64", "[24]时段9": "float64", "[25]时段10": "float64", "[26]时段11": "float64", "[27]时段12": "float64", "[28]时段13": "float64", "[29]时段14": "float64", "[30]时段15": "float64", "[31]时段16": "float64", "[32]时段17": "float64", "[33]时段18": "float64", "[34]时段19": "float64", "[35]时段20": "float64", "[36]时段21": "float64", "[37]时段22": "float64", "[38]时段23": "float64", "[39]时段24": "float64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": 0, "起始时间": 0, "[1]风速Weibull分布参数c": 0, "[2]风速Weibull分布参数k": 0, "[3]风速自相关函数衰减系数": 0, "此列开始为风区各月平均风速（标幺值）[4]1月": 0, "[5]2月": 0, "[6]3月": 0, "[7]4月": 0, "[8]5月": 0, "[9]6月": 0, "[10]7月": 0, "[11]8月": 0, "[12]9月": 0, "[13]10月": 0, "[14]11月": 0, "[15]12月": 0, "此列开始为风区日内各时段平均风速（标幺值）[16]时段1": 0, "[17]时段2": 0, "[18]时段3": 0, "[19]时段4": 0, "[20]时段5": 0, "[21]时段6": 0, "[22]时段7": 0, "[23]时段8": 0, "[24]时段9": 0, "[25]时段10": 0, "[26]时段11": 0, "[27]时段12": 0, "[28]时段13": 0, "[29]时段14": 0, "[30]时段15": 0, "[31]时段16": 0, "[32]时段17": 0, "[33]时段18": 0, "[34]时段19": 0, "[35]时段20": 0, "[36]时段21": 0, "[37]时段22": 0, "[38]时段23": 0, "[39]时段24": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]风区名称": "默认风区", "起始时间": 20300101, "[1]风速Weibull分布参数c": 5.805480868, "[2]风速Weibull分布参数k": 2.374547193, "[3]风速自相关函数衰减系数": 0.05, "此列开始为风区各月平均风速（标幺值）[4]1月": 0.897874464, "[5]2月": 1.020311891, "[6]3月": 1.123314805, "[7]4月": 1.173844537, "[8]5月": 1.129145159, "[9]6月": 0.98727322, "[10]7月": 0.950347647, "[11]8月": 0.975612512, "[12]9月": 0.899817915, "[13]10月": 1.065011269, "[14]11月": 0.897874464, "[15]12月": 0.875850874, "此列开始为风区日内各时段平均风速（标幺值）[16]时段1": 0.915927062, "[17]时段2": 0.915579036, "[18]时段3": 0.918380522, "[19]时段4": 0.911131649, "[20]时段5": 0.904268401, "[21]时段6": 0.905661296, "[22]时段7": 0.914424616, "[23]时段8": 0.950485225, "[24]时段9": 0.996799268, "[25]时段10": 1.066773234, "[26]时段11": 1.137886072, "[27]时段12": 1.179061679, "[28]时段13": 1.204959651, "[29]时段14": 1.209337538, "[30]时段15": 1.196415804, "[31]时段16": 1.139915819, "[32]时段17": 1.009950659, "[33]时段18": 0.944412332, "[34]时段19": 0.947929308, "[35]时段20": 0.94198327, "[36]时段21": 0.935269099, "[37]时段22": 0.919709557, "[38]时段23": 0.91829549, "[39]时段24": 0.91421777}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": {"count": 1, "sample_values": ["默认风区"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": {"is_categorical": false, "has_chinese": true}}}, "风区之间相关系数": {"sheet_name": "风区之间相关系数", "shape": [1, 2], "columns": ["注：该表格中数据仅用于系统运行模拟[0]风区名称", "默认风区"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": "object", "默认风区": "int64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": 0, "默认风区": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]风区名称": "默认风区", "默认风区": 1}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": {"count": 1, "sample_values": ["默认风区"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]风区名称": {"is_categorical": false, "has_chinese": true}}}, "风电备用": {"sheet_name": "风电备用", "shape": [20, 3], "columns": ["[0]风电出力占装机比例（%）", "[1]正备用（%）", "[2]负备用（%）"], "data_types": {"[0]风电出力占装机比例（%）": "int64", "[1]正备用（%）": "int64", "[2]负备用（%）": "int64"}, "null_counts": {"[0]风电出力占装机比例（%）": 0, "[1]正备用（%）": 0, "[2]负备用（%）": 0}, "sample_data": [{"[0]风电出力占装机比例（%）": 5, "[1]正备用（%）": 1, "[2]负备用（%）": 24}, {"[0]风电出力占装机比例（%）": 10, "[1]正备用（%）": 5, "[2]负备用（%）": 27}, {"[0]风电出力占装机比例（%）": 15, "[1]正备用（%）": 8, "[2]负备用（%）": 29}, {"[0]风电出力占装机比例（%）": 20, "[1]正备用（%）": 13, "[2]负备用（%）": 30}, {"[0]风电出力占装机比例（%）": 25, "[1]正备用（%）": 15, "[2]负备用（%）": 29}], "unique_values": {}, "column_analysis": {}}, "光伏电站": {"sheet_name": "光伏电站", "shape": [7, 8], "columns": ["注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)", "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)", "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)", "[5]光伏组件单元个数", "[6]光伏板可用率（0~1之间的数）", "[7]预测绝对误差占装机容量的百分比"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": "object", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": "object", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": "int64", "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": "int64", "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": "int64", "[5]光伏组件单元个数": "int64", "[6]光伏板可用率（0~1之间的数）": "int64", "[7]预测绝对误差占装机容量的百分比": "float64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": 0, "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": 0, "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": 0, "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": 0, "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": 0, "[5]光伏组件单元个数": 0, "[6]光伏板可用率（0~1之间的数）": 0, "[7]预测绝对误差占装机容量的百分比": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": "GF#粤北光伏发电#1", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": "默认光区", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": 0, "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": 28, "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": 0, "[5]光伏组件单元个数": 36, "[6]光伏板可用率（0~1之间的数）": 1, "[7]预测绝对误差占装机容量的百分比": 0.15}, {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": "GF#粤东光伏发电#1", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": "默认光区", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": 0, "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": 28, "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": 0, "[5]光伏组件单元个数": 36, "[6]光伏板可用率（0~1之间的数）": 1, "[7]预测绝对误差占装机容量的百分比": 0.15}, {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": "GF#粤西光伏发电#1", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": "默认光区", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": 0, "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": 28, "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": 0, "[5]光伏组件单元个数": 36, "[6]光伏板可用率（0~1之间的数）": 1, "[7]预测绝对误差占装机容量的百分比": 0.15}, {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": "GF#珠东北光伏发电#1", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": "默认光区", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": 0, "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": 28, "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": 0, "[5]光伏组件单元个数": 36, "[6]光伏板可用率（0~1之间的数）": 1, "[7]预测绝对误差占装机容量的百分比": 0.15}, {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": "GF#珠东南光伏发电#1", "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": "默认光区", "[2]光伏阵列类型(0:固定倾角 1：水平单轴 2：倾角单轴 3：双轴)": 0, "[3]光伏阵列倾斜角度，仅对固定倾角以及单轴适用，北半球默认朝南，南半球默认朝北（°)": 28, "[4]光伏阵列方向角，北半球：与正南方的夹角，向东为负，向西为正，南半球：与正北方的夹角，向东为负，由向西为正，对于固定倾角表示固定板方向角，对于水平单轴和倾角单轴表明跟踪轴方位角，不适用于双轴（°)": 0, "[5]光伏组件单元个数": 36, "[6]光伏板可用率（0~1之间的数）": 1, "[7]预测绝对误差占装机容量的百分比": 0.15}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": {"count": 7, "sample_values": ["GF#粤北光伏发电#1", "GF#粤东光伏发电#1", "GF#粤西光伏发电#1", "GF#珠东北光伏发电#1", "GF#珠东南光伏发电#1", "GF#珠西北光伏发电#1", "GF#珠西南光伏发电#1"]}, "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": {"count": 1, "sample_values": ["默认光区"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）": {"is_categorical": false, "has_chinese": true}, "[1]所在光区名称（注：该表中的光区名称需与光区信息表中的一致）": {"is_categorical": false, "has_chinese": true}}}, "光区信息": {"sheet_name": "光区信息", "shape": [1, 19], "columns": ["注：该表格中数据仅用于系统运行模拟[0]光区名称", "起始时间", "[1]晴空指数概率分布参数C", "[2]晴空指数概率分布参数lamda", "[3]晴空指数概率分布参数ktu", "[4]地理位置--经度", "[5]地理位置--纬度", "[6]地理位置--海拔高度", "[7]地面反射率", "[8]大气散射系数p", "[9]大气散射系数q", "[10]天气类型1概率（晴）", "[11]天气类型2概率（多云、雾)", "[12]天气类型3概率（阴、小雨、小雪、冻雨）", "[13]天气类型4概率（中雨、大雨、暴雨、大暴雨、特大暴雨、中雪、大雪、暴雪、沙尘暴）", "[14]天气类型5概率（晴间多云、多云间晴）", "[15]天气类型6概率（阴间多云、多云间阴）", "[16]天气类型7概率（阵雨、雨夹雪、雷阵雨、雷阵雨伴有冰雹、阵雪、小到中雨、小到中雪）", "[17]天气类型8概率（中到大雨、大到暴雨、暴雨到大暴雨、大暴雨到特大暴雨、中到大雪、大到暴雪）"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": "object", "起始时间": "int64", "[1]晴空指数概率分布参数C": "float64", "[2]晴空指数概率分布参数lamda": "float64", "[3]晴空指数概率分布参数ktu": "float64", "[4]地理位置--经度": "float64", "[5]地理位置--纬度": "float64", "[6]地理位置--海拔高度": "float64", "[7]地面反射率": "float64", "[8]大气散射系数p": "int64", "[9]大气散射系数q": "int64", "[10]天气类型1概率（晴）": "float64", "[11]天气类型2概率（多云、雾)": "float64", "[12]天气类型3概率（阴、小雨、小雪、冻雨）": "float64", "[13]天气类型4概率（中雨、大雨、暴雨、大暴雨、特大暴雨、中雪、大雪、暴雪、沙尘暴）": "float64", "[14]天气类型5概率（晴间多云、多云间晴）": "float64", "[15]天气类型6概率（阴间多云、多云间阴）": "float64", "[16]天气类型7概率（阵雨、雨夹雪、雷阵雨、雷阵雨伴有冰雹、阵雪、小到中雨、小到中雪）": "float64", "[17]天气类型8概率（中到大雨、大到暴雨、暴雨到大暴雨、大暴雨到特大暴雨、中到大雪、大到暴雪）": "float64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": 0, "起始时间": 0, "[1]晴空指数概率分布参数C": 0, "[2]晴空指数概率分布参数lamda": 0, "[3]晴空指数概率分布参数ktu": 0, "[4]地理位置--经度": 0, "[5]地理位置--纬度": 0, "[6]地理位置--海拔高度": 0, "[7]地面反射率": 0, "[8]大气散射系数p": 0, "[9]大气散射系数q": 0, "[10]天气类型1概率（晴）": 0, "[11]天气类型2概率（多云、雾)": 0, "[12]天气类型3概率（阴、小雨、小雪、冻雨）": 0, "[13]天气类型4概率（中雨、大雨、暴雨、大暴雨、特大暴雨、中雪、大雪、暴雪、沙尘暴）": 0, "[14]天气类型5概率（晴间多云、多云间晴）": 0, "[15]天气类型6概率（阴间多云、多云间阴）": 0, "[16]天气类型7概率（阵雨、雨夹雪、雷阵雨、雷阵雨伴有冰雹、阵雪、小到中雨、小到中雪）": 0, "[17]天气类型8概率（中到大雨、大到暴雨、暴雨到大暴雨、大暴雨到特大暴雨、中到大雪、大到暴雪）": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]光区名称": "默认光区", "起始时间": 20300101, "[1]晴空指数概率分布参数C": 0.2994, "[2]晴空指数概率分布参数lamda": 5.062, "[3]晴空指数概率分布参数ktu": 0.0343, "[4]地理位置--经度": 119.95, "[5]地理位置--纬度": 31.79, "[6]地理位置--海拔高度": 5.3, "[7]地面反射率": 0.2, "[8]大气散射系数p": 1, "[9]大气散射系数q": 1, "[10]天气类型1概率（晴）": 0.04, "[11]天气类型2概率（多云、雾)": 0.07, "[12]天气类型3概率（阴、小雨、小雪、冻雨）": 0.25, "[13]天气类型4概率（中雨、大雨、暴雨、大暴雨、特大暴雨、中雪、大雪、暴雪、沙尘暴）": 0.04, "[14]天气类型5概率（晴间多云、多云间晴）": 0.04, "[15]天气类型6概率（阴间多云、多云间阴）": 0.3, "[16]天气类型7概率（阵雨、雨夹雪、雷阵雨、雷阵雨伴有冰雹、阵雪、小到中雨、小到中雪）": 0.11, "[17]天气类型8概率（中到大雨、大到暴雨、暴雨到大暴雨、大暴雨到特大暴雨、中到大雪、大到暴雪）": 0.15}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": {"count": 1, "sample_values": ["默认光区"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": {"is_categorical": false, "has_chinese": true}}}, "光区之间相关系数": {"sheet_name": "光区之间相关系数", "shape": [1, 2], "columns": ["注：该表格中数据仅用于系统运行模拟[0]光区名称", "默认光区"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": "object", "默认光区": "int64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": 0, "默认光区": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]光区名称": "默认光区", "默认光区": 1}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": {"count": 1, "sample_values": ["默认光区"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]光区名称": {"is_categorical": false, "has_chinese": true}}}, "节点": {"sheet_name": "节点", "shape": [9, 10], "columns": ["注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称", "[1]投产日期", "[2]退役日期", "[3]节点负荷占总负荷的比例(%)", "[4]节点固定负荷(MW)", "[5]节点所属大区", "[6]节点切除负荷损失(元/kWh)", "[7]节点强迫停运率", "[8]节点故障恢复时间", "[9]供电区域"], "data_types": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": "object", "[1]投产日期": "int64", "[2]退役日期": "int64", "[3]节点负荷占总负荷的比例(%)": "float64", "[4]节点固定负荷(MW)": "int64", "[5]节点所属大区": "object", "[6]节点切除负荷损失(元/kWh)": "int64", "[7]节点强迫停运率": "int64", "[8]节点故障恢复时间": "int64", "[9]供电区域": "object"}, "null_counts": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": 0, "[1]投产日期": 0, "[2]退役日期": 0, "[3]节点负荷占总负荷的比例(%)": 0, "[4]节点固定负荷(MW)": 0, "[5]节点所属大区": 0, "[6]节点切除负荷损失(元/kWh)": 0, "[7]节点强迫停运率": 0, "[8]节点故障恢复时间": 0, "[9]供电区域": 0}, "sample_data": [{"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": "GD_ZDN", "[1]投产日期": 20090101, "[2]退役日期": 21000101, "[3]节点负荷占总负荷的比例(%)": 16.6873, "[4]节点固定负荷(MW)": 0, "[5]节点所属大区": "广东", "[6]节点切除负荷损失(元/kWh)": 100000, "[7]节点强迫停运率": 0, "[8]节点故障恢复时间": 12, "[9]供电区域": "系统"}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": "GD_ZDB", "[1]投产日期": 20090101, "[2]退役日期": 21000101, "[3]节点负荷占总负荷的比例(%)": 16.2649, "[4]节点固定负荷(MW)": 0, "[5]节点所属大区": "广东", "[6]节点切除负荷损失(元/kWh)": 100000, "[7]节点强迫停运率": 0, "[8]节点故障恢复时间": 12, "[9]供电区域": "系统"}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": "GD_ZXN", "[1]投产日期": 20090101, "[2]退役日期": 21000101, "[3]节点负荷占总负荷的比例(%)": 18.9344, "[4]节点固定负荷(MW)": 0, "[5]节点所属大区": "广东", "[6]节点切除负荷损失(元/kWh)": 100000, "[7]节点强迫停运率": 0, "[8]节点故障恢复时间": 12, "[9]供电区域": "系统"}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": "GD_ZXB", "[1]投产日期": 20090101, "[2]退役日期": 21000101, "[3]节点负荷占总负荷的比例(%)": 22.8728, "[4]节点固定负荷(MW)": 0, "[5]节点所属大区": "广东", "[6]节点切除负荷损失(元/kWh)": 100000, "[7]节点强迫停运率": 0, "[8]节点故障恢复时间": 12, "[9]供电区域": "系统"}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": "GD_YD", "[1]投产日期": 20090101, "[2]退役日期": 21000101, "[3]节点负荷占总负荷的比例(%)": 13.345, "[4]节点固定负荷(MW)": 0, "[5]节点所属大区": "广东", "[6]节点切除负荷损失(元/kWh)": 100000, "[7]节点强迫停运率": 0, "[8]节点故障恢复时间": 12, "[9]供电区域": "系统"}], "unique_values": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": {"count": 9, "sample_values": ["GD_ZDN", "GD_ZDB", "GD_ZXN", "GD_ZXB", "GD_YD", "GD_YX", "GD_YB", "QW_AM", "QW_HK"]}, "[5]节点所属大区": {"count": 2, "sample_values": ["广东", "外送"]}, "[9]供电区域": {"count": 1, "sample_values": ["系统"]}}, "column_analysis": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]节点名称": {"is_categorical": false, "has_chinese": false}, "[5]节点所属大区": {"is_categorical": false, "has_chinese": true}, "[9]供电区域": {"is_categorical": false, "has_chinese": true}}}, "发电公司": {"sheet_name": "发电公司", "shape": [21, 1], "columns": ["注：该表格中数据仅用于系统运行模拟[0]发电公司名称"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": "object"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": "FD"}, {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": "FD_JH"}, {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": "FD_SH"}, {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": "GF"}, {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": "HD"}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": {"count": 21, "sample_values": ["FD", "FD_JH", "FD_SH", "GF", "HD", "LJ", "MD_1000", "MD_200", "MD_300", "MD_600"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]发电公司名称": {"is_categorical": false, "has_chinese": false}}}, "机组规划类型": {"sheet_name": "机组规划类型", "shape": [15, 11], "columns": ["注：该表格中数据仅用于电源规划优化计算[0]分类名称", "[1]是否可启停", "[2]单位启停费用（元/MW)", "[3]是否必开", "[4]利用小时数", "[5]最小出力率", "[6]厂用电", "[7]煤耗(g/kWh)", "[8]运行成本(元/kWh)", "[9]碳排放强度（tCO2/MWh）", "[10]冬季检修率"], "data_types": {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": "object", "[1]是否可启停": "int64", "[2]单位启停费用（元/MW)": "int64", "[3]是否必开": "int64", "[4]利用小时数": "int64", "[5]最小出力率": "float64", "[6]厂用电": "float64", "[7]煤耗(g/kWh)": "int64", "[8]运行成本(元/kWh)": "float64", "[9]碳排放强度（tCO2/MWh）": "float64", "[10]冬季检修率": "int64"}, "null_counts": {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": 0, "[1]是否可启停": 0, "[2]单位启停费用（元/MW)": 0, "[3]是否必开": 0, "[4]利用小时数": 0, "[5]最小出力率": 0, "[6]厂用电": 0, "[7]煤耗(g/kWh)": 0, "[8]运行成本(元/kWh)": 0, "[9]碳排放强度（tCO2/MWh）": 0, "[10]冬季检修率": 0}, "sample_data": [{"注：该表格中数据仅用于电源规划优化计算[0]分类名称": "核电", "[1]是否可启停": 0, "[2]单位启停费用（元/MW)": 0, "[3]是否必开": 1, "[4]利用小时数": 6500, "[5]最小出力率": 1.0, "[6]厂用电": 0.05, "[7]煤耗(g/kWh)": 0, "[8]运行成本(元/kWh)": 0.045, "[9]碳排放强度（tCO2/MWh）": 0.0, "[10]冬季检修率": 1}, {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": "热电", "[1]是否可启停": 0, "[2]单位启停费用（元/MW)": 0, "[3]是否必开": 1, "[4]利用小时数": 8000, "[5]最小出力率": 1.0, "[6]厂用电": 0.07, "[7]煤耗(g/kWh)": 340, "[8]运行成本(元/kWh)": 0.16675, "[9]碳排放强度（tCO2/MWh）": 0.76, "[10]冬季检修率": 1}, {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": "E级燃机", "[1]是否可启停": 0, "[2]单位启停费用（元/MW)": 0, "[3]是否必开": 0, "[4]利用小时数": 0, "[5]最小出力率": 0.0, "[6]厂用电": 0.02, "[7]煤耗(g/kWh)": 0, "[8]运行成本(元/kWh)": 0.3, "[9]碳排放强度（tCO2/MWh）": 0.1, "[10]冬季检修率": 1}, {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": "F级燃机", "[1]是否可启停": 1, "[2]单位启停费用（元/MW)": 0, "[3]是否必开": 1, "[4]利用小时数": 3500, "[5]最小出力率": 0.8, "[6]厂用电": 0.02, "[7]煤耗(g/kWh)": 0, "[8]运行成本(元/kWh)": 0.3, "[9]碳排放强度（tCO2/MWh）": 0.1, "[10]冬季检修率": 1}, {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": "抽蓄", "[1]是否可启停": 0, "[2]单位启停费用（元/MW)": 0, "[3]是否必开": 1, "[4]利用小时数": -250, "[5]最小出力率": -1.0, "[6]厂用电": 0.0, "[7]煤耗(g/kWh)": 0, "[8]运行成本(元/kWh)": 0.0, "[9]碳排放强度（tCO2/MWh）": 0.0, "[10]冬季检修率": 1}], "unique_values": {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": {"count": 15, "sample_values": ["核电", "热电", "E级燃机", "F级燃机", "抽蓄", "小火电", "中火电", "大火电", "水电", "风电"]}}, "column_analysis": {"注：该表格中数据仅用于电源规划优化计算[0]分类名称": {"is_categorical": false, "has_chinese": true}}}, "机组指定出力": {"sheet_name": "机组指定出力", "shape": [13976, 28], "columns": ["注：该表格中数据仅用于系统运行模拟[0]机组名称", "[1]起始日期", "[2]结束日期", "[3]时段1", "[4]时段2", "[5]时段3", "[6]时段4", "[7]时段5", "[8时段6", "[9]时段7", "[10]时段8", "[11]时段9", "[12]时段10", "[13]时段11", "[14]时段12", "[15]时段13", "[16]时段14", "[17]时段15", "[18]时段16", "[19]时段17", "[20]时段18", "[21]时段19", "[22]时段20", "[23]时段21", "[24]时段22", "[25]时段23", "[26]时段24", "[27约束类型（-1，出力下限，0，指定出力，1出力上限）"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "object", "[1]起始日期": "int64", "[2]结束日期": "int64", "[3]时段1": "float64", "[4]时段2": "float64", "[5]时段3": "float64", "[6]时段4": "float64", "[7]时段5": "float64", "[8时段6": "float64", "[9]时段7": "float64", "[10]时段8": "float64", "[11]时段9": "float64", "[12]时段10": "float64", "[13]时段11": "float64", "[14]时段12": "float64", "[15]时段13": "float64", "[16]时段14": "float64", "[17]时段15": "float64", "[18]时段16": "float64", "[19]时段17": "float64", "[20]时段18": "float64", "[21]时段19": "float64", "[22]时段20": "float64", "[23]时段21": "float64", "[24]时段22": "float64", "[25]时段23": "float64", "[26]时段24": "float64", "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": "int64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": 0, "[1]起始日期": 0, "[2]结束日期": 0, "[3]时段1": 0, "[4]时段2": 0, "[5]时段3": 0, "[6]时段4": 0, "[7]时段5": 0, "[8时段6": 0, "[9]时段7": 0, "[10]时段8": 0, "[11]时段9": 0, "[12]时段10": 0, "[13]时段11": 0, "[14]时段12": 0, "[15]时段13": 0, "[16]时段14": 0, "[17]时段15": 0, "[18]时段16": 0, "[19]时段17": 0, "[20]时段18": 0, "[21]时段19": 0, "[22]时段20": 0, "[23]时段21": 0, "[24]时段22": 0, "[25]时段23": 0, "[26]时段24": 0, "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]机组名称": "QD_RD#宝昌B厂#9", "[1]起始日期": 20300101, "[2]结束日期": 20301231, "[3]时段1": 150.0, "[4]时段2": 150.0, "[5]时段3": 150.0, "[6]时段4": 150.0, "[7]时段5": 150.0, "[8时段6": 150.0, "[9]时段7": 150.0, "[10]时段8": 150.0, "[11]时段9": 150.0, "[12]时段10": 150.0, "[13]时段11": 150.0, "[14]时段12": 150.0, "[15]时段13": 150.0, "[16]时段14": 150.0, "[17]时段15": 150.0, "[18]时段16": 150.0, "[19]时段17": 150.0, "[20]时段18": 150.0, "[21]时段19": 150.0, "[22]时段20": 150.0, "[23]时段21": 150.0, "[24]时段22": 150.0, "[25]时段23": 150.0, "[26]时段24": 150.0, "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": -1}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "QD_RD#宝昌B厂#11", "[1]起始日期": 20300101, "[2]结束日期": 20301231, "[3]时段1": 150.0, "[4]时段2": 150.0, "[5]时段3": 150.0, "[6]时段4": 150.0, "[7]时段5": 150.0, "[8时段6": 150.0, "[9]时段7": 150.0, "[10]时段8": 150.0, "[11]时段9": 150.0, "[12]时段10": 150.0, "[13]时段11": 150.0, "[14]时段12": 150.0, "[15]时段13": 150.0, "[16]时段14": 150.0, "[17]时段15": 150.0, "[18]时段16": 150.0, "[19]时段17": 150.0, "[20]时段18": 150.0, "[21]时段19": 150.0, "[22]时段20": 150.0, "[23]时段21": 150.0, "[24]时段22": 150.0, "[25]时段23": 150.0, "[26]时段24": 150.0, "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": -1}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "QD_RD#东莞高埗改扩建#1", "[1]起始日期": 20300101, "[2]结束日期": 20301231, "[3]时段1": 300.0, "[4]时段2": 300.0, "[5]时段3": 300.0, "[6]时段4": 300.0, "[7]时段5": 300.0, "[8时段6": 300.0, "[9]时段7": 300.0, "[10]时段8": 300.0, "[11]时段9": 300.0, "[12]时段10": 300.0, "[13]时段11": 300.0, "[14]时段12": 300.0, "[15]时段13": 300.0, "[16]时段14": 300.0, "[17]时段15": 300.0, "[18]时段16": 300.0, "[19]时段17": 300.0, "[20]时段18": 300.0, "[21]时段19": 300.0, "[22]时段20": 300.0, "[23]时段21": 300.0, "[24]时段22": 300.0, "[25]时段23": 300.0, "[26]时段24": 300.0, "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": -1}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "QD_RD#东莞高埗改扩建#2", "[1]起始日期": 20300101, "[2]结束日期": 20301231, "[3]时段1": 300.0, "[4]时段2": 300.0, "[5]时段3": 300.0, "[6]时段4": 300.0, "[7]时段5": 300.0, "[8时段6": 300.0, "[9]时段7": 300.0, "[10]时段8": 300.0, "[11]时段9": 300.0, "[12]时段10": 300.0, "[13]时段11": 300.0, "[14]时段12": 300.0, "[15]时段13": 300.0, "[16]时段14": 300.0, "[17]时段15": 300.0, "[18]时段16": 300.0, "[19]时段17": 300.0, "[20]时段18": 300.0, "[21]时段19": 300.0, "[22]时段20": 300.0, "[23]时段21": 300.0, "[24]时段22": 300.0, "[25]时段23": 300.0, "[26]时段24": 300.0, "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": -1}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "QD_RD#东莞洪梅天然气热电联产（科然）#1", "[1]起始日期": 20300101, "[2]结束日期": 20301231, "[3]时段1": 300.0, "[4]时段2": 300.0, "[5]时段3": 300.0, "[6]时段4": 300.0, "[7]时段5": 300.0, "[8时段6": 300.0, "[9]时段7": 300.0, "[10]时段8": 300.0, "[11]时段9": 300.0, "[12]时段10": 300.0, "[13]时段11": 300.0, "[14]时段12": 300.0, "[15]时段13": 300.0, "[16]时段14": 300.0, "[17]时段15": 300.0, "[18]时段16": 300.0, "[19]时段17": 300.0, "[20]时段18": 300.0, "[21]时段19": 300.0, "[22]时段20": 300.0, "[23]时段21": 300.0, "[24]时段22": 300.0, "[25]时段23": 300.0, "[26]时段24": 300.0, "[27约束类型（-1，出力下限，0，指定出力，1出力上限）": -1}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": {"count": 144, "sample_values": ["QD_RD#宝昌B厂#9", "QD_RD#宝昌B厂#11", "QD_RD#东莞高埗改扩建#1", "QD_RD#东莞高埗改扩建#2", "QD_RD#东莞洪梅天然气热电联产（科然）#1", "QD_RD#东莞洪梅天然气热电联产（科然）#2", "QD_RD#大唐高明燃气热电（鳌围）#1", "QD_RD#大唐高明燃气热电（鳌围）#3", "QD_RD#大唐惠州博罗燃气-蒸汽热电联产项目#1", "QD_RD#大唐惠州博罗燃气-蒸汽热电联产项目#2"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": {"is_categorical": false, "has_chinese": true}}}, "机组指定状态": {"sheet_name": "机组指定状态", "shape": [24, 4], "columns": ["注：该表格中数据仅用于系统运行模拟[0]机组名称", "[1]起始日期", "[2]结束日期", "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "object", "[1]起始日期": "int64", "[2]结束日期": "int64", "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": "int64"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": 0, "[1]起始日期": 0, "[2]结束日期": 0, "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]机组名称": "HD#阳江核电#1", "[1]起始日期": 20300101, "[2]结束日期": 20300228, "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": 0}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "HD#阳江核电#5", "[1]起始日期": 20300101, "[2]结束日期": 20300228, "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": 0}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "HD#陆丰核电5、6号机组#5", "[1]起始日期": 20300101, "[2]结束日期": 20300228, "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": 0}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "HD#陆丰核电1、2号机组#2", "[1]起始日期": 20300101, "[2]结束日期": 20300228, "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": 0}, {"注：该表格中数据仅用于系统运行模拟[0]机组名称": "HD#襟岛核电#1", "[1]起始日期": 20300101, "[2]结束日期": 20300228, "[3]指定状态（0－指定停机；1－指定开机（安排检修时仍为停机））": 0}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": {"count": 24, "sample_values": ["HD#阳江核电#1", "HD#阳江核电#5", "HD#陆丰核电5、6号机组#5", "HD#陆丰核电1、2号机组#2", "HD#襟岛核电#1", "HD#太平岭核电二期#4", "HD#岭澳核电二期B（岭东）#4", "HD#阳江核电#4", "HD#襟岛核电#2", "HD#太平岭核电一期#2"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]机组名称": {"is_categorical": false, "has_chinese": true}}}, "线路": {"sheet_name": "线路", "shape": [9, 28], "columns": ["注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称", "[1]原投产日期", "[2]原退役日期", "[3]起始节点", "[4]终止节点", "[5]线路区域", "[6]线路电抗x", "[7]线路电阻R", "[8]线路充电电容C/2", "[9]传输容量（MVA）", "[10]线路热稳极限", "[11]新建/扩建/技改输变电投资（万元）", "[12]投资回收年限(年)", "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)", "[14]该线路是否导入数据库", "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）", "[16]线路长度(km)", "[17]线路跳闸率（次/年/百千米）", "[18]线路故障恢复时间（h）", "[19]重合闸成功率", "[20]线路暂态安全稳定极限(MW)", "[21]一段继电保护平均动作时间（秒）", "[22]一段继电保护动作时间的标准差（秒）", "[23]线路三段距离继电保护整定值", "[24]保护拒动概率", "[25]保护误动概率", "[26]线路状态(0停运；1正常；2非正常)", "[27]线路故障率增益"], "data_types": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": "object", "[1]原投产日期": "int64", "[2]原退役日期": "int64", "[3]起始节点": "object", "[4]终止节点": "object", "[5]线路区域": "object", "[6]线路电抗x": "float64", "[7]线路电阻R": "float64", "[8]线路充电电容C/2": "float64", "[9]传输容量（MVA）": "int64", "[10]线路热稳极限": "int64", "[11]新建/扩建/技改输变电投资（万元）": "int64", "[12]投资回收年限(年)": "int64", "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": "int64", "[14]该线路是否导入数据库": "int64", "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": "int64", "[16]线路长度(km)": "int64", "[17]线路跳闸率（次/年/百千米）": "float64", "[18]线路故障恢复时间（h）": "int64", "[19]重合闸成功率": "int64", "[20]线路暂态安全稳定极限(MW)": "int64", "[21]一段继电保护平均动作时间（秒）": "float64", "[22]一段继电保护动作时间的标准差（秒）": "float64", "[23]线路三段距离继电保护整定值": "int64", "[24]保护拒动概率": "int64", "[25]保护误动概率": "int64", "[26]线路状态(0停运；1正常；2非正常)": "int64", "[27]线路故障率增益": "int64"}, "null_counts": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": 0, "[1]原投产日期": 0, "[2]原退役日期": 0, "[3]起始节点": 0, "[4]终止节点": 0, "[5]线路区域": 0, "[6]线路电抗x": 0, "[7]线路电阻R": 0, "[8]线路充电电容C/2": 0, "[9]传输容量（MVA）": 0, "[10]线路热稳极限": 0, "[11]新建/扩建/技改输变电投资（万元）": 0, "[12]投资回收年限(年)": 0, "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": 0, "[14]该线路是否导入数据库": 0, "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": 0, "[16]线路长度(km)": 0, "[17]线路跳闸率（次/年/百千米）": 0, "[18]线路故障恢复时间（h）": 0, "[19]重合闸成功率": 0, "[20]线路暂态安全稳定极限(MW)": 0, "[21]一段继电保护平均动作时间（秒）": 0, "[22]一段继电保护动作时间的标准差（秒）": 0, "[23]线路三段距离继电保护整定值": 0, "[24]保护拒动概率": 0, "[25]保护误动概率": 0, "[26]线路状态(0停运；1正常；2非正常)": 0, "[27]线路故障率增益": 0}, "sample_data": [{"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": "珠西南-区外AM", "[1]原投产日期": 20100101, "[2]原退役日期": 21000101, "[3]起始节点": "GD_ZXN", "[4]终止节点": "QW_AM", "[5]线路区域": "默认区域", "[6]线路电抗x": 0.28, "[7]线路电阻R": 0.03, "[8]线路充电电容C/2": 0.000359, "[9]传输容量（MVA）": 5000, "[10]线路热稳极限": 5000, "[11]新建/扩建/技改输变电投资（万元）": 0, "[12]投资回收年限(年)": 25, "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": 4, "[14]该线路是否导入数据库": 1, "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": 0, "[16]线路长度(km)": 100, "[17]线路跳闸率（次/年/百千米）": 0.15, "[18]线路故障恢复时间（h）": 10, "[19]重合闸成功率": 1, "[20]线路暂态安全稳定极限(MW)": 5000, "[21]一段继电保护平均动作时间（秒）": 0.1, "[22]一段继电保护动作时间的标准差（秒）": 0.02, "[23]线路三段距离继电保护整定值": 10000, "[24]保护拒动概率": 0, "[25]保护误动概率": 0, "[26]线路状态(0停运；1正常；2非正常)": 1, "[27]线路故障率增益": 5}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": "粤东-珠东北", "[1]原投产日期": 20100101, "[2]原退役日期": 21000101, "[3]起始节点": "GD_YD", "[4]终止节点": "GD_ZDB", "[5]线路区域": "默认区域", "[6]线路电抗x": 0.28, "[7]线路电阻R": 0.03, "[8]线路充电电容C/2": 0.000359, "[9]传输容量（MVA）": 9000, "[10]线路热稳极限": 9000, "[11]新建/扩建/技改输变电投资（万元）": 0, "[12]投资回收年限(年)": 25, "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": 4, "[14]该线路是否导入数据库": 1, "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": 0, "[16]线路长度(km)": 100, "[17]线路跳闸率（次/年/百千米）": 0.15, "[18]线路故障恢复时间（h）": 10, "[19]重合闸成功率": 1, "[20]线路暂态安全稳定极限(MW)": 9000, "[21]一段继电保护平均动作时间（秒）": 0.1, "[22]一段继电保护动作时间的标准差（秒）": 0.02, "[23]线路三段距离继电保护整定值": 18000, "[24]保护拒动概率": 0, "[25]保护误动概率": 0, "[26]线路状态(0停运；1正常；2非正常)": 1, "[27]线路故障率增益": 5}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": "粤东-珠东南", "[1]原投产日期": 20100101, "[2]原退役日期": 21000101, "[3]起始节点": "GD_YD", "[4]终止节点": "GD_ZDN", "[5]线路区域": "默认区域", "[6]线路电抗x": 0.28, "[7]线路电阻R": 0.03, "[8]线路充电电容C/2": 0.000359, "[9]传输容量（MVA）": 6000, "[10]线路热稳极限": 6000, "[11]新建/扩建/技改输变电投资（万元）": 0, "[12]投资回收年限(年)": 25, "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": 4, "[14]该线路是否导入数据库": 1, "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": 0, "[16]线路长度(km)": 100, "[17]线路跳闸率（次/年/百千米）": 0.15, "[18]线路故障恢复时间（h）": 10, "[19]重合闸成功率": 1, "[20]线路暂态安全稳定极限(MW)": 6000, "[21]一段继电保护平均动作时间（秒）": 0.1, "[22]一段继电保护动作时间的标准差（秒）": 0.02, "[23]线路三段距离继电保护整定值": 12000, "[24]保护拒动概率": 0, "[25]保护误动概率": 0, "[26]线路状态(0停运；1正常；2非正常)": 1, "[27]线路故障率增益": 5}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": "粤西-珠西南", "[1]原投产日期": 20100101, "[2]原退役日期": 21000101, "[3]起始节点": "GD_YX", "[4]终止节点": "GD_ZXN", "[5]线路区域": "默认区域", "[6]线路电抗x": 0.28, "[7]线路电阻R": 0.03, "[8]线路充电电容C/2": 0.000359, "[9]传输容量（MVA）": 6000, "[10]线路热稳极限": 6000, "[11]新建/扩建/技改输变电投资（万元）": 0, "[12]投资回收年限(年)": 25, "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": 4, "[14]该线路是否导入数据库": 1, "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": 0, "[16]线路长度(km)": 100, "[17]线路跳闸率（次/年/百千米）": 0.15, "[18]线路故障恢复时间（h）": 10, "[19]重合闸成功率": 1, "[20]线路暂态安全稳定极限(MW)": 6000, "[21]一段继电保护平均动作时间（秒）": 0.1, "[22]一段继电保护动作时间的标准差（秒）": 0.02, "[23]线路三段距离继电保护整定值": 12000, "[24]保护拒动概率": 0, "[25]保护误动概率": 0, "[26]线路状态(0停运；1正常；2非正常)": 1, "[27]线路故障率增益": 5}, {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": "珠东南-珠西南", "[1]原投产日期": 20100101, "[2]原退役日期": 21000101, "[3]起始节点": "GD_ZDN", "[4]终止节点": "GD_ZXN", "[5]线路区域": "默认区域", "[6]线路电抗x": 0.28, "[7]线路电阻R": 0.03, "[8]线路充电电容C/2": 0.000359, "[9]传输容量（MVA）": 3000, "[10]线路热稳极限": 3000, "[11]新建/扩建/技改输变电投资（万元）": 0, "[12]投资回收年限(年)": 25, "[13]线路类型(1:1000kV线路;2:750kV线路;3:500kV线路;4:330kV线路;5:220kV线路;6:110kV线路;7:66kV线路;8:35kV线路;9:10kV线路;20:直流线路;11:1000kV变压器;12:750kV变压器;13:500kV变压器;14:330kV变压器;15:220kV变压器;16:110kV变压器;17:66kV变压器;18:35kV变压器;19:10kV变压器)": 4, "[14]该线路是否导入数据库": 1, "[15]是否为规划线路（0：已投建 1：已定计划，2：未定计划）": 0, "[16]线路长度(km)": 100, "[17]线路跳闸率（次/年/百千米）": 0.15, "[18]线路故障恢复时间（h）": 10, "[19]重合闸成功率": 1, "[20]线路暂态安全稳定极限(MW)": 3000, "[21]一段继电保护平均动作时间（秒）": 0.1, "[22]一段继电保护动作时间的标准差（秒）": 0.02, "[23]线路三段距离继电保护整定值": 6000, "[24]保护拒动概率": 0, "[25]保护误动概率": 0, "[26]线路状态(0停运；1正常；2非正常)": 1, "[27]线路故障率增益": 5}], "unique_values": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": {"count": 9, "sample_values": ["珠西南-区外AM", "粤东-珠东北", "粤东-珠东南", "粤西-珠西南", "珠东南-珠西南", "珠东北-珠西北", "粤西-珠西北", "粤北-珠西北", "珠东南-区外HK"]}, "[3]起始节点": {"count": 6, "sample_values": ["GD_ZXN", "GD_YD", "GD_YX", "GD_ZDN", "GD_ZDB", "GD_YB"]}, "[4]终止节点": {"count": 6, "sample_values": ["QW_AM", "GD_ZDB", "GD_ZDN", "GD_ZXN", "GD_ZXB", "QW_HK"]}, "[5]线路区域": {"count": 1, "sample_values": ["默认区域"]}}, "column_analysis": {"注：该表格中数据用于系统运行模拟与电源规划优化计算[0]线路名称": {"is_categorical": false, "has_chinese": true}, "[3]起始节点": {"is_categorical": false, "has_chinese": false}, "[4]终止节点": {"is_categorical": false, "has_chinese": false}, "[5]线路区域": {"is_categorical": false, "has_chinese": true}}}, "线路区域": {"sheet_name": "线路区域", "shape": [1, 1], "columns": ["注：该表格中数据仅用于系统运行模拟[0]线路区域名称"], "data_types": {"注：该表格中数据仅用于系统运行模拟[0]线路区域名称": "object"}, "null_counts": {"注：该表格中数据仅用于系统运行模拟[0]线路区域名称": 0}, "sample_data": [{"注：该表格中数据仅用于系统运行模拟[0]线路区域名称": "默认区域"}], "unique_values": {"注：该表格中数据仅用于系统运行模拟[0]线路区域名称": {"count": 1, "sample_values": ["默认区域"]}}, "column_analysis": {"注：该表格中数据仅用于系统运行模拟[0]线路区域名称": {"is_categorical": false, "has_chinese": true}}}, "负荷曲线": {"sheet_name": "负荷曲线", "shape": [1095, 27], "columns": ["[0]地区", "[1]开始日期", "[2]结束日期", "[1~25]时段1~24", "[1~25]时段1~24.1", "[2~25]时段1~24", "[2~25]时段1~24.1", "[2~25]时段1~24.2", "[2~25]时段1~24.3", "[2~25]时段1~24.4", "[2~25]时段1~24.5", "[2~25]时段1~24.6", "[2~25]时段1~24.7", "[2~25]时段1~24.8", "[2~25]时段1~24.9", "[2~25]时段1~24.10", "[2~25]时段1~24.11", "[2~25]时段1~24.12", "[2~25]时段1~24.13", "[2~25]时段1~24.14", "[2~25]时段1~24.15", "[2~25]时段1~24.16", "[2~25]时段1~24.17", "[2~25]时段1~24.18", "[2~25]时段1~24.19", "[2~25]时段1~24.20", "[2~25]时段1~24.21"], "data_types": {"[0]地区": "object", "[1]开始日期": "int64", "[2]结束日期": "int64", "[1~25]时段1~24": "float64", "[1~25]时段1~24.1": "float64", "[2~25]时段1~24": "float64", "[2~25]时段1~24.1": "float64", "[2~25]时段1~24.2": "float64", "[2~25]时段1~24.3": "float64", "[2~25]时段1~24.4": "float64", "[2~25]时段1~24.5": "float64", "[2~25]时段1~24.6": "float64", "[2~25]时段1~24.7": "float64", "[2~25]时段1~24.8": "float64", "[2~25]时段1~24.9": "float64", "[2~25]时段1~24.10": "float64", "[2~25]时段1~24.11": "float64", "[2~25]时段1~24.12": "float64", "[2~25]时段1~24.13": "float64", "[2~25]时段1~24.14": "float64", "[2~25]时段1~24.15": "float64", "[2~25]时段1~24.16": "float64", "[2~25]时段1~24.17": "float64", "[2~25]时段1~24.18": "float64", "[2~25]时段1~24.19": "float64", "[2~25]时段1~24.20": "float64", "[2~25]时段1~24.21": "float64"}, "null_counts": {"[0]地区": 0, "[1]开始日期": 0, "[2]结束日期": 0, "[1~25]时段1~24": 0, "[1~25]时段1~24.1": 0, "[2~25]时段1~24": 0, "[2~25]时段1~24.1": 0, "[2~25]时段1~24.2": 0, "[2~25]时段1~24.3": 0, "[2~25]时段1~24.4": 0, "[2~25]时段1~24.5": 0, "[2~25]时段1~24.6": 0, "[2~25]时段1~24.7": 0, "[2~25]时段1~24.8": 0, "[2~25]时段1~24.9": 0, "[2~25]时段1~24.10": 0, "[2~25]时段1~24.11": 0, "[2~25]时段1~24.12": 0, "[2~25]时段1~24.13": 0, "[2~25]时段1~24.14": 0, "[2~25]时段1~24.15": 0, "[2~25]时段1~24.16": 0, "[2~25]时段1~24.17": 0, "[2~25]时段1~24.18": 0, "[2~25]时段1~24.19": 0, "[2~25]时段1~24.20": 0, "[2~25]时段1~24.21": 0}, "sample_data": [{"[0]地区": "外送", "[1]开始日期": 20300101, "[2]结束日期": 20300102, "[1~25]时段1~24": 1543.597860262, "[1~25]时段1~24.1": 1496.509894339, "[2~25]时段1~24": 1426.366389361, "[2~25]时段1~24.1": 1425.32371786, "[2~25]时段1~24.2": 1332.126318417, "[2~25]时段1~24.3": 1377.257219536, "[2~25]时段1~24.4": 1284.059820093, "[2~25]时段1~24.5": 1467.487774521, "[2~25]时段1~24.6": 1720.079056934, "[2~25]时段1~24.7": 1949.549016599, "[2~25]时段1~24.8": 2017.799595873, "[2~25]时段1~24.9": 1624.860453589, "[2~25]时段1~24.10": 1462.465189724, "[2~25]时段1~24.11": 1507.466168053, "[2~25]时段1~24.12": 1621.794933595, "[2~25]时段1~24.13": 1966.603438457, "[2~25]时段1~24.14": 2011.829725927, "[2~25]时段1~24.15": 1964.646373653, "[2~25]时段1~24.16": 2055.85546104, "[2~25]时段1~24.17": 1962.656417005, "[2~25]时段1~24.18": 1638.849355452, "[2~25]时段1~24.19": 1384.301008236, "[2~25]时段1~24.20": 1567.793101762, "[2~25]时段1~24.21": 1544.673423607}, {"[0]地区": "外送", "[1]开始日期": 20300102, "[2]结束日期": 20300103, "[1~25]时段1~24": 1543.597860262, "[1~25]时段1~24.1": 1496.509894339, "[2~25]时段1~24": 1426.366389361, "[2~25]时段1~24.1": 1425.32371786, "[2~25]时段1~24.2": 1332.126318417, "[2~25]时段1~24.3": 1377.257219536, "[2~25]时段1~24.4": 1284.059820093, "[2~25]时段1~24.5": 1467.487774521, "[2~25]时段1~24.6": 1720.079056934, "[2~25]时段1~24.7": 1949.549016599, "[2~25]时段1~24.8": 2017.799595873, "[2~25]时段1~24.9": 1624.860453589, "[2~25]时段1~24.10": 1462.465189724, "[2~25]时段1~24.11": 1507.466168053, "[2~25]时段1~24.12": 1621.794933595, "[2~25]时段1~24.13": 1966.603438457, "[2~25]时段1~24.14": 2011.829725927, "[2~25]时段1~24.15": 1964.646373653, "[2~25]时段1~24.16": 2055.85546104, "[2~25]时段1~24.17": 1962.656417005, "[2~25]时段1~24.18": 1638.849355452, "[2~25]时段1~24.19": 1384.301008236, "[2~25]时段1~24.20": 1567.793101762, "[2~25]时段1~24.21": 1544.673423607}, {"[0]地区": "外送", "[1]开始日期": 20300103, "[2]结束日期": 20300104, "[1~25]时段1~24": 1543.597860262, "[1~25]时段1~24.1": 1496.509894339, "[2~25]时段1~24": 1426.366389361, "[2~25]时段1~24.1": 1425.32371786, "[2~25]时段1~24.2": 1332.126318417, "[2~25]时段1~24.3": 1377.257219536, "[2~25]时段1~24.4": 1284.059820093, "[2~25]时段1~24.5": 1467.487774521, "[2~25]时段1~24.6": 1720.079056934, "[2~25]时段1~24.7": 1949.549016599, "[2~25]时段1~24.8": 2017.799595873, "[2~25]时段1~24.9": 1624.860453589, "[2~25]时段1~24.10": 1462.465189724, "[2~25]时段1~24.11": 1507.466168053, "[2~25]时段1~24.12": 1621.794933595, "[2~25]时段1~24.13": 1966.603438457, "[2~25]时段1~24.14": 2011.829725927, "[2~25]时段1~24.15": 1964.646373653, "[2~25]时段1~24.16": 2055.85546104, "[2~25]时段1~24.17": 1962.656417005, "[2~25]时段1~24.18": 1638.849355452, "[2~25]时段1~24.19": 1384.301008236, "[2~25]时段1~24.20": 1567.793101762, "[2~25]时段1~24.21": 1544.673423607}, {"[0]地区": "外送", "[1]开始日期": 20300104, "[2]结束日期": 20300105, "[1~25]时段1~24": 1543.597860262, "[1~25]时段1~24.1": 1496.509894339, "[2~25]时段1~24": 1426.366389361, "[2~25]时段1~24.1": 1425.32371786, "[2~25]时段1~24.2": 1332.126318417, "[2~25]时段1~24.3": 1377.257219536, "[2~25]时段1~24.4": 1284.059820093, "[2~25]时段1~24.5": 1467.487774521, "[2~25]时段1~24.6": 1720.079056934, "[2~25]时段1~24.7": 1949.549016599, "[2~25]时段1~24.8": 2017.799595873, "[2~25]时段1~24.9": 1624.860453589, "[2~25]时段1~24.10": 1462.465189724, "[2~25]时段1~24.11": 1507.466168053, "[2~25]时段1~24.12": 1621.794933595, "[2~25]时段1~24.13": 1966.603438457, "[2~25]时段1~24.14": 2011.829725927, "[2~25]时段1~24.15": 1964.646373653, "[2~25]时段1~24.16": 2055.85546104, "[2~25]时段1~24.17": 1962.656417005, "[2~25]时段1~24.18": 1638.849355452, "[2~25]时段1~24.19": 1384.301008236, "[2~25]时段1~24.20": 1567.793101762, "[2~25]时段1~24.21": 1544.673423607}, {"[0]地区": "外送", "[1]开始日期": 20300105, "[2]结束日期": 20300106, "[1~25]时段1~24": 1543.597860262, "[1~25]时段1~24.1": 1496.509894339, "[2~25]时段1~24": 1426.366389361, "[2~25]时段1~24.1": 1425.32371786, "[2~25]时段1~24.2": 1332.126318417, "[2~25]时段1~24.3": 1377.257219536, "[2~25]时段1~24.4": 1284.059820093, "[2~25]时段1~24.5": 1467.487774521, "[2~25]时段1~24.6": 1720.079056934, "[2~25]时段1~24.7": 1949.549016599, "[2~25]时段1~24.8": 2017.799595873, "[2~25]时段1~24.9": 1624.860453589, "[2~25]时段1~24.10": 1462.465189724, "[2~25]时段1~24.11": 1507.466168053, "[2~25]时段1~24.12": 1621.794933595, "[2~25]时段1~24.13": 1966.603438457, "[2~25]时段1~24.14": 2011.829725927, "[2~25]时段1~24.15": 1964.646373653, "[2~25]时段1~24.16": 2055.85546104, "[2~25]时段1~24.17": 1962.656417005, "[2~25]时段1~24.18": 1638.849355452, "[2~25]时段1~24.19": 1384.301008236, "[2~25]时段1~24.20": 1567.793101762, "[2~25]时段1~24.21": 1544.673423607}], "unique_values": {"[0]地区": {"count": 3, "sample_values": ["外送", "广东", "系统"]}}, "column_analysis": {"[0]地区": {"is_categorical": true, "has_chinese": true}}}}