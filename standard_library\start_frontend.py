#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动前端界面
使用Python内置的HTTP服务器来运行前端界面
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

def start_server():
    """启动HTTP服务器"""
    PORT = 8080
    
    # 切换到frontend目录
    os.chdir('frontend')
    
    # 创建HTTP服务器
    Handler = http.server.SimpleHTTPRequestHandler
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"🚀 前端服务器已启动在 http://localhost:{PORT}")
        print(f"📁 服务目录: {os.getcwd()}")
        print("🌐 正在打开浏览器...")
        
        # 在新线程中打开浏览器
        def open_browser():
            time.sleep(1)  # 等待服务器启动
            webbrowser.open(f'http://localhost:{PORT}/start_simple.html')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    print("=" * 50)
    print("标准库配置系统 - 前端界面")
    print("=" * 50)
    print()
    
    # 检查frontend目录是否存在
    if not os.path.exists('frontend'):
        print("❌ 错误: frontend目录不存在")
        exit(1)
    
    # 检查HTML文件是否存在
    if not os.path.exists('frontend/start_simple.html'):
        print("❌ 错误: start_simple.html文件不存在")
        exit(1)
    
    print("✅ 检查完成，开始启动服务器...")
    print()
    
    start_server() 