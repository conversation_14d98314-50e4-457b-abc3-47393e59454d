# 标准库配置系统 - 完整运行指南

## 🎉 恭喜！系统已成功运行

您的标准库配置系统现在已经完全可用了！

## 📋 当前运行状态

### ✅ 后端系统
- **状态**: 已运行
- **功能**: 配置管理、数据转换、Excel生成
- **演示程序**: `python run_demo.py` ✅ 已成功运行
- **生成文件**: 
  - 配置文件: `configs/power_plant_config.json`
  - Excel文件: `output/HUST_power_plant_config_20250625_101239.xlsx`

### ✅ 前端界面
- **状态**: 已运行
- **地址**: http://localhost:8080
- **界面**: 标准库配置系统Web界面
- **功能**: 可视化配置、字段映射、转换预览

## 🌐 访问前端界面

### 方法1：自动打开（推荐）
前端服务器启动时会自动打开浏览器，如果没有自动打开，请手动访问：

**http://localhost:8080/start_simple.html**

### 方法2：手动启动
如果前端服务器没有运行，请执行：
```bash
cd standard_library
python start_frontend.py
```

## 🎯 前端界面功能

### 1. 配置信息
- 配置ID、名称、描述设置
- 基本信息管理

### 2. 字段映射配置
- 源字段 → 目标字段映射
- 支持直接映射、转换映射、查找映射
- 动态添加/删除映射关系

### 3. 软件模板配置
- 支持HUST、GOPT、PSASP、BPA
- 主表名设置
- 模板管理

### 4. 转换预览
- 实时预览转换结果
- 数据表格展示
- 转换状态提示

### 5. 操作功能
- 保存配置
- 导出Excel

## 🔧 使用流程

### 步骤1：配置设置
1. 在"配置信息"部分设置配置ID、名称和描述
2. 在"字段映射配置"部分添加字段映射关系
3. 在"软件模板配置"部分选择目标软件

### 步骤2：预览转换
1. 点击"预览转换结果"按钮
2. 查看转换后的数据表格
3. 确认映射关系正确

### 步骤3：生成文件
1. 点击"保存配置"保存当前配置
2. 点击"导出Excel"生成目标格式文件
3. 查看`output`目录中的生成文件

## 📁 文件结构

```
standard_library/
├── configs/                    # 配置文件存储
│   └── power_plant_config.json # 示例配置
├── output/                     # 输出文件目录
│   └── HUST_*.xlsx            # 生成的Excel文件
├── frontend/                   # 前端文件
│   ├── start_simple.html      # 前端界面
│   └── README.md              # 前端说明
├── run_demo.py                # 后端演示程序
├── start_frontend.py          # 前端启动脚本
└── 完整运行指南.md            # 本文件
```

## 🎨 界面特色

### 现代化设计
- 清爽的蓝色主题
- 响应式布局
- 直观的操作界面

### 交互功能
- 动态添加/删除配置项
- 实时预览转换结果
- 友好的用户提示

### 功能完整
- 配置管理
- 字段映射
- 软件模板
- 转换预览
- 文件导出

## 🔄 扩展使用

### 添加新的字段映射
1. 点击"添加字段映射"按钮
2. 填写源字段和目标字段
3. 选择映射类型
4. 保存配置

### 添加新的软件模板
1. 点击"添加软件模板"按钮
2. 选择软件名称（HUST/GOPT/PSASP/BPA）
3. 设置主表名
4. 保存配置

### 自定义配置
- 编辑`configs/power_plant_config.json`文件
- 修改字段映射关系
- 添加新的软件模板
- 重新运行演示程序测试

## 🛠️ 故障排除

### 前端无法访问
1. 检查端口8080是否被占用
2. 重新运行`python start_frontend.py`
3. 手动访问http://localhost:8080/start_simple.html

### 配置保存失败
1. 检查`configs`目录权限
2. 确保有足够的磁盘空间
3. 查看控制台错误信息

### Excel生成失败
1. 检查`output`目录权限
2. 确保安装了openpyxl：`pip install openpyxl`
3. 查看日志输出

## 📞 技术支持

### 查看日志
- 后端日志：运行程序时的控制台输出
- 前端日志：浏览器开发者工具

### 常见问题
1. **PowerShell执行策略问题**：已解决
2. **npm依赖安装**：已提供简化版本
3. **端口冲突**：可修改`start_frontend.py`中的端口号

## 🎊 总结

您的标准库配置系统现在已经完全运行！

✅ **后端功能**：配置管理、数据转换、Excel生成  
✅ **前端界面**：可视化配置、实时预览、用户友好  
✅ **系统集成**：前后端分离、模块化设计、易于扩展  

现在您可以：
1. 通过Web界面配置字段映射关系
2. 预览数据转换结果
3. 生成符合HUST、GOPT等软件要求的Excel文件
4. 根据实际需求扩展更多功能

**开始使用您的标准库配置系统吧！** 🚀 