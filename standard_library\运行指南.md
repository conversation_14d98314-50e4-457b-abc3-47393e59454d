# 标准库配置系统运行指南

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- **Python 3.8+**
- **Node.js 14+**
- **npm** 或 **yarn**

### 2. 后端运行

#### 安装Python依赖
```bash
cd standard_library
pip install -r requirements.txt
```

#### 运行演示程序
```bash
python run_demo.py
```

这将：
- 创建示例配置
- 生成示例数据
- 应用字段映射
- 生成HUST格式的Excel文件

#### 查看结果
- 配置文件保存在 `configs/` 目录
- 生成的Excel文件保存在 `output/` 目录

### 3. 前端运行（可选）

#### 安装前端依赖
```bash
cd frontend
npm install
```

#### 启动开发服务器
```bash
npm run serve
```

前端将在 http://localhost:8080 启动

## 📁 目录结构

```
standard_library/
├── core/                    # 核心业务逻辑
│   ├── config_manager.py   # 配置管理器
│   └── converter_manager.py # 转换器管理器
├── models/                  # 数据模型
│   └── standard_config.py  # 标准配置模型
├── configs/                 # 配置文件存储
├── output/                  # 输出文件目录
├── frontend/                # Vue2前端
├── run_demo.py             # 演示程序
├── main.py                 # 主程序（完整版）
└── requirements.txt        # Python依赖
```

## 🔧 功能演示

### 运行演示程序后，您将看到：

1. **示例数据创建**
   ```
   机组名称 电厂名称  装机容量 机组类型  投产年份 所在省份
   机组1  电厂A   600   火电  2020   广东
   机组2  电厂B   300   水电  2019   四川
   ...
   ```

2. **字段映射应用**
   ```
   机组名称 -> 机组编号
   电厂名称 -> 电厂编号
   装机容量 -> 额定容量
   ```

3. **Excel文件生成**
   - 主表：电源明细表
   - 附加表：机组报价
   - 文件位置：`output/HUST_power_plant_config_YYYYMMDD_HHMMSS.xlsx`

## 🎯 核心功能

### 1. 配置管理
- 创建和管理标准配置
- 字段映射配置
- 软件模板管理

### 2. 数据转换
- 支持HUST、GOPT等软件格式
- 字段映射和数据转换
- Excel文件生成

### 3. 用户界面
- Vue2 + Element UI
- 可视化配置界面
- 实时预览功能

## 🔄 工作流程

1. **配置创建** → 定义字段映射和软件模板
2. **数据输入** → 提供源数据
3. **转换处理** → 应用配置进行数据转换
4. **文件生成** → 输出目标格式的Excel文件

## 🛠️ 自定义配置

### 修改字段映射
编辑 `configs/power_plant_config.json` 文件中的 `field_mappings` 部分：

```json
{
  "field_mappings": {
    "源字段名": {
      "target_field": "目标字段名",
      "mapping_type": "direct",
      "transformation": null
    }
  }
}
```

### 添加软件模板
在 `software_templates` 中添加新的软件配置：

```json
{
  "software_templates": {
    "GOPT": {
      "config": {
        "main_sheet": "机组数据",
        "required_columns": ["机组名称", "电厂名称", "装机容量"]
      }
    }
  }
}
```

## 📊 输出文件说明

生成的Excel文件包含：
- **主数据表**：转换后的主要数据
- **附加表**：根据软件模板生成的辅助表
- **格式规范**：符合目标软件的要求

## 🔍 故障排除

### 常见问题

1. **导入错误**
   - 确保已安装所有依赖：`pip install -r requirements.txt`

2. **文件生成失败**
   - 检查输出目录权限
   - 确保有足够的磁盘空间

3. **前端无法启动**
   - 检查Node.js版本：`node --version`
   - 重新安装依赖：`npm install`

### 日志查看
程序运行时会输出详细的日志信息，包括：
- 配置加载状态
- 字段映射过程
- 文件生成结果

## 📈 扩展开发

### 添加新软件支持
1. 在 `converter_manager.py` 中添加新的转换方法
2. 更新 `supported_software` 列表
3. 创建对应的软件模板

### 添加新转换规则
1. 扩展 `conversion_rules` 类型
2. 实现规则执行逻辑
3. 更新配置界面

## 📞 技术支持

如遇到问题，请检查：
1. 环境依赖是否正确安装
2. 配置文件格式是否正确
3. 日志输出中的错误信息

---

**恭喜！** 您已成功运行标准库配置系统。现在可以开始配置您的数据映射关系，生成符合不同软件要求的Excel文件了。 