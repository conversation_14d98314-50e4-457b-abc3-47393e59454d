<!-- eslint-disable -->
<template>
  <div class="hydropower-output-container">
    <!-- 左侧树形导航 -->
    <div class="left-panel">
      <div class="tree-header">
        <span>电站及机组</span>
      </div>
      <el-tree
        :data="treeData"
        :props="defaultProps"
        :highlight-current="true"
        @node-click="handleNodeClick"
        default-expand-all
        node-key="id"
        :expand-on-click-node="false"
        ref="unitTree"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <i :class="data.type === 'station' ? 'el-icon-office-building' : 'el-icon-cpu'" style="margin-right: 5px;"></i>
          <span>{{ node.label }}</span>
        </span>
      </el-tree>
    </div>

    <!-- 右侧内容区 -->
    <div class="right-panel">
      <div v-if="!currentNode">
        <el-empty description="请从左侧选择一个电厂或机组"></el-empty>
      </div>
      <div v-else>
        <!-- 顶部工具栏 -->
        <div class="toolbar">
          <el-form :inline="true" size="small">
            <el-form-item label="电厂名称">
              <el-input v-model="currentNodeName" disabled></el-input>
            </el-form-item>
            <el-form-item label="起始时间">
              <el-date-picker
                v-model="startDate"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyyMMdd">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadOutputData">查询</el-button>
              <el-button type="success" @click="exportData">导出</el-button>
              <el-button type="warning" @click="importData">导入</el-button>
              <input
                ref="fileUpload"
                type="file"
                accept=".xlsx, .xls"
                style="display: none;"
                @change="handleFileImport"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table
            :data="outputData"
            border
            style="width: 100%"
            height="calc(100vh - 200px)"
            :cell-class-name="cellClassName"
          >
            <el-table-column type="index" label="序号" width="60" fixed></el-table-column>
            <el-table-column prop="name" label="电厂名称" width="150" fixed></el-table-column>
            <el-table-column prop="startDate" label="起始时间" width="100" fixed></el-table-column>
            <el-table-column prop="inputTypeA" label="输入类型A" width="120" fixed></el-table-column>
            <el-table-column prop="inputTypeB" label="输入类型B" width="120" fixed></el-table-column>
            
            <!-- 动态生成12个月的列 -->
            <el-table-column
              v-for="month in 12"
              :key="month"
              :label="`${month}月`"
              :prop="`month${month}`"
              width="80"
            >
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row[`month${month}`]"
                  :min="0"
                  :precision="0"
                  :step="1"
                  size="mini"
                  controls-position="right"
                  @change="handleCellChange(scope.row, month)"
                ></el-input-number>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 初始化电站和机组数据
const initialStations = [
  {
    id: 'S001',
    name: '枫树坝',
    type: 'station'
  },
  {
    id: 'S002',
    name: '青溪电站',
    type: 'station'
  }
];

const initialUnits = [
  {
    id: 'U001',
    name: '枫树坝1号机组',
    stationId: 'S001',
    parentId: 'S001',
    type: 'unit',
    isLeaf: true
  },
  {
    id: 'U002',
    name: '枫树坝2号机组',
    stationId: 'S001',
    parentId: 'S001',
    type: 'unit',
    isLeaf: true
  },
  {
    id: 'U003',
    name: '青溪电站1号机组',
    stationId: 'S002',
    parentId: 'S002',
    type: 'unit',
    isLeaf: true
  }
];

// 初始化输出数据
const initialOutputData = {
  'S001': [
    {
      id: 'O001',
      name: '枫树坝',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '平均出力',
      month1: 50,
      month2: 53,
      month3: 44,
      month4: 83,
      month5: 54,
      month6: 75,
      month7: 78,
      month8: 77,
      month9: 36,
      month10: 52,
      month11: 45,
      month12: 51
    },
    {
      id: 'O002',
      name: '枫树坝',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '预测出力',
      month1: 150,
      month2: 136,
      month3: 118,
      month4: 130,
      month5: 126,
      month6: 129,
      month7: 136,
      month8: 149,
      month9: 129,
      month10: 139,
      month11: 139,
      month12: 139
    },
    {
      id: 'O003',
      name: '枫树坝',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '强迫出力',
      month1: 10,
      month2: 34,
      month3: 13,
      month4: 36,
      month5: 46,
      month6: 69,
      month7: 68,
      month8: 59,
      month9: 30,
      month10: 47,
      month11: 33,
      month12: 32
    },
    {
      id: 'O004',
      name: '枫树坝',
      startDate: '20300101',
      inputTypeA: '枯水年',
      inputTypeB: '强迫出力',
      month1: 0,
      month2: 0,
      month3: 0,
      month4: 11,
      month5: 27,
      month6: 24,
      month7: 39,
      month8: 30,
      month9: 11,
      month10: 23,
      month11: 16,
      month12: 13
    },
    {
      id: 'O005',
      name: '枫树坝',
      startDate: '20300101',
      inputTypeA: '枯水年',
      inputTypeB: '平均出力',
      month1: 49,
      month2: 53,
      month3: 58,
      month4: 67,
      month5: 46,
      month6: 44,
      month7: 47,
      month8: 39,
      month9: 41,
      month10: 39,
      month11: 46,
      month12: 43
    }
  ],
  'S002': [
    {
      id: 'O006',
      name: '青溪电站',
      startDate: '20300101',
      inputTypeA: '枯水年',
      inputTypeB: '预测出力',
      month1: 85,
      month2: 74,
      month3: 84,
      month4: 84,
      month5: 103,
      month6: 92,
      month7: 103,
      month8: 103,
      month9: 81,
      month10: 92,
      month11: 97,
      month12: 96
    },
    {
      id: 'O007',
      name: '青溪电站',
      startDate: '20300101',
      inputTypeA: '枯水年',
      inputTypeB: '平均出力',
      month1: 39,
      month2: 42,
      month3: 46,
      month4: 53,
      month5: 37,
      month6: 35,
      month7: 38,
      month8: 31,
      month9: 33,
      month10: 31,
      month11: 36,
      month12: 34
    },
    {
      id: 'O008',
      name: '青溪电站',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '预测出力',
      month1: 119,
      month2: 108,
      month3: 94,
      month4: 104,
      month5: 100,
      month6: 103,
      month7: 109,
      month8: 119,
      month9: 103,
      month10: 111,
      month11: 111,
      month12: 111
    }
  ],
  'U001': [
    {
      id: 'OU001',
      name: '枫树坝1号机组',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '平均出力',
      month1: 25,
      month2: 26,
      month3: 22,
      month4: 41,
      month5: 27,
      month6: 37,
      month7: 39,
      month8: 38,
      month9: 18,
      month10: 26,
      month11: 22,
      month12: 25
    }
  ],
  'U002': [
    {
      id: 'OU002',
      name: '枫树坝2号机组',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '平均出力',
      month1: 25,
      month2: 27,
      month3: 22,
      month4: 42,
      month5: 27,
      month6: 38,
      month7: 39,
      month8: 39,
      month9: 18,
      month10: 26,
      month11: 23,
      month12: 26
    }
  ],
  'U003': [
    {
      id: 'OU003',
      name: '青溪电站1号机组',
      startDate: '20300101',
      inputTypeA: '平水年',
      inputTypeB: '平均出力',
      month1: 39,
      month2: 42,
      month3: 46,
      month4: 53,
      month5: 37,
      month6: 35,
      month7: 38,
      month8: 31,
      month9: 33,
      month10: 31,
      month11: 36,
      month12: 34
    }
  ]
};

// 从localStorage获取数据，如果没有则使用初始数据
let stations = [];
let units = [];
let outputData = {};

try {
  const savedStations = localStorage.getItem('hydropowerStations');
  const savedUnits = localStorage.getItem('hydropowerUnits');
  const savedOutputData = localStorage.getItem('hydropowerOutputData');
  
  stations = savedStations ? JSON.parse(savedStations) : [...initialStations];
  units = savedUnits ? JSON.parse(savedUnits) : [...initialUnits];
  outputData = savedOutputData ? JSON.parse(savedOutputData) : {...initialOutputData};
} catch (e) {
  console.error('Error loading data from localStorage:', e);
  stations = [...initialStations];
  units = [...initialUnits];
  outputData = {...initialOutputData};
}

// 保存数据到localStorage的辅助函数
function saveToStorage() {
  try {
    localStorage.setItem('hydropowerStations', JSON.stringify(stations));
    localStorage.setItem('hydropowerUnits', JSON.stringify(units));
    localStorage.setItem('hydropowerOutputData', JSON.stringify(outputData));
  } catch (e) {
    console.error('Error saving data to localStorage:', e);
  }
}

export default {
  name: 'HydropowerOutput',
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      currentNode: null,
      currentNodeName: '',
      startDate: '20300101',
      outputData: [],
      dataChanged: false
    };
  },
  created() {
    this.initTreeData();
  },
  methods: {
    // 初始化树形结构数据
    initTreeData() {
      // 构建树形结构
      this.treeData = stations.map(station => {
        const stationNode = { ...station };
        stationNode.children = units.filter(unit => unit.stationId === station.id)
          .map(unit => ({
            id: unit.id,
            name: unit.name,
            type: 'unit',
            isLeaf: true,
            parentId: station.id
          }));
        return stationNode;
      });
    },
    
    // 处理树节点点击
    handleNodeClick(data) {
      // 如果数据有变更，提示保存
      if (this.dataChanged) {
        this.$confirm('数据已修改，是否保存?', '提示', {
          confirmButtonText: '保存',
          cancelButtonText: '不保存',
          type: 'warning'
        }).then(() => {
          this.saveCurrentData();
          this.loadNodeData(data);
        }).catch(() => {
          this.loadNodeData(data);
        });
      } else {
        this.loadNodeData(data);
      }
    },
    
    // 加载节点数据
    loadNodeData(data) {
      this.currentNode = data;
      this.currentNodeName = data.name;
      this.loadOutputData();
    },
    
    // 加载输出数据
    loadOutputData() {
      if (!this.currentNode) return;
      
      // 根据当前节点ID和起始时间获取数据
      const nodeData = outputData[this.currentNode.id] || [];
      this.outputData = nodeData.filter(item => item.startDate === this.startDate);
      
      // 如果没有数据，创建默认数据
      if (this.outputData.length === 0) {
        if (this.currentNode.type === 'station') {
          // 电站默认创建三种出力类型
          const waterTypes = ['平水年', '枯水年'];
          const outputTypes = ['平均出力', '预测出力', '强迫出力'];
          
          waterTypes.forEach(waterType => {
            outputTypes.forEach(outputType => {
              this.outputData.push({
                id: `O_${this.currentNode.id}_${waterType}_${outputType}_${this.startDate}`,
                name: this.currentNode.name,
                startDate: this.startDate,
                inputTypeA: waterType,
                inputTypeB: outputType,
                month1: 0, month2: 0, month3: 0, month4: 0,
                month5: 0, month6: 0, month7: 0, month8: 0,
                month9: 0, month10: 0, month11: 0, month12: 0
              });
            });
          });
        } else {
          // 机组默认只创建平均出力
          this.outputData.push({
            id: `O_${this.currentNode.id}_平水年_平均出力_${this.startDate}`,
            name: this.currentNode.name,
            startDate: this.startDate,
            inputTypeA: '平水年',
            inputTypeB: '平均出力',
            month1: 0, month2: 0, month3: 0, month4: 0,
            month5: 0, month6: 0, month7: 0, month8: 0,
            month9: 0, month10: 0, month11: 0, month12: 0
          });
        }
        
        // 保存新创建的数据
        if (!outputData[this.currentNode.id]) {
          outputData[this.currentNode.id] = [];
        }
        outputData[this.currentNode.id] = [
          ...outputData[this.currentNode.id],
          ...this.outputData
        ];
        saveToStorage();
      }
      
      this.dataChanged = false;
    },
    
    // 处理单元格数据变更
    handleCellChange(row, month) {
      this.dataChanged = true;
      
      // 更新数据
      if (outputData[this.currentNode.id]) {
        const index = outputData[this.currentNode.id].findIndex(item => item.id === row.id);
        if (index !== -1) {
          outputData[this.currentNode.id][index][`month${month}`] = row[`month${month}`];
        }
      }
    },
    
    // 保存当前数据
    saveCurrentData() {
      if (this.dataChanged) {
        saveToStorage();
        this.dataChanged = false;
        this.$message.success('数据保存成功');
      }
    },
    
    // 导出数据
    exportData() {
      if (!this.currentNode) {
        this.$message.warning('请先选择一个节点');
        return;
      }
      
      if (this.outputData.length === 0) {
        this.$message.warning('没有可导出的数据');
        return;
      }
      
      // 保存当前数据
      this.saveCurrentData();
      
      // 准备导出数据
      const exportData = this.outputData.map(row => {
        const newRow = {};
        newRow['电厂名称'] = row.name;
        newRow['起始时间'] = row.startDate;
        newRow['输入类型A'] = row.inputTypeA;
        newRow['输入类型B'] = row.inputTypeB;
        
        // 添加12个月的数据
        for (let i = 1; i <= 12; i++) {
          newRow[`${i}月`] = row[`month${i}`];
        }
        
        return newRow;
      });
      
      // 创建工作表
      const worksheet = this.$XLSX.utils.json_to_sheet(exportData);
      const workbook = this.$XLSX.utils.book_new();
      this.$XLSX.utils.book_append_sheet(workbook, worksheet, '水电出力数据');
      
      // 设置列宽
      const colWidths = [
        { wch: 15 }, // 电厂名称
        { wch: 10 }, // 起始时间
        { wch: 10 }, // 输入类型A
        { wch: 10 }, // 输入类型B
        { wch: 8 }, { wch: 8 }, { wch: 8 }, { wch: 8 }, // 1-4月
        { wch: 8 }, { wch: 8 }, { wch: 8 }, { wch: 8 }, // 5-8月
        { wch: 8 }, { wch: 8 }, { wch: 8 }, { wch: 8 }  // 9-12月
      ];
      worksheet['!cols'] = colWidths;
      
      // 导出文件
      const excelBuffer = this.$XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
      this.$saveAs(blob, `${this.currentNode.name}_水电出力数据_${this.startDate}.xlsx`);
      
      this.$message.success('导出成功');
    },
    
    // 导入数据
    importData() {
      if (!this.currentNode) {
        this.$message.warning('请先选择一个节点');
        return;
      }
      
      this.$refs.fileUpload.click();
    },
    
    // 处理文件导入
    handleFileImport(e) {
      const files = e.target.files;
      if (!files || !files.length) {
        return;
      }
      
      const file = files[0];
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target.result;
          const workbook = this.$XLSX.read(data, { type: 'array' });
          
          // 默认读取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 转换为JSON格式
          const jsonData = this.$XLSX.utils.sheet_to_json(worksheet);
          
          if (jsonData && jsonData.length > 0) {
            // 处理导入的数据
            this.processImportData(jsonData);
          } else {
            this.$message.error('导入的Excel文件未包含有效数据！');
          }
        } catch (error) {
          console.error('Excel导入错误:', error);
          this.$message.error('Excel导入失败，请检查文件格式！');
        }
        
        // 清空文件输入，以便于下次导入同一个文件
        this.$refs.fileUpload.value = '';
      };
      
      reader.readAsArrayBuffer(file);
    },
    
    // 处理导入数据
    processImportData(data) {
      try {
        const importedData = data.map(item => {
          const row = {
            id: `O_${this.currentNode.id}_${item['输入类型A'] || '平水年'}_${item['输入类型B'] || '平均出力'}_${this.startDate}`,
            name: item['电厂名称'] || this.currentNode.name,
            startDate: item['起始时间'] || this.startDate,
            inputTypeA: item['输入类型A'] || '平水年',
            inputTypeB: item['输入类型B'] || '平均出力'
          };
          
          // 添加12个月的数据
          for (let i = 1; i <= 12; i++) {
            row[`month${i}`] = parseInt(item[`${i}月`] || 0);
          }
          
          return row;
        });
        
        // 更新数据
        if (!outputData[this.currentNode.id]) {
          outputData[this.currentNode.id] = [];
        }
        
        // 删除当前日期的数据
        outputData[this.currentNode.id] = outputData[this.currentNode.id].filter(
          item => item.startDate !== this.startDate
        );
        
        // 添加导入的数据
        outputData[this.currentNode.id] = [
          ...outputData[this.currentNode.id],
          ...importedData
        ];
        
        // 更新显示的数据
        this.outputData = [...importedData];
        
        // 保存到localStorage
        saveToStorage();
        
        this.$message.success(`成功导入 ${importedData.length} 条数据`);
        this.dataChanged = false;
      } catch (error) {
        console.error('处理导入数据错误:', error);
        this.$message.error('导入数据处理失败！');
      }
    },
    
    // 设置单元格样式
    cellClassName({row, column}) {
      // 根据输入类型设置不同的背景色
      if (column.property && column.property.startsWith('month')) {
        if (row.inputTypeA === '平水年' && row.inputTypeB === '平均出力') {
          return 'cell-avg-output';
        } else if (row.inputTypeA === '平水年' && row.inputTypeB === '预测出力') {
          return 'cell-forecast-output';
        } else if (row.inputTypeA === '平水年' && row.inputTypeB === '强迫出力') {
          return 'cell-forced-output';
        } else if (row.inputTypeA === '枯水年' && row.inputTypeB === '强迫出力') {
          return 'cell-dry-forced-output';
        } else if (row.inputTypeA === '枯水年' && row.inputTypeB === '平均出力') {
          return 'cell-dry-avg-output';
        } else if (row.inputTypeA === '枯水年' && row.inputTypeB === '预测出力') {
          return 'cell-dry-forecast-output';
        }
      }
      return '';
    }
  }
};
</script>

<style scoped>
.hydropower-output-container {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.left-panel {
  width: 250px;
  border-right: 1px solid #e6e6e6;
  padding: 10px;
  overflow-y: auto;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tree-header {
  font-weight: bold;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.toolbar {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e6e6e6;
}

.table-container {
  flex: 1;
  padding: 10px;
  overflow: auto;
}

/* 单元格样式 */
/deep/ .cell-avg-output {
  background-color: #f0f9eb;
}

/deep/ .cell-forecast-output {
  background-color: #fdf6ec;
}

/deep/ .cell-forced-output {
  background-color: #fef0f0;
}

/deep/ .cell-dry-forced-output {
  background-color: #f4f4f5;
}

/deep/ .cell-dry-avg-output {
  background-color: #ecf5ff;
}

/deep/ .cell-dry-forecast-output {
  background-color: #f0f2f5;
}
</style> 