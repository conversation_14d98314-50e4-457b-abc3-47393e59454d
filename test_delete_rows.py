"""
测试删除Excel行功能
"""
import pandas as pd
from openpyxl import load_workbook
import tkinter as tk
from tkinter import filedialog, messagebox

def test_delete_rows():
    """测试删除Excel行功能"""
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    try:
        # 选择要处理的Excel文件
        file_path = filedialog.askopenfilename(
            title="请选择要处理的Excel文件",
            filetypes=[("Excel files", "*.xlsx")]
        )
        
        if not file_path:
            messagebox.showinfo("提示", "未选择文件，程序退出")
            return

        # 加载数据
        df = pd.read_excel(
            file_path,
            sheet_name='水电三段式出力',
            engine='openpyxl',
            na_filter=False
        )
        
        # 获取要删除的水电厂名称
        plants_to_delete = ['青溪电站', '藏东南2030']  # 这里替换为实际要删除的水电厂名称
        
        # 找到要删除的行
        rows_to_delete = df[df['水电厂名称'].isin(plants_to_delete)].index.tolist()
        
        if not rows_to_delete:
            messagebox.showinfo("提示", "未找到要删除的水电厂")
            return
            
        # 加载工作簿
        wb = load_workbook(file_path)
        ws = wb['水电三段式出力']
        
        # 清空要删除的行的内容（保留行）
        for row in rows_to_delete:
            row_num = row + 2  # Excel行号从2开始
            for col in range(1, ws.max_column + 1):
                ws.cell(row=row_num, column=col).value = None
        
        # 保存文件
        wb.save(file_path)
        messagebox.showinfo("成功", f"已清空{len(rows_to_delete)}行的内容")
        
    except Exception as e:
        error_msg = f"处理过程中出现错误：\n{str(e)}"
        messagebox.showerror("错误", error_msg)

if __name__ == "__main__":
    test_delete_rows() 