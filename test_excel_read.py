import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import time
import os

def test_read_excel():
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 选择输入文件
    input_file = filedialog.askopenfilename(
        title="请选择电源明细表Excel文件",
        filetypes=[("Excel files", "*.xlsx")]
    )
    
    if not input_file:
        print("未选择文件")
        return

    try:
        print(f"开始读取文件: {input_file}")
        print("文件大小:", round(os.path.getsize(input_file) / (1024*1024), 2), "MB")
        
        start_time = time.time()
        print("开始读取时间:", time.strftime("%H:%M:%S"))
        
        # 读取Excel文件
        df = pd.read_excel(input_file, sheet_name='Sheet1')
        
        end_time = time.time()
        print("读取完成时间:", time.strftime("%H:%M:%S"))
        print(f"读取耗时: {end_time - start_time:.2f} 秒")
        
        # 显示基本信息
        print("\n文件基本信息:")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print("\n列名:")
        for col in df.columns:
            print(f"- {col}")
            
        # 显示前5行数据
        print("\n前5行数据预览:")
        print(df.head())
        
    except Exception as e:
        print(f"错误: {str(e)}")
        print("错误类型:", type(e).__name__)

if __name__ == "__main__":
    test_read_excel() 