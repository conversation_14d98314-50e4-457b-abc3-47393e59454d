"""
Excel文件验证程序
"""
import tkinter as tk
from tkinter import filedialog, messagebox
from src.core.excel_validator import ExcelValidator
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def validate_excel():
    """验证Excel文件的主函数"""
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    try:
        # 选择要验证的Excel文件
        file_path = filedialog.askopenfilename(
            title="请选择要验证的Excel文件",
            filetypes=[("Excel files", "*.xlsx")]
        )
        
        if not file_path:
            messagebox.showinfo("提示", "未选择文件，程序退出")
            return

        # 显示正在读取文件的提示
        messagebox.showinfo("提示", "正在读取Excel文件，请稍候...")
        
        # 创建验证器实例
        validator = ExcelValidator(file_path)
        
        # 加载数据
        if not validator.load_data():
            messagebox.showerror("错误", "加载数据失败")
            return
        
        # 创建功能选择窗口
        root = tk.Tk()
        root.title("Excel验证工具")
        
        # 设置窗口大小和位置
        window_width = 400
        window_height = 400
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 创建功能按钮
        def run_remove_duplicates():
            if validator.remove_duplicate_machines():
                messagebox.showinfo("成功", "重复机组处理完成")
            else:
                messagebox.showerror("错误", "处理重复机组时出错")
        
        def run_check_hydro():
            if validator.check_hydro_plants():
                messagebox.showinfo("成功", "水电厂检查完成")
            else:
                messagebox.showerror("错误", "检查水电厂时出错")
        
        def run_check_wind():
            if validator.check_wind_farm_names():
                messagebox.showinfo("成功", "风电机组检查完成")
            else:
                messagebox.showerror("错误", "检查风电机组时出错")
        
        def run_check_solar():
            if validator.check_solar_plants():
                messagebox.showinfo("成功", "光伏电站检查完成")
            else:
                messagebox.showerror("错误", "检查光伏电站时出错")
        
        def run_check_offer():
            if validator.check_offer_machines():
                messagebox.showinfo("成功", "机组报价检查完成")
            else:
                messagebox.showerror("错误", "检查机组报价时出错")
        
        def run_add_missing_wind_farms():
            if validator.add_missing_wind_farms():
                messagebox.showinfo("成功", "已补全风电场sheet中的风电机组")
            else:
                messagebox.showerror("错误", "补全风电场时出错")
        
        def run_add_missing_machine_quotes():
            if validator.add_missing_machine_quotes():
                messagebox.showinfo("成功", "已补全机组报价sheet中的机组")
            else:
                messagebox.showerror("错误", "补全机组报价时出错")
        
        # 添加按钮
        tk.Button(
            root,
            text="1. 剔除机组名称重复项",
            command=run_remove_duplicates,
            width=30,
            height=2
        ).pack(pady=10)
        
        tk.Button(
            root,
            text="2. 检查水电厂名称",
            command=run_check_hydro,
            width=30,
            height=2
        ).pack(pady=10)
        
        tk.Button(
            root,
            text="3. 检查风电机组名称",
            command=run_check_wind,
            width=30,
            height=2
        ).pack(pady=10)
        
        tk.Button(
            root,
            text="4. 检查光伏电站名称",
            command=run_check_solar,
            width=30,
            height=2
        ).pack(pady=10)
        
        tk.Button(
            root,
            text="5. 检查机组报价名称",
            command=run_check_offer,
            width=30,
            height=2
        ).pack(pady=10)
        
        tk.Button(
            root,
            text="7. 补全风电场sheet风电机组",
            command=run_add_missing_wind_farms,
            width=30,
            height=2
        ).pack(pady=10)
        
        tk.Button(
            root,
            text="8. 补全机组报价sheet机组",
            command=run_add_missing_machine_quotes,
            width=30,
            height=2
        ).pack(pady=10)
        
        # 运行主循环
        root.mainloop()
        
    except Exception as e:
        error_msg = f"处理过程中出现错误：\n{str(e)}"
        messagebox.showerror("错误", error_msg)
        logger.error(error_msg)

if __name__ == "__main__":
    validate_excel() 