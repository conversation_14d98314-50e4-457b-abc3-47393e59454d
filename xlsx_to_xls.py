import openpyxl
import xlwt
import tkinter as tk
from tkinter import filedialog, messagebox
import os

def xlsx_to_xls():
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="请选择要转换的xlsx文件",
        filetypes=[("Excel xlsx files", "*.xlsx")]
    )
    if not file_path:
        messagebox.showinfo("提示", "未选择文件，程序退出")
        return

    out_path = os.path.splitext(file_path)[0] + ".xls"
    wb_xlsx = openpyxl.load_workbook(file_path, data_only=False)
    wb_xls = xlwt.Workbook()

    for sheet_name in wb_xlsx.sheetnames:
        ws_xlsx = wb_xlsx[sheet_name]
        ws_xls = wb_xls.add_sheet(sheet_name[:31])  # xls sheet名最多31字符

        for row_idx, row in enumerate(ws_xlsx.iter_rows(values_only=False), start=0):
            for col_idx, cell in enumerate(row, start=0):
                value = cell.value
                if cell.data_type == 'f' and cell.value is not None:
                    # 公式
                    ws_xls.write(row_idx, col_idx, f"={cell.value}")
                else:
                    ws_xls.write(row_idx, col_idx, value)

    wb_xls.save(out_path)
    messagebox.showinfo("成功", f"已转换为xls格式：\n{out_path}")

def rename_xlsx_to_xls():
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="请选择要重命名的xlsx文件",
        filetypes=[("Excel xlsx files", "*.xlsx")]
    )
    if not file_path:
        messagebox.showinfo("提示", "未选择文件，程序退出")
        return

    out_path = os.path.splitext(file_path)[0] + ".xls"
    os.rename(file_path, out_path)
    messagebox.showinfo("成功", f"已重命名为xls扩展名：\n{out_path}")

if __name__ == "__main__":
    rename_xlsx_to_xls()