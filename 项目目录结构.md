excel2excel/
├── src/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── excel_reader.py      # Excel文件读取相关
│   │   ├── excel_writer.py      # Excel文件写入相关
│   │   └── data_processor.py    # 数据处理核心逻辑
│   ├── processors/
│   │   ├── __init__.py
│   │   ├── machine_name.py      # 机组名称处理
│   │   ├── node.py             # 节点处理
│   │   ├── power_plant.py      # 电厂处理
│   │   ├── company.py          # 发电公司处理
│   │   ├── date_processor.py   # 日期相关处理
│   │   └── capacity.py         # 机组容量处理
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── config.py           # 配置文件
│   │   ├── logger.py           # 日志处理
│   │   └── validators.py       # 数据验证
│   └── gui/
│       ├── __init__.py
│       └── main_window.py      # GUI界面
├── tests/
│   ├── __init__.py
│   ├── test_excel_reader.py
│   └── test_processors.py
├── requirements.txt
└── main.py                     # 程序入口